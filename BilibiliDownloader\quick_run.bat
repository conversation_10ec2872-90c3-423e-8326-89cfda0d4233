@echo off
chcp 65001 >nul
cd /d "%~dp0"

echo 🐱 快速启动 像素喵网络资源下载器...
echo.

REM 终止可能运行的实例
taskkill /f /im BilibiliDownloader.exe >nul 2>&1

REM 直接运行
echo 正在启动程序...
dotnet run --project BilibiliDownloader.csproj

if errorlevel 1 (
    echo.
    echo 启动失败，尝试完整编译...
    dotnet clean >nul 2>&1
    dotnet restore >nul 2>&1
    dotnet build >nul 2>&1
    dotnet run --project BilibiliDownloader.csproj
)

echo.
pause
