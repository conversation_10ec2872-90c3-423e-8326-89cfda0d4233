using System;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace BilibiliDownloader
{
    /// <summary>
    /// 番剧API测试工具
    /// </summary>
    public class TestBangumiApi
    {
        private static readonly HttpClient _httpClient = new HttpClient();

        static TestBangumiApi()
        {
            _httpClient.DefaultRequestHeaders.Add("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            _httpClient.DefaultRequestHeaders.Add("Referer", "https://www.bilibili.com/");
        }

        public static async Task TestBangumiApiAsync(string ssId)
        {
            try
            {
                Console.WriteLine($"测试番剧API: ss{ssId}");
                
                // 测试不同的API端点
                var apiUrls = new[]
                {
                    $"https://api.bilibili.com/pgc/view/web/season?season_id={ssId}",
                    $"https://api.bilibili.com/pgc/view/web/season?season_id={ssId}&ep_id=",
                    $"https://api.bilibili.com/pgc/web/season/section?season_id={ssId}",
                    $"https://bangumi.bilibili.com/view/web_api/season?season_id={ssId}"
                };

                foreach (var apiUrl in apiUrls)
                {
                    Console.WriteLine($"\n尝试API: {apiUrl}");
                    try
                    {
                        var response = await _httpClient.GetStringAsync(apiUrl);
                        var jsonData = JsonConvert.DeserializeObject<dynamic>(response);
                        
                        Console.WriteLine($"响应代码: {jsonData.code}");
                        Console.WriteLine($"响应消息: {jsonData.message}");
                        
                        if (jsonData.code == 0)
                        {
                            var result = jsonData.result;
                            if (result != null)
                            {
                                Console.WriteLine($"番剧标题: {result.title}");
                                Console.WriteLine($"番剧描述: {result.evaluate}");
                                
                                if (result.episodes != null)
                                {
                                    var episodes = result.episodes as Newtonsoft.Json.Linq.JArray;
                                    Console.WriteLine($"分集数量: {episodes?.Count ?? 0}");
                                    
                                    if (episodes != null && episodes.Count > 0)
                                    {
                                        var firstEp = episodes[0];
                                        Console.WriteLine($"第一集: {firstEp["title"]} (cid: {firstEp["cid"]})");
                                    }
                                }
                                
                                if (result.section != null)
                                {
                                    var sections = result.section as Newtonsoft.Json.Linq.JArray;
                                    Console.WriteLine($"章节数量: {sections?.Count ?? 0}");
                                }
                                
                                Console.WriteLine("✅ 此API可用");
                                break; // 找到可用的API就停止
                            }
                        }
                        else
                        {
                            Console.WriteLine($"❌ API返回错误: {jsonData.message}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ 请求失败: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
            }
        }

        public static async Task Main(string[] args)
        {
            Console.WriteLine("番剧API测试工具");
            Console.WriteLine("================");
            
            if (args.Length > 0)
            {
                await TestBangumiApiAsync(args[0]);
            }
            else
            {
                // 测试示例番剧
                await TestBangumiApiAsync("914669");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
