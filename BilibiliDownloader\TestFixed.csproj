<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <StartupObject>BilibiliDownloader.TestXiaohongshuFixed</StartupObject>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="HtmlAgilityPack" Version="1.11.54" />
    <PackageReference Include="System.Text.Json" Version="8.0.4" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="Core\**\*.cs" />
    <Compile Include="Models\**\*.cs" />
    <Compile Include="Utils\**\*.cs" />
    <Compile Include="TestXiaohongshuFixed.cs" />
  </ItemGroup>

</Project>
