using System;
using System.Threading.Tasks;
using BilibiliDownloader.Core;
using BilibiliDownloader.Core.Platforms;

namespace BilibiliDownloader
{
    /// <summary>
    /// 控制台测试程序
    /// </summary>
    class TestConsole
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("小红书下载器功能测试");
            Console.WriteLine("====================");

            try
            {
                // 测试小红书下载器
                await TestXiaohongshuDownloader();

                // 测试平台管理器
                await TestPlatformManager();

                // 注意：调试类已删除，使用新的小红书下载器
                Console.WriteLine("小红书下载器测试完成");

                Console.WriteLine("\n所有测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中出现错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        public static Task TestXiaohongshuDownloader()
        {
            Console.WriteLine("=== 小红书下载器测试 ===");

            var downloader = new XiaohongshuDownloader();

            // 测试URL识别
            var testUrls = new[]
            {
                "https://www.xiaohongshu.com/explore/64f8a1b2000000001e00c123",
                "https://xhslink.com/abc123",
                "https://www.bilibili.com/video/BV1xx411c7mD", // 非小红书链接
                "https://www.xiaohongshu.com/discovery/item/64f8a1b2000000001e00c123"
            };

            Console.WriteLine("\n--- URL识别测试 ---");
            foreach (var url in testUrls)
            {
                var isSupported = downloader.IsUrlSupported(url);
                Console.WriteLine($"{url} -> {(isSupported ? "✅ 支持" : "❌ 不支持")}");
            }

            Console.WriteLine("\n小红书下载器测试完成");
            return Task.CompletedTask;
        }

        public static Task TestPlatformManager()
        {
            Console.WriteLine("\n=== 平台管理器测试 ===");

            var platformManager = PlatformManager.Instance;
            var platforms = platformManager.GetAllPlatforms();

            Console.WriteLine($"支持的平台数量: {platforms.Count}");
            foreach (var platform in platforms)
            {
                Console.WriteLine($"- {platform.PlatformIcon} {platform.PlatformName}");
            }

            Console.WriteLine("\n平台管理器测试完成");
            return Task.CompletedTask;
        }
    }
}
