# 简单的文字图标创建脚本
Add-Type -AssemblyName System.Drawing

# 创建位图
$bitmap = New-Object System.Drawing.Bitmap(64, 64)
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)

# 设置高质量渲染
$graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
$graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAlias

# 背景
$bgBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 74, 144, 226))
$graphics.FillRectangle($bgBrush, 0, 0, 64, 64)

# 绘制圆角矩形
$rect = New-Object System.Drawing.Rectangle(4, 4, 56, 56)
$whiteBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
$graphics.FillEllipse($whiteBrush, $rect)

# 绘制文字 "喵"
$font = New-Object System.Drawing.Font('SimHei', 24, [System.Drawing.FontStyle]::Bold)
$textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 74, 144, 226))
$text = "喵"
$textSize = $graphics.MeasureString($text, $font)
$textX = (64 - $textSize.Width) / 2
$textY = (64 - $textSize.Height) / 2
$graphics.DrawString($text, $font, $textBrush, $textX, $textY)

# 保存为PNG
$bitmap.Save("icon.png", [System.Drawing.Imaging.ImageFormat]::Png)

# 清理资源
$graphics.Dispose()
$bitmap.Dispose()
$bgBrush.Dispose()
$whiteBrush.Dispose()
$font.Dispose()
$textBrush.Dispose()

Write-Host "Simple icon created: icon.png" -ForegroundColor Green
