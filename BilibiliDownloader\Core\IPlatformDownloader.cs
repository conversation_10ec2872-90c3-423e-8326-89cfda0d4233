using BilibiliDownloader.Models;

namespace BilibiliDownloader.Core
{
    /// <summary>
    /// 平台下载器接口
    /// </summary>
    public interface IPlatformDownloader
    {
        /// <summary>
        /// 平台名称
        /// </summary>
        string PlatformName { get; }

        /// <summary>
        /// 平台图标
        /// </summary>
        string PlatformIcon { get; }

        /// <summary>
        /// 支持的URL模式
        /// </summary>
        string[] SupportedUrlPatterns { get; }

        /// <summary>
        /// 检查URL是否被此平台支持
        /// </summary>
        bool IsUrlSupported(string url);

        /// <summary>
        /// 解析视频信息
        /// </summary>
        Task<VideoInfo?> ParseVideoInfoAsync(string url, string? cookie = null);

        /// <summary>
        /// 获取下载链接
        /// </summary>
        Task<List<DownloadUrl>> GetDownloadUrlsAsync(VideoInfo videoInfo, string quality, string? cookie = null);

        /// <summary>
        /// 获取支持的清晰度列表
        /// </summary>
        Task<List<QualityOption>> GetAvailableQualitiesAsync(VideoInfo videoInfo, string? cookie = null);

        /// <summary>
        /// 是否需要登录
        /// </summary>
        bool RequiresLogin { get; }

        /// <summary>
        /// 验证账号信息
        /// </summary>
        Task<bool> ValidateAccountAsync(string cookie);
    }

    /// <summary>
    /// 清晰度选项
    /// </summary>
    public class QualityOption
    {
        public string Quality { get; set; } = "";
        public string Description { get; set; } = "";
        public int Width { get; set; }
        public int Height { get; set; }
        public string Format { get; set; } = "";
    }

    /// <summary>
    /// 下载链接信息
    /// </summary>
    public class DownloadUrl
    {
        public string Url { get; set; } = "";
        public string Quality { get; set; } = "";
        public string Format { get; set; } = "";
        public long FileSize { get; set; }
        public Dictionary<string, string> Headers { get; set; } = new();
    }
}
