﻿#pragma checksum "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C07050543EC735254329E650B2DAF762B1D050D7"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace BilibiliDownloader.UI {
    
    
    /// <summary>
    /// XiaohongshuCookieHelperWindow
    /// </summary>
    public partial class XiaohongshuCookieHelperWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 33 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAutoHelper;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnManualGuide;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border autoSection;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnOpenBrowser;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnGetCookie;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtAutoStatus;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border manualSection;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCopyExample;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtCookieInput;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnTestCookie;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClearCookie;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnUseCookie;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtCookieStatus;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClose;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/BilibiliDownloader;V2.0.0.0;component/ui/xiaohongshucookiehelperwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.btnAutoHelper = ((System.Windows.Controls.Button)(target));
            
            #line 34 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
            this.btnAutoHelper.Click += new System.Windows.RoutedEventHandler(this.BtnAutoHelper_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.btnManualGuide = ((System.Windows.Controls.Button)(target));
            
            #line 37 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
            this.btnManualGuide.Click += new System.Windows.RoutedEventHandler(this.BtnManualGuide_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.autoSection = ((System.Windows.Controls.Border)(target));
            return;
            case 4:
            this.btnOpenBrowser = ((System.Windows.Controls.Button)(target));
            
            #line 53 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
            this.btnOpenBrowser.Click += new System.Windows.RoutedEventHandler(this.BtnOpenBrowser_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.btnGetCookie = ((System.Windows.Controls.Button)(target));
            
            #line 56 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
            this.btnGetCookie.Click += new System.Windows.RoutedEventHandler(this.BtnGetCookie_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.txtAutoStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.manualSection = ((System.Windows.Controls.Border)(target));
            return;
            case 8:
            this.btnCopyExample = ((System.Windows.Controls.Button)(target));
            
            #line 93 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
            this.btnCopyExample.Click += new System.Windows.RoutedEventHandler(this.BtnCopyExample_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.txtCookieInput = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.btnTestCookie = ((System.Windows.Controls.Button)(target));
            
            #line 111 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
            this.btnTestCookie.Click += new System.Windows.RoutedEventHandler(this.BtnTestCookie_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btnClearCookie = ((System.Windows.Controls.Button)(target));
            
            #line 114 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
            this.btnClearCookie.Click += new System.Windows.RoutedEventHandler(this.BtnClearCookie_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.btnUseCookie = ((System.Windows.Controls.Button)(target));
            
            #line 116 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
            this.btnUseCookie.Click += new System.Windows.RoutedEventHandler(this.BtnUseCookie_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.txtCookieStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.btnClose = ((System.Windows.Controls.Button)(target));
            
            #line 144 "..\..\..\..\UI\XiaohongshuCookieHelperWindow.xaml"
            this.btnClose.Click += new System.Windows.RoutedEventHandler(this.BtnClose_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

