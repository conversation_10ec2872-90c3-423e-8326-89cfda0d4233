using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using BilibiliDownloader.Utils;
using HtmlAgilityPack;
using YamlDotNet.Serialization;

namespace BilibiliDownloader.Core
{
    /// <summary>
    /// 小红书API服务类 - 基于XHS-Downloader移植
    /// </summary>
    public class XiaohongshuApi : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _userAgent;
        private readonly int _timeout;
        private readonly int _maxRetry;

        // URL匹配正则表达式
        private static readonly Regex LinkRegex = new(@"(?:https?://)?www\.xiaohongshu\.com/explore/\S+", RegexOptions.IgnoreCase);
        private static readonly Regex UserRegex = new(@"(?:https?://)?www\.xiaohongshu\.com/user/profile/[a-z0-9]+/\S+", RegexOptions.IgnoreCase);
        private static readonly Regex ShareRegex = new(@"(?:https?://)?www\.xiaohongshu\.com/discovery/item/\S+", RegexOptions.IgnoreCase);
        private static readonly Regex ShortRegex = new(@"(?:https?://)?xhslink\.com/[^\s""<>\\^`{|}，。；！？、【】《》]+", RegexOptions.IgnoreCase);
        private static readonly Regex IdRegex = new(@"(?:explore|item)/(\S+)?\?", RegexOptions.IgnoreCase);
        private static readonly Regex IdUserRegex = new(@"user/profile/[a-z0-9]+/(\S+)?\?", RegexOptions.IgnoreCase);

        public XiaohongshuApi(HttpClient httpClient = null)
        {
            if (httpClient == null)
            {
                // 创建支持自动解压缩的HttpClient
                var handler = new HttpClientHandler()
                {
                    AutomaticDecompression = System.Net.DecompressionMethods.GZip | System.Net.DecompressionMethods.Deflate
                };
                _httpClient = new HttpClient(handler);
            }
            else
            {
                _httpClient = httpClient;
            }

            _userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
            _timeout = 10;
            _maxRetry = 3;
            SetupHttpClient();
        }

        private void SetupHttpClient()
        {
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", _userAgent);
            _httpClient.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate, br");
            _httpClient.DefaultRequestHeaders.Add("Cache-Control", "no-cache");
            _httpClient.DefaultRequestHeaders.Add("Pragma", "no-cache");
            _httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Dest", "document");
            _httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Mode", "navigate");
            _httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Site", "none");
            _httpClient.DefaultRequestHeaders.Add("Sec-Fetch-User", "?1");
            _httpClient.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");
        }

        /// <summary>
        /// 提取小红书链接
        /// </summary>
        public async Task<List<string>> ExtractLinksAsync(string input)
        {
            var urls = new List<string>();
            var parts = input.Split(' ', StringSplitOptions.RemoveEmptyEntries);

            foreach (var part in parts)
            {
                try
                {
                    // 处理短链接
                    if (ShortRegex.IsMatch(part))
                    {
                        var realUrl = await ResolveShortLinkAsync(part);
                        if (!string.IsNullOrEmpty(realUrl))
                        {
                            if (ShareRegex.IsMatch(realUrl) || LinkRegex.IsMatch(realUrl) || UserRegex.IsMatch(realUrl))
                            {
                                urls.Add(realUrl);
                            }
                        }
                    }
                    // 处理完整链接
                    else if (ShareRegex.IsMatch(part) || LinkRegex.IsMatch(part) || UserRegex.IsMatch(part))
                    {
                        urls.Add(part);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance.Warning($"处理链接失败: {part}, 错误: {ex.Message}");
                }
            }

            return urls;
        }

        /// <summary>
        /// 解析短链接
        /// </summary>
        private async Task<string> ResolveShortLinkAsync(string shortUrl)
        {
            try
            {
                if (!shortUrl.StartsWith("http"))
                {
                    shortUrl = "https://" + shortUrl;
                }

                var request = new HttpRequestMessage(HttpMethod.Get, shortUrl);
                request.Headers.Add("User-Agent", _userAgent);

                using var handler = new HttpClientHandler { AllowAutoRedirect = false };
                using var client = new HttpClient(handler);
                
                var response = await client.SendAsync(request);
                
                if (response.StatusCode == System.Net.HttpStatusCode.Redirect ||
                    response.StatusCode == System.Net.HttpStatusCode.MovedPermanently ||
                    response.StatusCode == System.Net.HttpStatusCode.Found)
                {
                    return response.Headers.Location?.ToString() ?? "";
                }

                return "";
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"解析短链接失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 从URL中提取作品ID
        /// </summary>
        public string ExtractNoteId(string url)
        {
            try
            {
                var match = IdRegex.Match(url);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }

                match = IdUserRegex.Match(url);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }

                return "";
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"提取作品ID失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取作品详细信息
        /// </summary>
        public async Task<XiaohongshuNoteDetail?> GetNoteDetailAsync(string noteId, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"开始获取小红书作品详情: {noteId}");

                var url = $"https://www.xiaohongshu.com/explore/{noteId}";
                var html = await RequestUrlAsync(url, cookie);
                
                if (string.IsNullOrEmpty(html))
                {
                    Logger.Instance.Error("获取页面内容失败");
                    return null;
                }

                var noteDetail = ExtractNoteDataFromHtml(html);
                if (noteDetail != null)
                {
                    Logger.Instance.Info("成功提取作品详情");
                    return noteDetail;
                }

                Logger.Instance.Error("提取作品详情失败");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取作品详情失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 请求URL获取HTML内容 - 支持gzip压缩
        /// </summary>
        private async Task<string> RequestUrlAsync(string url, string? cookie = null)
        {
            for (int retry = 0; retry < _maxRetry; retry++)
            {
                try
                {
                    // 为每次请求创建新的HttpClient实例以避免重用问题
                    using var handler = new HttpClientHandler()
                    {
                        AutomaticDecompression = System.Net.DecompressionMethods.GZip | System.Net.DecompressionMethods.Deflate
                    };

                    using var httpClient = new HttpClient(handler);
                    httpClient.Timeout = TimeSpan.FromSeconds(_timeout);

                    var request = new HttpRequestMessage(HttpMethod.Get, url);

                    // 设置请求头
                    request.Headers.Add("User-Agent", _userAgent);
                    request.Headers.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8");
                    request.Headers.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
                    request.Headers.Add("Accept-Encoding", "gzip, deflate, br");
                    request.Headers.Add("Cache-Control", "no-cache");
                    request.Headers.Add("Pragma", "no-cache");

                    if (!string.IsNullOrEmpty(cookie))
                    {
                        request.Headers.Add("Cookie", cookie);
                    }

                    var response = await httpClient.SendAsync(request);
                    response.EnsureSuccessStatusCode();

                    // 正确处理压缩内容
                    var content = await response.Content.ReadAsStringAsync();

                    Logger.Instance.Info($"获取到HTML内容，长度: {content.Length}");
                    Logger.Instance.Info($"HTML开头: {content.Substring(0, Math.Min(200, content.Length))}");

                    // 添加延时避免请求过于频繁
                    await Task.Delay(1000);

                    return content;
                }
                catch (Exception ex)
                {
                    Logger.Instance.Warning($"请求失败 (重试 {retry + 1}/{_maxRetry}): {ex.Message}");
                    if (retry == _maxRetry - 1)
                    {
                        throw;
                    }
                    await Task.Delay(2000 * (retry + 1)); // 递增延时
                }
            }

            return "";
        }

        /// <summary>
        /// 从HTML中提取作品数据 - 完全基于XHS-Downloader的实现
        /// </summary>
        private XiaohongshuNoteDetail? ExtractNoteDataFromHtml(string html)
        {
            try
            {
                Logger.Instance.Info("开始解析HTML页面数据");

                // 使用HtmlAgilityPack查找所有script标签的文本内容
                var doc = new HtmlDocument();
                doc.LoadHtml(html);

                var scriptNodes = doc.DocumentNode.SelectNodes("//script");
                if (scriptNodes == null)
                {
                    Logger.Instance.Error("未找到script节点");
                    return CreateFallbackNoteDetail(html);
                }

                // 查找包含window.__INITIAL_STATE__的script（反向查找，与开源项目一致）
                string targetScript = "";
                var scriptList = scriptNodes.ToList();
                scriptList.Reverse(); // 反向查找

                foreach (var scriptNode in scriptList)
                {
                    var scriptText = scriptNode.InnerText;
                    if (!string.IsNullOrEmpty(scriptText) && scriptText.StartsWith("window.__INITIAL_STATE__"))
                    {
                        targetScript = scriptText;
                        break;
                    }
                }

                if (string.IsNullOrEmpty(targetScript))
                {
                    Logger.Instance.Error("未找到window.__INITIAL_STATE__脚本");
                    return CreateFallbackNoteDetail(html);
                }

                Logger.Instance.Info($"找到目标脚本，长度: {targetScript.Length}");
                Logger.Instance.Info($"脚本开头: {targetScript.Substring(0, Math.Min(100, targetScript.Length))}");

                // 移除window.__INITIAL_STATE__=前缀
                var jsObjectString = targetScript.Substring("window.__INITIAL_STATE__=".Length);

                Logger.Instance.Info($"JavaScript对象字符串长度: {jsObjectString.Length}");
                Logger.Instance.Info($"JS对象开头: {jsObjectString.Substring(0, Math.Min(100, jsObjectString.Length))}");

                // 解析JavaScript对象 - 使用开源项目的方法
                return ConvertAndParseJavaScriptObject(jsObjectString);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"解析HTML数据失败: {ex.Message}");
                return CreateFallbackNoteDetail(html);
            }
        }

        /// <summary>
        /// 查找JSON数据的结束位置
        /// </summary>
        private int FindJsonEnd(string jsonData)
        {
            int braceCount = 0;
            bool inString = false;
            bool escaped = false;

            for (int i = 0; i < jsonData.Length; i++)
            {
                char c = jsonData[i];

                if (escaped)
                {
                    escaped = false;
                    continue;
                }

                if (c == '\\' && inString)
                {
                    escaped = true;
                    continue;
                }

                if (c == '"')
                {
                    inString = !inString;
                    continue;
                }

                if (!inString)
                {
                    if (c == '{')
                    {
                        braceCount++;
                    }
                    else if (c == '}')
                    {
                        braceCount--;
                        if (braceCount == 0)
                        {
                            return i + 1;
                        }
                    }
                }
            }

            // 如果没有找到完整的JSON，尝试查找分号或其他结束标记
            var endMarkers = new[] { ";</script>", ";(function", ";window.", ";document.", ";\n", ";var ", ";function", ";undefined" };
            int minEnd = jsonData.Length;

            foreach (var marker in endMarkers)
            {
                var index = jsonData.IndexOf(marker);
                if (index > 0 && index < minEnd)
                {
                    minEnd = index;
                }
            }

            return minEnd;
        }

        /// <summary>
        /// 转换并解析JavaScript对象 - 完全基于XHS-Downloader的YAML方法实现
        /// </summary>
        private XiaohongshuNoteDetail? ProcessJavaScriptObjectWithYamlApproach(string jsString)
        {
            try
            {
                Logger.Instance.Info("使用YAML方法解析JavaScript对象");
                Logger.Instance.Info($"JS对象长度: {jsString.Length}");
                Logger.Instance.Info($"JS对象开头100字符: {jsString.Substring(0, Math.Min(100, jsString.Length))}");

                // 直接使用JSON解析（JavaScript对象不是标准YAML）
                Logger.Instance.Info("尝试直接JSON解析JavaScript对象");
                return ConvertAndParseJavaScriptObject(jsString);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"YAML方法解析失败: {ex.Message}");
                return ConvertAndParseJavaScriptObject(jsString);
            }
        }

        /// <summary>
        /// 转换并解析JavaScript对象 - JSON方法（备用）
        /// </summary>
        private XiaohongshuNoteDetail? ConvertAndParseJavaScriptObject(string jsString)
        {
            try
            {
                Logger.Instance.Info("使用JSON方法解析JavaScript对象");
                Logger.Instance.Info($"JSON数据长度: {jsString.Length}");
                Logger.Instance.Info($"JSON数据开头100字符: {jsString.Substring(0, Math.Min(100, jsString.Length))}");

                // 首先修复JavaScript对象为JSON格式
                Logger.Instance.Info("开始修复JavaScript对象为JSON");
                var fixedJsonString = FixJavaScriptToJson(jsString);
                Logger.Instance.Info($"修复后JSON长度: {fixedJsonString.Length}");
                Logger.Instance.Info($"修复后JSON开头: {fixedJsonString.Substring(0, Math.Min(200, fixedJsonString.Length))}");

                // 尝试解析完整的JSON
                try
                {
                    Logger.Instance.Info($"修复后JSON长度: {fixedJsonString.Length}");
                    var fullJson = JsonSerializer.Deserialize<JsonElement>(fixedJsonString);
                    Logger.Instance.Info("成功解析完整JSON，开始查找note数据");

                    // 使用与开源项目完全相同的路径查找方法
                    return FilterNoteObject(fullJson);
                }
                catch (JsonException ex)
                {
                    Logger.Instance.Warning($"完整JSON解析失败: {ex.Message}");
                    Logger.Instance.Info($"错误位置: {ex.BytePositionInLine}");

                    // 显示错误位置附近的内容
                    if (ex.BytePositionInLine.HasValue && ex.BytePositionInLine.Value < fixedJsonString.Length)
                    {
                        var errorPos = (int)ex.BytePositionInLine.Value;
                        var start = Math.Max(0, errorPos - 50);
                        var length = Math.Min(100, fixedJsonString.Length - start);
                        var context = fixedJsonString.Substring(start, length);
                        Logger.Instance.Warning($"错误位置附近内容: ...{context}...");
                    }

                    // 如果完整JSON解析失败，回退到正则表达式方法
                    return ExtractNoteUsingRegex(jsString);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"解析JavaScript对象失败: {ex.Message}");
                return CreateFallbackFromString(jsString);
            }
        }

        /// <summary>
        /// 过滤note对象 - 使用与开源项目完全相同的路径
        /// </summary>
        private XiaohongshuNoteDetail? FilterNoteObject(JsonElement data)
        {
            try
            {
                // 首先输出根级别的属性用于调试
                Logger.Instance.Info("根JSON对象属性:");
                if (data.ValueKind == JsonValueKind.Object)
                {
                    foreach (var prop in data.EnumerateObject())
                    {
                        Logger.Instance.Info($"  - {prop.Name}: {prop.Value.ValueKind}");
                    }
                }

                // 使用与开源项目完全相同的路径: note.noteDetailMap.[-1].note
                var keysLink = new[] { "note", "noteDetailMap", "[-1]", "note" };
                Logger.Instance.Info($"尝试使用标准路径: {string.Join(".", keysLink)}");

                var noteData = DeepGet(data, keysLink);

                if (noteData.HasValue)
                {
                    Logger.Instance.Info("使用标准路径找到note数据");
                    Logger.Instance.Info($"Note数据类型: {noteData.Value.ValueKind}");

                    // 详细输出note数据的内容
                    if (noteData.Value.ValueKind == JsonValueKind.Object)
                    {
                        var properties = new List<string>();
                        foreach (var property in noteData.Value.EnumerateObject())
                        {
                            properties.Add($"{property.Name}({property.Value.ValueKind})");
                        }
                        Logger.Instance.Info($"Note数据包含的属性: {string.Join(", ", properties)}");

                        // 如果note对象为空，让我们检查noteDetailMap的结构
                        if (properties.Count == 0)
                        {
                            Logger.Instance.Warning("Note对象为空，检查noteDetailMap结构");

                            // 回到noteDetailMap层级查看
                            var noteDetailMapPath = new[] { "note", "noteDetailMap" };
                            var noteDetailMapResult = DeepGet(data, noteDetailMapPath);

                            if (noteDetailMapResult.HasValue && noteDetailMapResult.Value.ValueKind == JsonValueKind.Object)
                            {
                                Logger.Instance.Info("noteDetailMap结构:");
                                foreach (var prop in noteDetailMapResult.Value.EnumerateObject())
                                {
                                    Logger.Instance.Info($"  {prop.Name}: {prop.Value.ValueKind}");

                                    // 如果是对象，进一步查看其属性
                                    if (prop.Value.ValueKind == JsonValueKind.Object)
                                    {
                                        foreach (var subProp in prop.Value.EnumerateObject())
                                        {
                                            Logger.Instance.Info($"    {prop.Name}.{subProp.Name}: {subProp.Value.ValueKind}");
                                        }
                                    }
                                }
                            }
                        }
                    }

                    return ParseNoteDetail(noteData.Value);
                }

                Logger.Instance.Warning("标准路径未找到note数据，尝试检查noteData结构");

                // 检查是否是noteData结构
                if (data.TryGetProperty("noteData", out var noteDataElement))
                {
                    Logger.Instance.Info("发现noteData结构，尝试提取数据");

                    // 尝试路径: noteData.data.noteData
                    var noteDataPath = new[] { "noteData", "data", "noteData" };
                    var noteDataResult = DeepGet(data, noteDataPath);

                    if (noteDataResult.HasValue)
                    {
                        Logger.Instance.Info("从noteData结构找到note数据");
                        return ParseNoteDetail(noteDataResult.Value);
                    }
                }

                Logger.Instance.Warning("所有路径都未找到note数据，尝试其他方法");
                return SearchForNoteDataInJson(data);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"过滤note对象失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 深度获取JSON属性 - 与开源项目完全相同的实现
        /// </summary>
        private JsonElement? DeepGet(JsonElement data, string[] keys)
        {
            try
            {
                JsonElement current = data;
                Logger.Instance.Info($"开始深度获取，初始数据类型: {current.ValueKind}");

                for (int i = 0; i < keys.Length; i++)
                {
                    var key = keys[i];
                    Logger.Instance.Info($"处理键 [{i}]: {key}, 当前数据类型: {current.ValueKind}");

                    if (key.StartsWith("[") && key.EndsWith("]"))
                    {
                        // 处理数组索引
                        var indexStr = key.Substring(1, key.Length - 2);
                        if (int.TryParse(indexStr, out var index))
                        {
                            Logger.Instance.Info($"处理数组索引: {index}");
                            current = SafeGetByIndex(current, index);
                            Logger.Instance.Info($"索引后数据类型: {current.ValueKind}");
                        }
                        else
                        {
                            Logger.Instance.Warning($"无效的数组索引: {key}");
                            return null;
                        }
                    }
                    else
                    {
                        // 处理对象属性
                        Logger.Instance.Info($"查找属性: {key}");
                        if (!current.TryGetProperty(key, out current))
                        {
                            Logger.Instance.Warning($"未找到属性: {key}");
                            return null;
                        }
                        Logger.Instance.Info($"找到属性 {key}，数据类型: {current.ValueKind}");
                    }
                }

                Logger.Instance.Info($"深度获取完成，最终数据类型: {current.ValueKind}");
                return current;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"深度获取属性失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 安全获取数组或对象的指定索引元素
        /// </summary>
        private JsonElement SafeGetByIndex(JsonElement data, int index)
        {
            try
            {
                if (data.ValueKind == JsonValueKind.Object)
                {
                    // 如果是对象，获取第index个值
                    var values = data.EnumerateObject().ToList();
                    Logger.Instance.Info($"对象包含 {values.Count} 个属性");

                    if (index == -1)
                    {
                        index = values.Count - 1; // -1表示最后一个
                        Logger.Instance.Info($"使用最后一个索引: {index}");
                    }

                    if (index >= 0 && index < values.Count)
                    {
                        var selectedProperty = values[index];
                        Logger.Instance.Info($"选择属性: {selectedProperty.Name}, 类型: {selectedProperty.Value.ValueKind}");
                        return selectedProperty.Value;
                    }
                    else
                    {
                        Logger.Instance.Warning($"对象索引 {index} 超出范围 (0-{values.Count - 1})");
                    }
                }
                else if (data.ValueKind == JsonValueKind.Array)
                {
                    // 如果是数组，获取第index个元素
                    var array = data.EnumerateArray().ToList();
                    Logger.Instance.Info($"数组包含 {array.Count} 个元素");

                    if (index == -1)
                    {
                        index = array.Count - 1; // -1表示最后一个
                        Logger.Instance.Info($"使用最后一个索引: {index}");
                    }

                    if (index >= 0 && index < array.Count)
                    {
                        Logger.Instance.Info($"选择数组元素 {index}");
                        return array[index];
                    }
                    else
                    {
                        Logger.Instance.Warning($"数组索引 {index} 超出范围 (0-{array.Count - 1})");
                    }
                }
                else
                {
                    Logger.Instance.Warning($"数据类型 {data.ValueKind} 不支持索引操作");
                }

                throw new IndexOutOfRangeException($"索引 {index} 超出范围");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"安全获取索引失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 在JSON中搜索note数据
        /// </summary>
        private XiaohongshuNoteDetail? SearchForNoteDataInJson(JsonElement json)
        {
            try
            {
                Logger.Instance.Info("开始递归搜索note数据");

                // 首先检查根级别的属性
                if (json.ValueKind == JsonValueKind.Object)
                {
                    Logger.Instance.Info("根JSON对象属性:");
                    foreach (var prop in json.EnumerateObject())
                    {
                        Logger.Instance.Info($"  - {prop.Name}: {prop.Value.ValueKind}");
                    }
                }

                // 递归搜索所有可能包含note数据的路径
                return SearchForNoteRecursive(json, "", 0);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"递归搜索note数据失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 递归搜索note数据
        /// </summary>
        private XiaohongshuNoteDetail? SearchForNoteRecursive(JsonElement element, string path, int depth)
        {
            try
            {
                // 限制递归深度避免无限递归
                if (depth > 5)
                {
                    return null;
                }

                if (element.ValueKind == JsonValueKind.Object)
                {
                    foreach (var property in element.EnumerateObject())
                    {
                        var currentPath = string.IsNullOrEmpty(path) ? property.Name : $"{path}.{property.Name}";

                        // 记录重要的路径
                        if (depth <= 2)
                        {
                            Logger.Instance.Info($"搜索路径 {currentPath}: {property.Value.ValueKind}");
                        }

                        // 检查是否是note对象（包含title, desc等字段）
                        if (property.Value.ValueKind == JsonValueKind.Object)
                        {
                            var hasTitle = property.Value.TryGetProperty("title", out _);
                            var hasDesc = property.Value.TryGetProperty("desc", out _);
                            var hasNoteId = property.Value.TryGetProperty("noteId", out _);
                            var hasImageList = property.Value.TryGetProperty("imageList", out _);
                            var hasVideoUrl = property.Value.TryGetProperty("video", out _);

                            if (hasTitle || hasDesc || hasNoteId || hasImageList || hasVideoUrl)
                            {
                                Logger.Instance.Info($"在路径 {currentPath} 找到可能的note数据 (title:{hasTitle}, desc:{hasDesc}, noteId:{hasNoteId}, imageList:{hasImageList}, video:{hasVideoUrl})");
                                var noteResult = ParseNoteDetail(property.Value);
                                if (noteResult != null)
                                {
                                    return noteResult;
                                }
                            }
                        }

                        // 递归搜索子对象
                        var recursiveResult = SearchForNoteRecursive(property.Value, currentPath, depth + 1);
                        if (recursiveResult != null)
                        {
                            return recursiveResult;
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"递归搜索路径 {path} 失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 使用正则表达式提取note数据（备用方法）
        /// </summary>
        private XiaohongshuNoteDetail? ExtractNoteUsingRegex(string jsString)
        {
            try
            {
                Logger.Instance.Info("使用正则表达式备用方法");

                // 方法1: 查找noteDetailMap中的完整note对象
                var notePattern = @"""noteDetailMap"":\s*\{\s*""[^""]+"":\s*\{\s*""note"":\s*(\{(?:[^{}]|{[^{}]*})*\})";
                var match = Regex.Match(jsString, notePattern);

                if (!match.Success)
                {
                    Logger.Instance.Info("正则方法1失败，尝试方法2");
                    // 方法2: 直接查找包含title的note对象
                    notePattern = @"""note"":\s*(\{[^}]*""title""[^}]*\})";
                    match = Regex.Match(jsString, notePattern);
                }

                if (!match.Success)
                {
                    Logger.Instance.Info("正则方法2失败，尝试方法3");
                    // 方法3: 查找任何包含基本字段的note对象
                    notePattern = @"""note"":\s*(\{[^}]*""noteId""[^}]*\})";
                    match = Regex.Match(jsString, notePattern);
                }

                if (!match.Success)
                {
                    Logger.Instance.Error("所有正则表达式方法都失败了");
                    return CreateFallbackFromString(jsString);
                }

                var noteJsonString = match.Groups[1].Value;
                Logger.Instance.Info($"提取到note JSON长度: {noteJsonString.Length}");
                Logger.Instance.Info($"note JSON开头: {noteJsonString.Substring(0, Math.Min(200, noteJsonString.Length))}");

                // 修复JavaScript对象为标准JSON
                noteJsonString = FixJavaScriptToJson(noteJsonString);

                // 解析note数据
                var noteElement = JsonSerializer.Deserialize<JsonElement>(noteJsonString);
                return ParseNoteDetail(noteElement);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"正则表达式提取失败: {ex.Message}");
                return CreateFallbackFromString(jsString);
            }
        }

        /// <summary>
        /// 修复JavaScript对象为标准JSON - 基于开源项目的实现
        /// </summary>
        private string FixJavaScriptToJson(string jsString)
        {
            try
            {
                Logger.Instance.Info("开始修复JavaScript对象为JSON");

                // 1. 替换undefined为null（更精确的匹配）
                jsString = Regex.Replace(jsString, @"\bundefined\b", "null");

                // 2. 处理其他JavaScript特有的值
                jsString = Regex.Replace(jsString, @"\bNaN\b", "null");
                jsString = Regex.Replace(jsString, @"\bInfinity\b", "null");
                jsString = Regex.Replace(jsString, @"\b-Infinity\b", "null");

                // 3. 替换单引号字符串为双引号
                jsString = Regex.Replace(jsString, @"'([^'\\]*(\\.[^'\\]*)*)'", "\"$1\"");

                // 4. 为没有引号的属性名添加引号
                jsString = Regex.Replace(jsString, @"([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:", "$1\"$2\":");

                // 5. 处理尾随逗号
                jsString = Regex.Replace(jsString, @",(\s*[}\]])", "$1");

                // 6. 处理函数调用等JavaScript特有语法
                jsString = Regex.Replace(jsString, @":\s*function\s*\([^)]*\)\s*\{[^}]*\}", ":null");

                // 7. 处理new Date()等构造函数调用
                jsString = Regex.Replace(jsString, @":\s*new\s+\w+\s*\([^)]*\)", ":null");

                // 8. 处理正则表达式字面量
                jsString = Regex.Replace(jsString, @":\s*/[^/\n]+/[gimuy]*", ":null");

                Logger.Instance.Info($"修复后JSON长度: {jsString.Length}");
                Logger.Instance.Info($"修复后JSON开头: {jsString.Substring(0, Math.Min(200, jsString.Length))}");

                return jsString;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"修复JavaScript对象失败: {ex.Message}");
                return jsString;
            }
        }

        /// <summary>
        /// 从字符串创建备用的note详情
        /// </summary>
        private XiaohongshuNoteDetail CreateFallbackFromString(string jsString)
        {
            try
            {
                Logger.Instance.Info("创建备用note详情");

                // 尝试提取基本信息
                var titleMatch = Regex.Match(jsString, @"""title"":\s*""([^""]*)""");
                var descMatch = Regex.Match(jsString, @"""desc"":\s*""([^""]*)""");
                var noteIdMatch = Regex.Match(jsString, @"""noteId"":\s*""([^""]*)""");

                return new XiaohongshuNoteDetail
                {
                    NoteId = noteIdMatch.Success ? noteIdMatch.Groups[1].Value : "unknown",
                    Title = titleMatch.Success ? titleMatch.Groups[1].Value : "未知标题",
                    Description = descMatch.Success ? descMatch.Groups[1].Value : "未知描述",
                    ImageUrls = new List<string>(),
                    VideoUrls = new List<XiaohongshuVideoUrl>(),
                    User = new XiaohongshuUser
                    {
                        UserId = "unknown",
                        Nickname = "未知用户"
                    }
                };
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"创建备用note详情失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 修复JSON数据中的常见问题
        /// </summary>
        private string FixJsonData(string jsonData)
        {
            try
            {
                // 修复undefined值
                jsonData = Regex.Replace(jsonData, @":undefined\b", ":null");

                // 修复单引号
                jsonData = Regex.Replace(jsonData, @"'([^']*)':", "\"$1\":");

                // 修复尾随逗号
                jsonData = Regex.Replace(jsonData, @",(\s*[}\]])", "$1");

                // 修复JavaScript注释
                jsonData = Regex.Replace(jsonData, @"//.*$", "", RegexOptions.Multiline);
                jsonData = Regex.Replace(jsonData, @"/\*.*?\*/", "", RegexOptions.Singleline);

                Logger.Instance.Info("已修复JSON数据中的常见问题");
                return jsonData;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"修复JSON数据失败: {ex.Message}");
                return jsonData;
            }
        }

        /// <summary>
        /// 清理JSON数据，移除可能导致解析失败的内容
        /// </summary>
        private string CleanJsonData(string jsonData)
        {
            try
            {
                // 移除可能的BOM标记
                if (jsonData.StartsWith("\uFEFF"))
                {
                    jsonData = jsonData.Substring(1);
                }

                // 移除开头和结尾的空白字符
                jsonData = jsonData.Trim();

                // 确保JSON以{开头
                if (!jsonData.StartsWith("{"))
                {
                    // 查找第一个{
                    var firstBrace = jsonData.IndexOf('{');
                    if (firstBrace > 0)
                    {
                        jsonData = jsonData.Substring(firstBrace);
                        Logger.Instance.Info($"移除了JSON开头的 {firstBrace} 个字符");
                    }
                    else if (firstBrace < 0)
                    {
                        // 如果没有找到{，可能JSON数据被截断了，尝试添加开头的{
                        if (jsonData.StartsWith("\""))
                        {
                            jsonData = "{" + jsonData;
                            Logger.Instance.Info("为JSON数据添加了开头的大括号");
                        }
                    }
                }

                // 查找并移除可能的JavaScript代码
                var patterns = new[]
                {
                    @";undefined\b",
                    @";window\.",
                    @";document\.",
                    @";\s*\(function",
                    @";\s*function\s+",
                    @";\s*var\s+",
                    @";\s*let\s+",
                    @";\s*const\s+"
                };

                foreach (var pattern in patterns)
                {
                    var match = Regex.Match(jsonData, pattern);
                    if (match.Success)
                    {
                        jsonData = jsonData.Substring(0, match.Index);
                        Logger.Instance.Info($"移除了JavaScript代码，使用模式: {pattern}");
                        break;
                    }
                }

                // 确保JSON以}结尾
                if (!jsonData.EndsWith("}"))
                {
                    var lastBrace = jsonData.LastIndexOf('}');
                    if (lastBrace > 0)
                    {
                        jsonData = jsonData.Substring(0, lastBrace + 1);
                        Logger.Instance.Info("截断JSON到最后一个大括号");
                    }
                }

                return jsonData;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"清理JSON数据失败: {ex.Message}");
                return jsonData;
            }
        }

        /// <summary>
        /// 创建备用的作品详情（当无法解析JSON时）
        /// </summary>
        private XiaohongshuNoteDetail? CreateFallbackNoteDetail(string html)
        {
            try
            {
                Logger.Instance.Info("尝试使用备用方法解析页面");

                var doc = new HtmlDocument();
                doc.LoadHtml(html);

                // 尝试从页面标题获取信息
                var titleNode = doc.DocumentNode.SelectSingleNode("//title");
                var title = titleNode?.InnerText?.Trim() ?? "未知标题";

                // 尝试从meta标签获取描述
                var descNode = doc.DocumentNode.SelectSingleNode("//meta[@name='description']");
                var description = descNode?.GetAttributeValue("content", "") ?? "";

                // 创建基本的作品信息
                var noteDetail = new XiaohongshuNoteDetail
                {
                    Title = title,
                    Description = description,
                    Type = "unknown",
                    CreateTime = DateTime.Now,
                    LastUpdateTime = DateTime.Now,
                    User = new XiaohongshuUser
                    {
                        Nickname = "未知用户",
                        UserId = ""
                    }
                };

                Logger.Instance.Info("使用备用方法创建了基本的作品信息");
                return noteDetail;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"备用解析方法也失败了: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 解析作品详情数据
        /// </summary>
        private XiaohongshuNoteDetail ParseNoteDetail(JsonElement noteData)
        {
            try
            {
                var noteDetail = new XiaohongshuNoteDetail();

                // 输出所有可用的属性名用于调试
                Logger.Instance.Info("Note数据包含的属性:");
                foreach (var property in noteData.EnumerateObject())
                {
                    Logger.Instance.Info($"  - {property.Name}: {property.Value.ValueKind}");
                }

                // 基本信息
                if (noteData.TryGetProperty("noteId", out var noteIdElement))
                {
                    noteDetail.NoteId = noteIdElement.GetString() ?? "";
                    Logger.Instance.Info($"找到noteId: {noteDetail.NoteId}");
                }

                if (noteData.TryGetProperty("title", out var titleElement))
                {
                    noteDetail.Title = titleElement.GetString() ?? "";
                    Logger.Instance.Info($"找到title: {noteDetail.Title}");
                }

                if (noteData.TryGetProperty("desc", out var descElement))
                {
                    noteDetail.Description = descElement.GetString() ?? "";
                    Logger.Instance.Info($"找到desc: {noteDetail.Description}");
                }

                if (noteData.TryGetProperty("type", out var typeElement))
                {
                    noteDetail.Type = typeElement.GetString() ?? "";
                    Logger.Instance.Info($"找到type: {noteDetail.Type}");
                }

                // 时间信息
                if (noteData.TryGetProperty("time", out var timeElement))
                {
                    var timestamp = timeElement.GetInt64();
                    noteDetail.CreateTime = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime;
                }

                if (noteData.TryGetProperty("lastUpdateTime", out var updateTimeElement))
                {
                    var timestamp = updateTimeElement.GetInt64();
                    noteDetail.LastUpdateTime = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime;
                }

                // 用户信息
                if (noteData.TryGetProperty("user", out var userElement))
                {
                    noteDetail.User = new XiaohongshuUser();

                    if (userElement.TryGetProperty("userId", out var userIdElement))
                        noteDetail.User.UserId = userIdElement.GetString() ?? "";

                    if (userElement.TryGetProperty("nickname", out var nicknameElement))
                        noteDetail.User.Nickname = nicknameElement.GetString() ?? "";
                }

                // 互动信息
                if (noteData.TryGetProperty("interactInfo", out var interactElement))
                {
                    if (interactElement.TryGetProperty("likedCount", out var likedElement))
                        noteDetail.LikeCount = likedElement.GetString() ?? "0";

                    if (interactElement.TryGetProperty("collectedCount", out var collectedElement))
                        noteDetail.CollectCount = collectedElement.GetString() ?? "0";

                    if (interactElement.TryGetProperty("commentCount", out var commentElement))
                        noteDetail.CommentCount = commentElement.GetString() ?? "0";

                    if (interactElement.TryGetProperty("shareCount", out var shareElement))
                        noteDetail.ShareCount = shareElement.GetString() ?? "0";
                }

                // 标签信息
                if (noteData.TryGetProperty("tagList", out var tagListElement) && tagListElement.ValueKind == JsonValueKind.Array)
                {
                    noteDetail.Tags = new List<string>();
                    foreach (var tag in tagListElement.EnumerateArray())
                    {
                        if (tag.TryGetProperty("name", out var tagNameElement))
                        {
                            var tagName = tagNameElement.GetString();
                            if (!string.IsNullOrEmpty(tagName))
                                noteDetail.Tags.Add(tagName);
                        }
                    }
                }

                // 处理媒体内容
                Logger.Instance.Info($"处理媒体内容，类型: {noteDetail.Type}");

                if (noteDetail.Type == "video")
                {
                    Logger.Instance.Info("开始提取视频URL");
                    // 视频内容
                    ExtractVideoUrls(noteData, noteDetail);
                }
                else if (noteDetail.Type == "normal")
                {
                    Logger.Instance.Info("开始提取图片URL");
                    // 图文内容
                    ExtractImageUrls(noteData, noteDetail);
                }
                else
                {
                    Logger.Instance.Warning($"未知的媒体类型: {noteDetail.Type}，尝试同时提取视频和图片");
                    // 尝试同时提取视频和图片
                    ExtractVideoUrls(noteData, noteDetail);
                    ExtractImageUrls(noteData, noteDetail);
                }

                return noteDetail;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"解析作品详情失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 提取视频URL
        /// </summary>
        private void ExtractVideoUrls(JsonElement noteData, XiaohongshuNoteDetail noteDetail)
        {
            try
            {
                noteDetail.VideoUrls = new List<XiaohongshuVideoUrl>();
                Logger.Instance.Info("开始提取视频URL");

                // 检查是否有video属性
                if (!noteData.TryGetProperty("video", out var videoElement))
                {
                    Logger.Instance.Warning("未找到video属性");
                    return;
                }

                Logger.Instance.Info("找到video属性");

                // 方法1: 从video.stream.h264数组中获取视频URL
                if (videoElement.TryGetProperty("stream", out var streamElement))
                {
                    Logger.Instance.Info("找到stream属性");

                    if (streamElement.TryGetProperty("h264", out var h264Element))
                    {
                        Logger.Instance.Info($"找到h264属性，类型: {h264Element.ValueKind}");

                        if (h264Element.ValueKind == JsonValueKind.Array)
                        {
                            Logger.Instance.Info($"h264是数组，包含 {h264Element.GetArrayLength()} 个元素");

                            foreach (var videoStream in h264Element.EnumerateArray())
                            {
                                Logger.Instance.Info("处理h264数组中的视频流");

                                if (videoStream.TryGetProperty("masterUrl", out var masterUrlElement))
                                {
                                    var masterUrl = masterUrlElement.GetString();
                                    Logger.Instance.Info($"找到masterUrl: {masterUrl}");

                                    if (!string.IsNullOrEmpty(masterUrl))
                                    {
                                        var width = videoStream.TryGetProperty("width", out var widthElement) ? widthElement.GetInt32() : 0;
                                        var height = videoStream.TryGetProperty("height", out var heightElement) ? heightElement.GetInt32() : 0;
                                        var size = videoStream.TryGetProperty("size", out var sizeElement) ? sizeElement.GetInt64() : 0;
                                        var qualityType = videoStream.TryGetProperty("qualityType", out var qualityElement) ? qualityElement.GetString() : "HD";

                                        noteDetail.VideoUrls.Add(new XiaohongshuVideoUrl
                                        {
                                            Url = masterUrl,
                                            Quality = qualityType ?? "HD",
                                            Width = width,
                                            Height = height,
                                            Size = size
                                        });

                                        Logger.Instance.Info($"添加视频URL: {masterUrl}, 质量: {qualityType}, 尺寸: {width}x{height}");
                                    }
                                }
                                else
                                {
                                    Logger.Instance.Warning("视频流中未找到masterUrl");
                                }
                            }
                        }
                        else
                        {
                            Logger.Instance.Warning("h264不是数组类型");
                        }
                    }
                    else
                    {
                        Logger.Instance.Warning("未找到h264属性");
                    }
                }
                else
                {
                    Logger.Instance.Warning("未找到stream属性");
                }

                // 方法2: 如果方法1失败，尝试从originVideoKey构造URL
                if (noteDetail.VideoUrls.Count == 0 &&
                    noteData.TryGetProperty("video", out videoElement) &&
                    videoElement.TryGetProperty("consumer", out var consumerElement) &&
                    consumerElement.TryGetProperty("originVideoKey", out var videoKeyElement))
                {
                    var videoKey = videoKeyElement.GetString();
                    if (!string.IsNullOrEmpty(videoKey))
                    {
                        noteDetail.VideoUrls.Add(new XiaohongshuVideoUrl
                        {
                            Url = $"https://sns-video-bd.xhscdn.com/{videoKey}",
                            Quality = "原画",
                            Width = 0,
                            Height = 0,
                            Size = 0
                        });
                    }
                }

                Logger.Instance.Info($"提取到 {noteDetail.VideoUrls.Count} 个视频URL");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"提取视频URL失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 提取图片URL
        /// </summary>
        private void ExtractImageUrls(JsonElement noteData, XiaohongshuNoteDetail noteDetail)
        {
            try
            {
                noteDetail.ImageUrls = new List<string>();

                if (noteData.TryGetProperty("imageList", out var imageListElement) &&
                    imageListElement.ValueKind == JsonValueKind.Array)
                {
                    foreach (var image in imageListElement.EnumerateArray())
                    {
                        if (image.TryGetProperty("urlDefault", out var urlElement))
                        {
                            var imageUrl = urlElement.GetString();
                            if (!string.IsNullOrEmpty(imageUrl))
                            {
                                // 直接使用原始URL，或者尝试提取token生成高质量URL
                                var token = ExtractImageToken(imageUrl);
                                if (!string.IsNullOrEmpty(token))
                                {
                                    var highQualityUrl = $"https://ci.xiaohongshu.com/{token}?imageView2/format/png";
                                    noteDetail.ImageUrls.Add(highQualityUrl);
                                }
                                else
                                {
                                    // 如果token提取失败，直接使用原始URL
                                    noteDetail.ImageUrls.Add(imageUrl);
                                }
                            }
                        }
                    }
                }

                Logger.Instance.Info($"提取到 {noteDetail.ImageUrls.Count} 个图片URL");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"提取图片URL失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从图片URL中提取token
        /// </summary>
        private string ExtractImageToken(string imageUrl)
        {
            try
            {
                var uri = new Uri(imageUrl);
                var pathParts = uri.AbsolutePath.Split('/');
                if (pathParts.Length >= 6)
                {
                    var tokenParts = string.Join("/", pathParts.Skip(5));
                    var exclamationIndex = tokenParts.IndexOf('!');
                    if (exclamationIndex > 0)
                    {
                        tokenParts = tokenParts.Substring(0, exclamationIndex);
                    }
                    return tokenParts;
                }
                return "";
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"提取图片token失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 从YAML数据中深度获取属性 - 基于开源项目的deep_get实现
        /// </summary>
        private object? DeepGetFromYamlData(Dictionary<string, object> data, string[] keys)
        {
            try
            {
                object current = data;
                Logger.Instance.Info($"开始YAML深度获取，键路径: {string.Join(".", keys)}");

                foreach (var key in keys)
                {
                    Logger.Instance.Info($"处理键: {key}, 当前数据类型: {current?.GetType().Name}");

                    if (key.StartsWith("[") && key.EndsWith("]"))
                    {
                        // 处理数组索引
                        var indexStr = key.Substring(1, key.Length - 2);
                        if (int.TryParse(indexStr, out var index))
                        {
                            current = SafeGetFromYamlByIndex(current, index);
                        }
                        else
                        {
                            Logger.Instance.Warning($"无效的数组索引: {key}");
                            return null;
                        }
                    }
                    else
                    {
                        // 处理对象属性
                        if (current is Dictionary<string, object> dict)
                        {
                            if (!dict.TryGetValue(key, out current))
                            {
                                Logger.Instance.Warning($"未找到属性: {key}");
                                return null;
                            }
                        }
                        else
                        {
                            Logger.Instance.Warning($"当前数据不是字典类型，无法获取属性: {key}");
                            return null;
                        }
                    }

                    Logger.Instance.Info($"找到键 {key}，数据类型: {current?.GetType().Name}");
                }

                Logger.Instance.Info("YAML深度获取完成");
                return current;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"YAML深度获取失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从YAML数据中安全获取索引元素 - 基于开源项目的safe_get实现
        /// </summary>
        private object? SafeGetFromYamlByIndex(object data, int index)
        {
            try
            {
                if (data is Dictionary<string, object> dict)
                {
                    // 如果是字典，获取第index个值
                    var values = dict.Values.ToList();
                    Logger.Instance.Info($"字典包含 {values.Count} 个值");

                    if (index == -1)
                    {
                        index = values.Count - 1; // -1表示最后一个
                        Logger.Instance.Info($"使用最后一个索引: {index}");
                    }

                    if (index >= 0 && index < values.Count)
                    {
                        Logger.Instance.Info($"选择字典值 {index}");
                        return values[index];
                    }
                    else
                    {
                        Logger.Instance.Warning($"字典索引 {index} 超出范围 (0-{values.Count - 1})");
                    }
                }
                else if (data is List<object> list)
                {
                    // 如果是列表，获取第index个元素
                    Logger.Instance.Info($"列表包含 {list.Count} 个元素");

                    if (index == -1)
                    {
                        index = list.Count - 1; // -1表示最后一个
                        Logger.Instance.Info($"使用最后一个索引: {index}");
                    }

                    if (index >= 0 && index < list.Count)
                    {
                        Logger.Instance.Info($"选择列表元素 {index}");
                        return list[index];
                    }
                    else
                    {
                        Logger.Instance.Warning($"列表索引 {index} 超出范围 (0-{list.Count - 1})");
                    }
                }
                else
                {
                    Logger.Instance.Warning($"数据类型 {data?.GetType().Name} 不支持索引操作");
                }

                return null;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"YAML安全获取索引失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从YAML数据解析note详情 - 基于开源项目的数据结构
        /// </summary>
        private XiaohongshuNoteDetail? ParseNoteFromYamlData(object noteData)
        {
            try
            {
                if (!(noteData is Dictionary<string, object> noteDict))
                {
                    Logger.Instance.Warning("note数据不是字典类型");
                    return null;
                }

                Logger.Instance.Info("开始从YAML数据解析note详情");
                Logger.Instance.Info($"Note数据包含的属性: {string.Join(", ", noteDict.Keys)}");

                var detail = new XiaohongshuNoteDetail();

                // 基本信息
                detail.NoteId = GetStringFromYamlDict(noteDict, "noteId") ?? "";
                detail.Title = GetStringFromYamlDict(noteDict, "title") ?? "";
                detail.Description = GetStringFromYamlDict(noteDict, "desc") ?? "";
                detail.Type = GetStringFromYamlDict(noteDict, "type") ?? "";

                // 作者信息
                if (noteDict.TryGetValue("user", out var userObj) && userObj is Dictionary<string, object> userDict)
                {
                    detail.User = new XiaohongshuUser
                    {
                        Nickname = GetStringFromYamlDict(userDict, "nickname") ?? "",
                        UserId = GetStringFromYamlDict(userDict, "userId") ?? ""
                    };
                }

                // 时间信息
                if (noteDict.TryGetValue("time", out var timeObj) && timeObj != null)
                {
                    if (long.TryParse(timeObj.ToString(), out var timestamp))
                    {
                        detail.CreateTime = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime;
                    }
                }

                // 媒体内容
                Logger.Instance.Info($"处理媒体内容，类型: {detail.Type}");

                if (detail.Type == "video")
                {
                    // 视频内容
                    ExtractVideoFromYamlData(noteDict, detail);
                }
                else
                {
                    // 图片内容
                    ExtractImagesFromYamlData(noteDict, detail);
                }

                Logger.Instance.Info($"成功解析作品: {detail.Title}");
                return detail;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"从YAML数据解析note失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从YAML字典中获取字符串值
        /// </summary>
        private string? GetStringFromYamlDict(Dictionary<string, object> dict, string key)
        {
            return dict.TryGetValue(key, out var value) ? value?.ToString() : null;
        }

        /// <summary>
        /// 从YAML数据提取视频信息 - 基于开源项目的video.py实现
        /// </summary>
        private void ExtractVideoFromYamlData(Dictionary<string, object> noteDict, XiaohongshuNoteDetail detail)
        {
            try
            {
                Logger.Instance.Info("开始提取视频URL");

                // 按照开源项目的路径: video.consumer.originVideoKey
                if (noteDict.TryGetValue("video", out var videoObj) &&
                    videoObj is Dictionary<string, object> videoDict &&
                    videoDict.TryGetValue("consumer", out var consumerObj) &&
                    consumerObj is Dictionary<string, object> consumerDict &&
                    consumerDict.TryGetValue("originVideoKey", out var videoKeyObj))
                {
                    var videoKey = videoKeyObj?.ToString();
                    if (!string.IsNullOrEmpty(videoKey))
                    {
                        var videoUrl = $"https://sns-video-bd.xhscdn.com/{videoKey}";
                        detail.VideoUrls.Add(new XiaohongshuVideoUrl
                        {
                            Url = videoUrl,
                            Quality = "原画"
                        });
                        Logger.Instance.Info($"提取到视频URL: {videoUrl}");
                    }
                }
                else
                {
                    Logger.Instance.Warning("未找到video.consumer.originVideoKey属性");
                }

                Logger.Instance.Info($"提取到 {detail.VideoUrls.Count} 个视频URL");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"提取视频URL失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从YAML数据提取图片信息 - 基于开源项目的image.py实现
        /// </summary>
        private void ExtractImagesFromYamlData(Dictionary<string, object> noteDict, XiaohongshuNoteDetail detail)
        {
            try
            {
                Logger.Instance.Info("开始提取图片URL");

                if (noteDict.TryGetValue("imageList", out var imageListObj) &&
                    imageListObj is List<object> imageList)
                {
                    Logger.Instance.Info($"找到 {imageList.Count} 个图片项");

                    foreach (var imageItem in imageList)
                    {
                        if (imageItem is Dictionary<string, object> imageDict)
                        {
                            // 提取图片URL - 基于开源项目的逻辑
                            if (imageDict.TryGetValue("urlDefault", out var urlDefaultObj))
                            {
                                var urlDefault = urlDefaultObj?.ToString();
                                if (!string.IsNullOrEmpty(urlDefault))
                                {
                                    // 提取token并生成高质量图片URL
                                    var token = ExtractImageTokenFromYamlUrl(urlDefault);
                                    if (!string.IsNullOrEmpty(token))
                                    {
                                        // 使用PNG格式的高质量图片URL
                                        var imageUrl = $"https://ci.xiaohongshu.com/{token}?imageView2/format/png";
                                        detail.ImageUrls.Add(imageUrl);
                                        Logger.Instance.Info($"提取到图片URL: {imageUrl}");
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    Logger.Instance.Warning("未找到imageList属性");
                }

                Logger.Instance.Info($"提取到 {detail.ImageUrls.Count} 个图片URL");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"提取图片URL失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从YAML URL中提取图片token - 基于开源项目的实现
        /// </summary>
        private string ExtractImageTokenFromYamlUrl(string url)
        {
            try
            {
                // 基于开源项目的逻辑: "/".join(url.split("/")[5:]).split("!")[0]
                var parts = url.Split('/');
                if (parts.Length > 5)
                {
                    var tokenPart = string.Join("/", parts.Skip(5));
                    var token = tokenPart.Split('!')[0];
                    Logger.Instance.Info($"从URL {url} 提取到token: {token}");
                    return token;
                }

                Logger.Instance.Warning($"无法从URL提取token: {url}");
                return "";
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"提取图片token失败: {ex.Message}");
                return "";
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// 小红书作品详情数据模型
    /// </summary>
    public class XiaohongshuNoteDetail
    {
        public string NoteId { get; set; } = "";
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Type { get; set; } = ""; // video, normal
        public DateTime CreateTime { get; set; }
        public DateTime LastUpdateTime { get; set; }
        public string CoverUrl { get; set; } = "";
        public int Duration { get; set; } = 0;
        public long ViewCount { get; set; } = 0;

        // 互动数据
        public string LikeCount { get; set; } = "0";
        public string CollectCount { get; set; } = "0";
        public string CommentCount { get; set; } = "0";
        public string ShareCount { get; set; } = "0";

        // 用户信息
        public XiaohongshuUser? User { get; set; }

        // 标签
        public List<string> Tags { get; set; } = new();

        // 媒体内容
        public List<string> ImageUrls { get; set; } = new();
        public List<XiaohongshuVideoUrl> VideoUrls { get; set; } = new();
    }

    /// <summary>
    /// 小红书用户信息
    /// </summary>
    public class XiaohongshuUser
    {
        public string UserId { get; set; } = "";
        public string Nickname { get; set; } = "";
    }

    /// <summary>
    /// 小红书视频URL信息
    /// </summary>
    public class XiaohongshuVideoUrl
    {
        public string Url { get; set; } = "";
        public string Quality { get; set; } = "";
        public int Width { get; set; } = 0;
        public int Height { get; set; } = 0;
        public long Size { get; set; } = 0;
    }
}
