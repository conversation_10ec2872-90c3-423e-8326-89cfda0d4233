using System;
using System.Globalization;
using System.Windows.Data;

namespace BilibiliDownloader
{
    public class ProgressToWidthConverter : IMultiValueConverter
    {
        public static readonly ProgressToWidthConverter Instance = new ProgressToWidthConverter();

        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length != 2 || values[0] == null || values[1] == null)
                return 0.0;

            if (values[0] is double progress && values[1] is double totalWidth)
            {
                // 确保进度在0-100范围内
                progress = Math.Max(0, Math.Min(100, progress));
                
                // 计算进度条宽度，减去边框宽度
                var progressWidth = (progress / 100.0) * (totalWidth - 2); // 减去左右边框各1px
                return Math.Max(0, progressWidth);
            }

            return 0.0;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
