using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Utils
{
    /// <summary>
    /// 小红书工具类 - 基于XHS-Downloader移植
    /// </summary>
    public static class XiaohongshuUtils
    {
        // URL匹配正则表达式
        private static readonly Regex LinkRegex = new(@"(?:https?://)?www\.xiaohongshu\.com/explore/\S+", RegexOptions.IgnoreCase);
        private static readonly Regex UserRegex = new(@"(?:https?://)?www\.xiaohongshu\.com/user/profile/[a-z0-9]+/\S+", RegexOptions.IgnoreCase);
        private static readonly Regex ShareRegex = new(@"(?:https?://)?www\.xiaohongshu\.com/discovery/item/\S+", RegexOptions.IgnoreCase);
        private static readonly Regex ShortRegex = new(@"(?:https?://)?xhslink\.com/[^\s""<>\\^`{|}，。；！？、【】《》]+", RegexOptions.IgnoreCase);

        // 文件名非法字符
        private static readonly char[] InvalidFileNameChars = Path.GetInvalidFileNameChars()
            .Concat(new[] { '<', '>', ':', '"', '|', '?', '*', '\\', '/' })
            .ToArray();

        /// <summary>
        /// 检查是否为小红书URL
        /// </summary>
        public static bool IsXiaohongshuUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return false;

            return LinkRegex.IsMatch(url) || 
                   UserRegex.IsMatch(url) || 
                   ShareRegex.IsMatch(url) || 
                   ShortRegex.IsMatch(url);
        }

        /// <summary>
        /// 从URL中提取作品ID
        /// </summary>
        public static string ExtractNoteId(string url)
        {
            if (string.IsNullOrEmpty(url))
                return string.Empty;

            try
            {
                // 处理短链接
                if (url.Contains("xhslink.com"))
                {
                    var match = Regex.Match(url, @"xhslink\.com/([a-zA-Z0-9]+)");
                    if (match.Success)
                    {
                        return match.Groups[1].Value;
                    }
                }

                // 处理完整链接
                var patterns = new[]
                {
                    @"explore/([a-zA-Z0-9]+)",
                    @"discovery/item/([a-zA-Z0-9]+)",
                    @"user/profile/[a-zA-Z0-9]+/([a-zA-Z0-9]+)"
                };

                foreach (var pattern in patterns)
                {
                    var match = Regex.Match(url, pattern);
                    if (match.Success)
                    {
                        return match.Groups[1].Value;
                    }
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"提取小红书作品ID失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 生成安全的文件名
        /// </summary>
        public static string GenerateSafeFileName(string title, string author, string noteId, string extension = "")
        {
            try
            {
                // 清理标题
                var safeTitle = CleanFileName(title ?? "无标题");
                var safeAuthor = CleanFileName(author ?? "未知作者");

                // 构建文件名：作者_标题_ID
                var fileName = $"{safeAuthor}_{safeTitle}_{noteId}";

                // 限制文件名长度
                if (fileName.Length > 100)
                {
                    fileName = fileName.Substring(0, 100);
                }

                // 添加扩展名
                if (!string.IsNullOrEmpty(extension))
                {
                    if (!extension.StartsWith("."))
                        extension = "." + extension;
                    fileName += extension;
                }

                return fileName;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"生成文件名失败: {ex.Message}");
                return $"xiaohongshu_{noteId}{extension}";
            }
        }

        /// <summary>
        /// 清理文件名中的非法字符
        /// </summary>
        public static string CleanFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return "未命名";

            try
            {
                // 移除非法字符
                var cleanName = fileName;
                foreach (var invalidChar in InvalidFileNameChars)
                {
                    cleanName = cleanName.Replace(invalidChar, '_');
                }

                // 移除多余的空格和下划线
                cleanName = Regex.Replace(cleanName, @"\s+", " ").Trim();
                cleanName = Regex.Replace(cleanName, @"_+", "_").Trim('_');

                // 确保不为空
                if (string.IsNullOrWhiteSpace(cleanName))
                    cleanName = "未命名";

                return cleanName;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"清理文件名失败: {ex.Message}");
                return "未命名";
            }
        }

        /// <summary>
        /// 格式化内容类型
        /// </summary>
        public static string FormatContentType(string contentType)
        {
            return contentType?.ToLower() switch
            {
                "video" => "视频",
                "normal" => "图文",
                _ => "未知"
            };
        }

        /// <summary>
        /// 格式化数量显示
        /// </summary>
        public static string FormatCount(string count)
        {
            if (string.IsNullOrEmpty(count) || count == "-1")
                return "0";

            if (int.TryParse(count, out var num))
            {
                return num switch
                {
                    >= 10000 => $"{num / 10000.0:F1}万",
                    >= 1000 => $"{num / 1000.0:F1}k",
                    _ => num.ToString()
                };
            }

            return count;
        }

        /// <summary>
        /// 获取请求头配置
        /// </summary>
        public static Dictionary<string, string> GetRequestHeaders()
        {
            return new Dictionary<string, string>
            {
                ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                ["Accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
                ["Accept-Language"] = "zh-CN,zh;q=0.9,en;q=0.8",
                ["Accept-Encoding"] = "gzip, deflate, br",
                ["Cache-Control"] = "no-cache",
                ["Pragma"] = "no-cache",
                ["Sec-Fetch-Dest"] = "document",
                ["Sec-Fetch-Mode"] = "navigate",
                ["Sec-Fetch-Site"] = "none",
                ["Sec-Fetch-User"] = "?1",
                ["Upgrade-Insecure-Requests"] = "1"
            };
        }

        /// <summary>
        /// 获取下载请求头配置
        /// </summary>
        public static Dictionary<string, string> GetDownloadHeaders()
        {
            return new Dictionary<string, string>
            {
                ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                ["Referer"] = "https://www.xiaohongshu.com/",
                ["Accept"] = "*/*",
                ["Accept-Language"] = "zh-CN,zh;q=0.9,en;q=0.8",
                ["Accept-Encoding"] = "gzip, deflate, br",
                ["Cache-Control"] = "no-cache",
                ["Pragma"] = "no-cache"
            };
        }

        /// <summary>
        /// 生成作品文件夹名称
        /// </summary>
        public static string GenerateFolderName(string title, string author, string noteId)
        {
            try
            {
                var safeTitle = CleanFileName(title ?? "无标题");
                var safeAuthor = CleanFileName(author ?? "未知作者");

                var folderName = $"[{safeAuthor}] {safeTitle}";

                // 限制文件夹名长度
                if (folderName.Length > 80)
                {
                    folderName = folderName.Substring(0, 80);
                }

                // 添加ID确保唯一性
                folderName += $"_{noteId}";

                return folderName;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"生成文件夹名失败: {ex.Message}");
                return $"xiaohongshu_{noteId}";
            }
        }

        /// <summary>
        /// 验证作品ID格式
        /// </summary>
        public static bool IsValidNoteId(string noteId)
        {
            if (string.IsNullOrEmpty(noteId))
                return false;

            // 小红书作品ID通常是24位的十六进制字符串
            return Regex.IsMatch(noteId, @"^[a-fA-F0-9]{24}$");
        }

        /// <summary>
        /// 获取文件扩展名
        /// </summary>
        public static string GetFileExtension(string contentType, string format = "")
        {
            if (!string.IsNullOrEmpty(format))
            {
                return format.ToLower() switch
                {
                    "png" => ".png",
                    "jpg" or "jpeg" => ".jpg",
                    "webp" => ".webp",
                    "mp4" => ".mp4",
                    _ => ".jpg"
                };
            }

            return contentType?.ToLower() switch
            {
                "video" => ".mp4",
                "normal" => ".png",
                _ => ".jpg"
            };
        }

        /// <summary>
        /// 格式化时间显示
        /// </summary>
        public static string FormatTime(DateTime dateTime)
        {
            try
            {
                return dateTime.ToString("yyyy-MM-dd HH:mm:ss");
            }
            catch
            {
                return "未知时间";
            }
        }

        /// <summary>
        /// 生成唯一文件名（避免重复）
        /// </summary>
        public static string GenerateUniqueFileName(string baseName, string directory, string extension)
        {
            try
            {
                var fileName = baseName + extension;
                var fullPath = Path.Combine(directory, fileName);
                
                if (!File.Exists(fullPath))
                    return fileName;

                var counter = 1;
                do
                {
                    fileName = $"{baseName}_{counter}{extension}";
                    fullPath = Path.Combine(directory, fileName);
                    counter++;
                } while (File.Exists(fullPath) && counter < 1000);

                return fileName;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"生成唯一文件名失败: {ex.Message}");
                return $"{baseName}_{DateTime.Now:yyyyMMddHHmmss}{extension}";
            }
        }
    }
}
