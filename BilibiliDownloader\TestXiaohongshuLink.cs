using System;
using System.Threading.Tasks;
using BilibiliDownloader.Core.Platforms;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader
{
    /// <summary>
    /// 测试指定的小红书链接
    /// </summary>
    class TestXiaohongshuLink
    {
        public static async Task TestLink()
        {
            Console.WriteLine("小红书链接测试");
            Console.WriteLine("====================");
            
            // 指定的测试链接
            var testUrl = "https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed";
            
            try
            {
                Console.WriteLine($"测试链接: {testUrl}");
                Console.WriteLine();
                
                // 创建小红书下载器
                using var downloader = new XiaohongshuDownloader();
                
                // 1. 测试URL识别
                Console.WriteLine("1. 测试URL识别...");
                var isSupported = downloader.IsUrlSupported(testUrl);
                Console.WriteLine($"   URL是否支持: {(isSupported ? "✅ 是" : "❌ 否")}");
                
                if (!isSupported)
                {
                    Console.WriteLine("   链接不被支持，测试结束。");
                    return;
                }
                
                // 2. 测试解析视频信息
                Console.WriteLine("\n2. 测试解析视频信息...");
                var videoInfo = await downloader.ParseVideoInfoAsync(testUrl);
                
                if (videoInfo == null)
                {
                    Console.WriteLine("   ❌ 解析失败");
                    return;
                }
                
                Console.WriteLine("   ✅ 解析成功");
                Console.WriteLine($"   作品ID: {videoInfo.VideoId}");
                Console.WriteLine($"   标题: {videoInfo.Title}");
                Console.WriteLine($"   作者: {videoInfo.Author}");
                Console.WriteLine($"   作者ID: {videoInfo.AuthorId}");
                Console.WriteLine($"   发布时间: {videoInfo.PublishTime}");
                Console.WriteLine($"   内容类型: {videoInfo.ContentType}");
                Console.WriteLine($"   点赞数: {videoInfo.LikeCount}");
                Console.WriteLine($"   收藏数: {videoInfo.CollectCount}");
                Console.WriteLine($"   评论数: {videoInfo.CommentCount}");
                Console.WriteLine($"   分享数: {videoInfo.ShareCount}");
                
                if (videoInfo.ImageUrls.Count > 0)
                {
                    Console.WriteLine($"   图片数量: {videoInfo.ImageUrls.Count}");
                    for (int i = 0; i < Math.Min(3, videoInfo.ImageUrls.Count); i++)
                    {
                        Console.WriteLine($"   图片{i + 1}: {videoInfo.ImageUrls[i]}");
                    }
                    if (videoInfo.ImageUrls.Count > 3)
                    {
                        Console.WriteLine($"   ... 还有 {videoInfo.ImageUrls.Count - 3} 张图片");
                    }
                }
                
                if (videoInfo.Tags.Count > 0)
                {
                    Console.WriteLine($"   标签: {string.Join(", ", videoInfo.Tags)}");
                }
                
                // 3. 测试获取可用质量选项
                Console.WriteLine("\n3. 测试获取可用质量选项...");
                var qualities = await downloader.GetAvailableQualitiesAsync(videoInfo);
                
                if (qualities.Count > 0)
                {
                    Console.WriteLine("   ✅ 获取成功");
                    foreach (var quality in qualities)
                    {
                        Console.WriteLine($"   - {quality.Quality}: {quality.Description} ({quality.Format})");
                    }
                }
                else
                {
                    Console.WriteLine("   ❌ 未获取到质量选项");
                }
                
                // 4. 测试获取下载链接
                Console.WriteLine("\n4. 测试获取下载链接...");
                var downloadUrls = await downloader.GetDownloadUrlsAsync(videoInfo, "原图");
                
                if (downloadUrls.Count > 0)
                {
                    Console.WriteLine($"   ✅ 获取成功，共 {downloadUrls.Count} 个下载链接");
                    for (int i = 0; i < Math.Min(3, downloadUrls.Count); i++)
                    {
                        var url = downloadUrls[i];
                        Console.WriteLine($"   链接{i + 1}: {url.Quality} - {url.Format}");
                        Console.WriteLine($"           URL: {url.Url}");
                    }
                    if (downloadUrls.Count > 3)
                    {
                        Console.WriteLine($"   ... 还有 {downloadUrls.Count - 3} 个下载链接");
                    }
                }
                else
                {
                    Console.WriteLine("   ❌ 未获取到下载链接");
                }
                
                Console.WriteLine("\n✅ 所有测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ 测试过程中出现错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
