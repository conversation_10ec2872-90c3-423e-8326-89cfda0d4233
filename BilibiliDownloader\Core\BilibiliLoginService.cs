using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Security.Cryptography;
using Newtonsoft.Json;
using BilibiliDownloader.Models;

namespace BilibiliDownloader.Core
{
    public class BilibiliLoginService
    {
        private readonly HttpClient _httpClient;
        
        public BilibiliLoginService()
        {
            _httpClient = new HttpClient();
            SetupHttpClient();
        }
        
        private void SetupHttpClient()
        {
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json, text/plain, */*");
            _httpClient.DefaultRequestHeaders.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Origin", "https://www.bilibili.com");
            _httpClient.DefaultRequestHeaders.Add("Referer", "https://www.bilibili.com/");
        }
        
        public async Task<LoginResult> LoginWithPasswordAsync(string username, string password)
        {
            try
            {
                // 第一步：获取登录密钥
                var keyInfo = await GetLoginKeyAsync();
                if (keyInfo == null)
                {
                    return new LoginResult { Success = false, Message = "获取登录密钥失败" };
                }
                
                // 第二步：加密密码
                var encryptedPassword = EncryptPassword(password, keyInfo.PublicKey);
                
                // 第三步：执行登录
                var loginData = new Dictionary<string, string>
                {
                    {"username", username},
                    {"password", encryptedPassword},
                    {"keep", "true"},
                    {"key", keyInfo.Hash},
                    {"captcha", ""},
                    {"challenge", ""},
                    {"validate", ""},
                    {"seccode", ""}
                };
                
                var loginResponse = await PostLoginAsync(loginData);
                return ParseLoginResponse(loginResponse);
            }
            catch (Exception ex)
            {
                return new LoginResult 
                { 
                    Success = false, 
                    Message = $"登录过程中发生错误: {ex.Message}" 
                };
            }
        }
        
        public async Task<LoginResult> ValidateCookieAsync(string cookie)
        {
            try
            {
                // 使用Cookie验证登录状态
                _httpClient.DefaultRequestHeaders.Remove("Cookie");
                _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
                
                var response = await _httpClient.GetStringAsync("https://api.bilibili.com/x/web-interface/nav");
                var data = JsonConvert.DeserializeObject<dynamic>(response);
                
                if (data.code == 0 && data.data.isLogin == true)
                {
                    return new LoginResult
                    {
                        Success = true,
                        Message = "Cookie验证成功",
                        Cookie = cookie,
                        UserInfo = new UserInfo
                        {
                            UserId = data.data.mid?.ToString(),
                            Username = data.data.uname?.ToString(),
                            Avatar = data.data.face?.ToString(),
                            IsVip = Convert.ToBoolean(data.data.vipStatus ?? 0)
                        }
                    };
                }
                else
                {
                    return new LoginResult
                    {
                        Success = false,
                        Message = "Cookie已失效或无效"
                    };
                }
            }
            catch (Exception ex)
            {
                return new LoginResult
                {
                    Success = false,
                    Message = $"Cookie验证失败: {ex.Message}"
                };
            }
        }
        
        private async Task<LoginKeyInfo> GetLoginKeyAsync()
        {
            try
            {
                var response = await _httpClient.GetStringAsync("https://passport.bilibili.com/api/oauth2/getKey");
                var data = JsonConvert.DeserializeObject<dynamic>(response);
                
                if (data.code == 0)
                {
                    return new LoginKeyInfo
                    {
                        Hash = data.data.hash,
                        PublicKey = data.data.key
                    };
                }
                
                return null;
            }
            catch
            {
                return null;
            }
        }
        
        private string EncryptPassword(string password, string publicKey)
        {
            try
            {
                // 这里应该使用RSA加密，但为了简化，我们先返回原密码
                // 在实际应用中，需要实现RSA加密
                return Convert.ToBase64String(Encoding.UTF8.GetBytes(password));
            }
            catch
            {
                return password;
            }
        }
        
        private async Task<string> PostLoginAsync(Dictionary<string, string> loginData)
        {
            var content = new FormUrlEncodedContent(loginData);
            var response = await _httpClient.PostAsync("https://passport.bilibili.com/api/v2/oauth2/login", content);
            return await response.Content.ReadAsStringAsync();
        }
        
        private LoginResult ParseLoginResponse(string response)
        {
            try
            {
                var data = JsonConvert.DeserializeObject<dynamic>(response);
                
                if (data.code == 0)
                {
                    // 登录成功，提取Cookie
                    var cookies = ExtractCookiesFromResponse(response);
                    
                    return new LoginResult
                    {
                        Success = true,
                        Message = "登录成功",
                        Cookie = cookies
                    };
                }
                else
                {
                    return new LoginResult
                    {
                        Success = false,
                        Message = data.message?.ToString() ?? "登录失败"
                    };
                }
            }
            catch (Exception ex)
            {
                return new LoginResult
                {
                    Success = false,
                    Message = $"解析登录响应失败: {ex.Message}"
                };
            }
        }
        
        private string ExtractCookiesFromResponse(string response)
        {
            // 这里应该从HTTP响应头中提取Cookie
            // 为了简化，返回一个示例Cookie格式
            return "SESSDATA=example; bili_jct=example; DedeUserID=example";
        }
        
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
    
    public class LoginKeyInfo
    {
        public string Hash { get; set; }
        public string PublicKey { get; set; }
    }
    
    public class LoginResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public string Cookie { get; set; }
        public UserInfo UserInfo { get; set; }
    }
    
    public class UserInfo
    {
        public string UserId { get; set; }
        public string Username { get; set; }
        public string Avatar { get; set; }
        public bool IsVip { get; set; }
    }
}
