using System;
using System.IO;
using System.Threading.Tasks;

namespace BilibiliDownloader.Utils
{
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error
    }
    
    public class Logger
    {
        private static Logger _instance;
        private static readonly object _lock = new object();
        private readonly string _logFilePath;
        
        public static Logger Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new Logger();
                        }
                    }
                }
                return _instance;
            }
        }
        
        public event EventHandler<LogEventArgs> LogMessageReceived;
        
        private Logger()
        {
            var logDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            Directory.CreateDirectory(logDir);
            _logFilePath = Path.Combine(logDir, $"bilibili_downloader_{DateTime.Now:yyyyMMdd}.log");
        }
        
        public void Debug(string message, Exception exception = null)
        {
            Log(LogLevel.Debug, message, exception);
        }
        
        public void Info(string message, Exception exception = null)
        {
            Log(LogLevel.Info, message, exception);
        }
        
        public void Warning(string message, Exception exception = null)
        {
            Log(LogLevel.Warning, message, exception);
        }
        
        public void Error(string message, Exception exception = null)
        {
            Log(LogLevel.Error, message, exception);
        }
        
        private void Log(LogLevel level, string message, Exception exception = null)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var logMessage = $"[{timestamp}] [{level}] {message}";
            
            if (exception != null)
            {
                logMessage += $"\nException: {exception.Message}";
                logMessage += $"\nStackTrace: {exception.StackTrace}";
            }
            
            // 触发事件，供UI显示
            LogMessageReceived?.Invoke(this, new LogEventArgs
            {
                Level = level,
                Message = message,
                Exception = exception,
                Timestamp = DateTime.Now
            });
            
            // 异步写入文件
            Task.Run(() => WriteToFile(logMessage));
        }
        
        private void WriteToFile(string message)
        {
            try
            {
                lock (_lock)
                {
                    File.AppendAllText(_logFilePath, message + Environment.NewLine);
                }
            }
            catch
            {
                // 忽略日志写入错误，避免无限循环
            }
        }
        
        public void ClearOldLogs(int daysToKeep = 7)
        {
            try
            {
                var logDir = Path.GetDirectoryName(_logFilePath);
                var files = Directory.GetFiles(logDir, "bilibili_downloader_*.log");
                
                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < DateTime.Now.AddDays(-daysToKeep))
                    {
                        File.Delete(file);
                    }
                }
            }
            catch (Exception ex)
            {
                Error("清理旧日志文件失败", ex);
            }
        }
    }
    
    public class LogEventArgs : EventArgs
    {
        public LogLevel Level { get; set; }
        public string Message { get; set; }
        public Exception Exception { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
