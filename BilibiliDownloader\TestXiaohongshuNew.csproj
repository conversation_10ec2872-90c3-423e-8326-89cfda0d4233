<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <StartupObject>BilibiliDownloader.TestXiaohongshuNew</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
    <PackageReference Include="YamlDotNet" Version="16.1.3" />
  </ItemGroup>

  <PropertyGroup>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="Core\**\*.cs" />
    <Compile Include="Models\**\*.cs" />
    <Compile Include="Utils\**\*.cs" />
    <Compile Include="TestXiaohongshuNew.cs" />
  </ItemGroup>

</Project>
