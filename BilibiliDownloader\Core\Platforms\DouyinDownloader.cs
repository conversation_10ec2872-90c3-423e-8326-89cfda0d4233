using System.Text.RegularExpressions;
using System.Text.Json;
using System.Net.Http;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core.Platforms
{
    public class DouyinDownloader : IPlatformDownloader
    {
        private readonly HttpClient _httpClient;

        public string PlatformName => "抖音";
        public string PlatformIcon => "🎵";
        public bool RequiresLogin => false;

        public string[] SupportedUrlPatterns => new[]
        {
            @"https?://(?:www\.)?douyin\.com/video/\d+",
            @"https?://v\.douyin\.com/[\w]+",
            @"https?://(?:www\.)?iesdouyin\.com/share/video/\d+",
            @"https?://(?:www\.)?douyin\.com/user/[\w]+/video/\d+"
        };

        public DouyinDownloader()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1");
        }

        public bool IsUrlSupported(string url)
        {
            return SupportedUrlPatterns.Any(pattern => Regex.IsMatch(url, pattern));
        }

        public async Task<VideoInfo?> ParseVideoInfoAsync(string url, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"解析抖音视频: {url}");

                // 设置Cookie（如果提供）
                if (!string.IsNullOrEmpty(cookie))
                {
                    _httpClient.DefaultRequestHeaders.Remove("Cookie");
                    _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
                }

                // 处理短链接
                var fullUrl = await ResolveShortUrl(url);
                var videoId = ExtractVideoId(fullUrl);

                if (string.IsNullOrEmpty(videoId))
                {
                    throw new Exception("无法提取视频ID");
                }

                // 获取视频信息
                var videoInfo = await GetDouyinVideoInfoAsync(videoId, fullUrl);
                if (videoInfo != null)
                {
                    videoInfo.Platform = PlatformName;
                    videoInfo.OriginalUrl = url;
                }

                return videoInfo;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"抖音解析失败: {ex.Message}");
                return null;
            }
        }

        private async Task<VideoInfo?> GetDouyinVideoInfoAsync(string videoId, string fullUrl)
        {
            try
            {
                // 获取网页内容
                var pageContent = await _httpClient.GetStringAsync(fullUrl);

                // 从页面中提取视频信息
                var title = ExtractTitleFromPage(pageContent) ?? $"抖音视频 {videoId}";
                var author = ExtractAuthorFromPage(pageContent) ?? "未知作者";
                var description = ExtractDescriptionFromPage(pageContent) ?? "";
                var coverUrl = ExtractCoverFromPage(pageContent) ?? "";
                var duration = ExtractDurationFromPage(pageContent);

                return new VideoInfo
                {
                    VideoId = videoId,
                    Title = title,
                    Author = author,
                    Description = description,
                    CoverUrl = coverUrl,
                    Duration = duration,
                    ViewCount = 0, // 抖音不公开具体播放数
                    PublishTime = DateTime.Now, // 抖音API限制
                    IsCollection = false,
                    IsPaymentRequired = false,
                    Pages = new List<VideoPage>
                    {
                        new VideoPage
                        {
                            Page = 1,
                            Cid = 0,
                            Part = title,
                            Duration = duration
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取抖音视频信息失败: {ex.Message}");
                return null;
            }
        }

        public async Task<List<QualityOption>> GetAvailableQualitiesAsync(VideoInfo videoInfo, string? cookie = null)
        {
            var qualities = new List<QualityOption>
            {
                new() { Quality = "720p", Description = "高清", Width = 720, Height = 1280, Format = "mp4" },
                new() { Quality = "540p", Description = "标清", Width = 540, Height = 960, Format = "mp4" },
                new() { Quality = "360p", Description = "流畅", Width = 360, Height = 640, Format = "mp4" }
            };

            return qualities;
        }

        public async Task<List<DownloadUrl>> GetDownloadUrlsAsync(VideoInfo videoInfo, string quality, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"获取抖音下载链接: {videoInfo.VideoId}, 质量: {quality}");

                // 设置Cookie（如果提供）
                if (!string.IsNullOrEmpty(cookie))
                {
                    _httpClient.DefaultRequestHeaders.Remove("Cookie");
                    _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
                }

                // 尝试获取实际的下载链接
                var downloadUrls = await GetDouyinDownloadUrlsAsync(videoInfo.VideoId, quality);
                
                if (downloadUrls.Count == 0)
                {
                    Logger.Instance.Warning("无法获取抖音直接下载链接");
                    throw new Exception("抖音下载需要特殊处理。由于抖音的反爬虫机制，无法直接获取下载链接。");
                }

                return downloadUrls;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取抖音下载链接失败: {ex.Message}");
                throw new Exception($"抖音下载失败: {ex.Message}\n\n建议：\n1. 使用专业的抖音下载工具\n2. 检查网络连接\n3. 确认视频是否可用");
            }
        }

        private async Task<List<DownloadUrl>> GetDouyinDownloadUrlsAsync(string videoId, string quality)
        {
            try
            {
                var downloadUrls = new List<DownloadUrl>();

                // 构建抖音视频页面URL
                var videoUrl = $"https://www.douyin.com/video/{videoId}";

                // 获取页面内容
                var pageContent = await _httpClient.GetStringAsync(videoUrl);

                // 从页面中提取视频下载链接
                var videoUrls = ExtractVideoUrlsFromPage(pageContent, quality);

                foreach (var url in videoUrls)
                {
                    if (!string.IsNullOrEmpty(url))
                    {
                        // 获取真实的下载链接（处理重定向）
                        var realUrl = await GetRealDownloadUrl(url);

                        if (!string.IsNullOrEmpty(realUrl))
                        {
                            downloadUrls.Add(new DownloadUrl
                            {
                                Url = realUrl,
                                Quality = quality,
                                Format = "mp4",
                                FileSize = EstimateFileSize(quality),
                                Headers = new Dictionary<string, string>
                                {
                                    ["User-Agent"] = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15",
                                    ["Referer"] = "https://www.douyin.com/"
                                }
                            });
                        }
                    }
                }

                // 如果没有找到视频链接，尝试使用API方式
                if (downloadUrls.Count == 0)
                {
                    var apiUrls = await GetDouyinUrlsFromApi(videoId, quality);
                    downloadUrls.AddRange(apiUrls);
                }

                return downloadUrls;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"抖音下载URL获取异常: {ex.Message}");
                return new List<DownloadUrl>();
            }
        }

        private List<string> ExtractVideoUrlsFromPage(string pageContent, string quality)
        {
            var urls = new List<string>();

            try
            {
                // 尝试提取不同格式的视频链接
                var patterns = new[]
                {
                    @"""play_url"":""([^""]+)""",
                    @"""download_url"":""([^""]+)""",
                    @"""url_list"":\[""([^""]+)""",
                    @"""play_addr"":{[^}]*""url_list"":\[""([^""]+)"""
                };

                foreach (var pattern in patterns)
                {
                    var matches = Regex.Matches(pageContent, pattern);
                    foreach (Match match in matches)
                    {
                        var url = match.Groups[1].Value.Replace("\\u002F", "/").Replace("\\/", "/");
                        if (!string.IsNullOrEmpty(url) && url.StartsWith("http"))
                        {
                            urls.Add(url);
                        }
                    }
                }

                // 去重
                return urls.Distinct().ToList();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"提取抖音视频链接失败: {ex.Message}");
                return urls;
            }
        }

        private async Task<string> GetRealDownloadUrl(string url)
        {
            try
            {
                // 处理抖音的重定向链接
                var request = new HttpRequestMessage(HttpMethod.Head, url);
                request.Headers.Add("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15");

                var response = await _httpClient.SendAsync(request);

                if (response.Headers.Location != null)
                {
                    return response.Headers.Location.ToString();
                }

                return url;
            }
            catch
            {
                return url;
            }
        }

        private async Task<List<DownloadUrl>> GetDouyinUrlsFromApi(string videoId, string quality)
        {
            try
            {
                // 尝试使用抖音的移动端API
                var apiUrl = $"https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/?item_ids={videoId}";

                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Add("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var jsonDoc = JsonDocument.Parse(content);

                    if (jsonDoc.RootElement.TryGetProperty("item_list", out var itemList) &&
                        itemList.GetArrayLength() > 0)
                    {
                        var item = itemList[0];
                        if (item.TryGetProperty("video", out var video) &&
                            video.TryGetProperty("play_addr", out var playAddr) &&
                            playAddr.TryGetProperty("url_list", out var urlList) &&
                            urlList.GetArrayLength() > 0)
                        {
                            var videoUrl = urlList[0].GetString();
                            if (!string.IsNullOrEmpty(videoUrl))
                            {
                                return new List<DownloadUrl>
                                {
                                    new DownloadUrl
                                    {
                                        Url = videoUrl,
                                        Quality = quality,
                                        Format = "mp4",
                                        FileSize = EstimateFileSize(quality),
                                        Headers = new Dictionary<string, string>
                                        {
                                            ["User-Agent"] = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15",
                                            ["Referer"] = "https://www.douyin.com/"
                                        }
                                    }
                                };
                            }
                        }
                    }
                }

                return new List<DownloadUrl>();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"抖音API获取失败: {ex.Message}");
                return new List<DownloadUrl>();
            }
        }

        private long EstimateFileSize(string quality)
        {
            return quality switch
            {
                "720p" => 20 * 1024 * 1024L,  // 20MB
                "540p" => 15 * 1024 * 1024L,  // 15MB
                "360p" => 10 * 1024 * 1024L,  // 10MB
                _ => 15 * 1024 * 1024L
            };
        }

        public async Task<bool> ValidateAccountAsync(string cookie)
        {
            try
            {
                // 简单验证Cookie是否有效
                _httpClient.DefaultRequestHeaders.Remove("Cookie");
                _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
                
                var response = await _httpClient.GetAsync("https://www.douyin.com/");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private string? ExtractTitleFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取标题
                var titleMatch = Regex.Match(pageContent, @"""desc"":""([^""]+)""");
                if (titleMatch.Success)
                {
                    return titleMatch.Groups[1].Value.Replace("\\n", " ").Replace("\\\"", "\"");
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractAuthorFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取作者信息
                var authorMatch = Regex.Match(pageContent, @"""nickname"":""([^""]+)""");
                if (authorMatch.Success)
                {
                    return authorMatch.Groups[1].Value;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractDescriptionFromPage(string pageContent)
        {
            try
            {
                // 抖音的描述通常就是标题
                return ExtractTitleFromPage(pageContent);
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractCoverFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取封面图
                var coverMatch = Regex.Match(pageContent, @"""cover"":""([^""]+)""");
                if (coverMatch.Success)
                {
                    return coverMatch.Groups[1].Value.Replace("\\u002F", "/");
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private long ExtractDurationFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取时长
                var durationMatch = Regex.Match(pageContent, @"""duration"":(\d+)");
                if (durationMatch.Success && long.TryParse(durationMatch.Groups[1].Value, out var duration))
                {
                    return duration;
                }
                return 15; // 抖音默认15秒
            }
            catch
            {
                return 15;
            }
        }

        private async Task<string> ResolveShortUrl(string url)
        {
            try
            {
                if (url.Contains("v.douyin.com"))
                {
                    var request = new HttpRequestMessage(HttpMethod.Head, url);
                    var response = await _httpClient.SendAsync(request);
                    
                    if (response.Headers.Location != null)
                    {
                        return response.Headers.Location.ToString();
                    }
                }
                return url;
            }
            catch
            {
                return url;
            }
        }

        private string ExtractVideoId(string url)
        {
            var patterns = new[]
            {
                @"douyin\.com/video/(\d+)",
                @"iesdouyin\.com/share/video/(\d+)",
                @"video/(\d+)"
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(url, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            return "";
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
