﻿#pragma checksum "..\..\..\..\UI\XiaohongshuLoginWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4E528D888602FD9DD227F5DCD5454450D9095C8B"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace BilibiliDownloader.UI {
    
    
    /// <summary>
    /// XiaohongshuLoginWindow
    /// </summary>
    public partial class XiaohongshuLoginWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 20 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtLoginStatus;
        
        #line default
        #line hidden
        
        
        #line 26 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl tabLogin;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnGenerateQrCode;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtQrStatus;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border qrCodeContainer;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtQrCodeTip;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtPhoneNumber;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtVerificationCode;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSendCode;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnPhoneLogin;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtPhoneLoginStatus;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtCookie;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCookieLogin;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCookieHelper;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnOpenGuide;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtCookieLoginStatus;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCancel;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnConfirm;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/BilibiliDownloader;V2.0.0.0;component/ui/xiaohongshuloginwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.txtLoginStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.tabLogin = ((System.Windows.Controls.TabControl)(target));
            return;
            case 3:
            this.btnGenerateQrCode = ((System.Windows.Controls.Button)(target));
            
            #line 49 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
            this.btnGenerateQrCode.Click += new System.Windows.RoutedEventHandler(this.BtnGenerateQrCode_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.txtQrStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.qrCodeContainer = ((System.Windows.Controls.Border)(target));
            return;
            case 6:
            this.txtQrCodeTip = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.txtPhoneNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.txtVerificationCode = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.btnSendCode = ((System.Windows.Controls.Button)(target));
            
            #line 103 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
            this.btnSendCode.Click += new System.Windows.RoutedEventHandler(this.BtnSendCode_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.btnPhoneLogin = ((System.Windows.Controls.Button)(target));
            
            #line 110 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
            this.btnPhoneLogin.Click += new System.Windows.RoutedEventHandler(this.BtnPhoneLogin_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.txtPhoneLoginStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.txtCookie = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.btnCookieLogin = ((System.Windows.Controls.Button)(target));
            
            #line 142 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
            this.btnCookieLogin.Click += new System.Windows.RoutedEventHandler(this.BtnCookieLogin_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.btnCookieHelper = ((System.Windows.Controls.Button)(target));
            
            #line 145 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
            this.btnCookieHelper.Click += new System.Windows.RoutedEventHandler(this.BtnCookieHelper_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.btnOpenGuide = ((System.Windows.Controls.Button)(target));
            
            #line 148 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
            this.btnOpenGuide.Click += new System.Windows.RoutedEventHandler(this.BtnOpenGuide_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.txtCookieLoginStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.btnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 163 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
            this.btnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.btnConfirm = ((System.Windows.Controls.Button)(target));
            
            #line 165 "..\..\..\..\UI\XiaohongshuLoginWindow.xaml"
            this.btnConfirm.Click += new System.Windows.RoutedEventHandler(this.BtnConfirm_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

