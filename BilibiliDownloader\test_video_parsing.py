#!/usr/bin/env python3
"""
简单的测试脚本，用于验证bilibili视频解析功能
"""

import requests
import json
import re

def test_bilibili_api():
    """测试bilibili API接口"""
    
    # 测试视频链接
    test_url = "https://www.bilibili.com/video/BV1tMhNe1ELf"
    
    # 提取BV号
    bv_match = re.search(r'BV([A-Za-z0-9]+)', test_url)
    if not bv_match:
        print("无法提取BV号")
        return
    
    bvid = "BV" + bv_match.group(1)
    print(f"提取到BV号: {bvid}")
    
    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.bilibili.com/',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
    }
    
    try:
        # 1. 获取视频基本信息
        print("\n=== 测试视频基本信息API ===")
        info_url = f"https://api.bilibili.com/x/web-interface/view?bvid={bvid}"
        response = requests.get(info_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"API响应状态: {data.get('code', 'unknown')}")
            print(f"API消息: {data.get('message', 'unknown')}")
            
            if data.get('code') == 0:
                video_data = data.get('data', {})
                print(f"视频标题: {video_data.get('title', 'unknown')}")
                print(f"视频作者: {video_data.get('owner', {}).get('name', 'unknown')}")
                print(f"视频时长: {video_data.get('duration', 'unknown')}秒")
                print(f"是否付费: {video_data.get('rights', {}).get('pay', 0) == 1}")
                
                # 获取cid
                pages = video_data.get('pages', [])
                if pages:
                    cid = pages[0].get('cid')
                    print(f"视频CID: {cid}")
                    
                    # 2. 测试播放信息API
                    print("\n=== 测试播放信息API ===")
                    play_url = f"https://api.bilibili.com/x/player/playurl?bvid={bvid}&cid={cid}&qn=120&fnval=4048&fourk=1&try_look=1"
                    play_response = requests.get(play_url, headers=headers, timeout=10)
                    
                    if play_response.status_code == 200:
                        play_data = play_response.json()
                        print(f"播放API响应状态: {play_data.get('code', 'unknown')}")
                        print(f"播放API消息: {play_data.get('message', 'unknown')}")
                        
                        if play_data.get('code') == 0:
                            play_info = play_data.get('data', {})
                            
                            # 检查DASH格式
                            if 'dash' in play_info:
                                dash = play_info['dash']
                                video_streams = dash.get('video', [])
                                audio_streams = dash.get('audio', [])
                                print(f"DASH视频流数量: {len(video_streams)}")
                                print(f"DASH音频流数量: {len(audio_streams)}")
                                
                                for i, stream in enumerate(video_streams[:3]):  # 只显示前3个
                                    print(f"  视频流{i+1}: 清晰度ID={stream.get('id')}, 编码={stream.get('codecs')}")
                            
                            # 检查传统格式
                            if 'durl' in play_info:
                                durl = play_info['durl']
                                print(f"传统格式流数量: {len(durl)}")
                                
                        else:
                            print(f"播放信息获取失败: {play_data.get('message')}")
                    else:
                        print(f"播放API请求失败: HTTP {play_response.status_code}")
                        
            else:
                print(f"视频信息获取失败: {data.get('message')}")
        else:
            print(f"基本信息API请求失败: HTTP {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"网络请求错误: {e}")
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
    except Exception as e:
        print(f"其他错误: {e}")

if __name__ == "__main__":
    print("Bilibili API测试脚本")
    print("=" * 50)
    test_bilibili_api()
    print("\n测试完成")
