Button {
    width: 1fr;
    margin: 1 1;
}
.vertical-layout {
    layout: vertical;
    height: auto;
}
.horizontal-layout, .settings_button {
    layout: horizontal;
    height: auto;
}
.horizontal-layout > * {
    width: 25vw;
}
Button#deal, Button#paste, Button#save, Button#enter {
    color: $success;
}
Button#reset, Button#abandon, Button#close {
    color: $error;
}
Label, Link {
    width: 100%;
    content-align-horizontal: center;
    content-align-vertical: middle;
}
Link {
    color: $accent;
}
Label.params {
    margin: 1 0 0 0;
    color: $primary;
}
Label.prompt {
    padding: 1;
}
.loading {
    grid-size: 1 2;
    grid-gutter: 1;
    width: 40vw;
    height: 5;
    border: double $primary;
}
#record {
    grid-size: 1 3;
    width: 80vw;
    height: 12;
    border: double $primary;
}
ModalScreen {
    align: center middle;
}
