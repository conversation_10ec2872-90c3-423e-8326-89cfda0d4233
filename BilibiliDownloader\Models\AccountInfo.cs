using System;

namespace BilibiliDownloader.Models
{
    public class AccountInfo
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Platform { get; set; } = "";
        public string PlatformIcon { get; set; } = "";
        public string Username { get; set; } = "";
        public string Password { get; set; } = "";
        public string Cookie { get; set; } = "";
        public DateTime LastLoginTime { get; set; }
        public bool IsActive { get; set; }
        public string UserId { get; set; } = "";
        public string Nickname { get; set; } = "";
        public string? Avatar { get; set; }
        public int Level { get; set; }
        public bool IsVip { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public DateTime CreatedTime { get; set; } = DateTime.Now;
        public DateTime LastValidatedTime { get; set; }
        public bool IsValid { get; set; } = true;
        public string? Notes { get; set; }

        /// <summary>
        /// 显示名称（优先显示昵称，其次用户名）
        /// </summary>
        public string DisplayName => !string.IsNullOrEmpty(Nickname) ? Nickname : Username;

        /// <summary>
        /// 平台显示名称
        /// </summary>
        public string PlatformDisplayName => $"{PlatformIcon} {Platform}";

        public AccountInfo()
        {
            LastLoginTime = DateTime.Now;
            IsActive = false;
        }

        public AccountInfo(string platform, string platformIcon, string username) : this()
        {
            Platform = platform;
            PlatformIcon = platformIcon;
            Username = username;
        }

        public static AccountInfo CreateWithCookie(string platform, string platformIcon, string username, string cookie)
        {
            var account = new AccountInfo(platform, platformIcon, username);
            account.Cookie = cookie;
            return account;
        }

        public override string ToString()
        {
            var displayName = !string.IsNullOrEmpty(Nickname) ? Nickname : Username;
            return $"{PlatformIcon} {Platform} - {displayName}";
        }
    }
}
