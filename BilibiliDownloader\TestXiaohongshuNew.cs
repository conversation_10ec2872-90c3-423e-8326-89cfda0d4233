using System;
using System.Threading.Tasks;
using BilibiliDownloader.Core;
using BilibiliDownloader.Core.Platforms;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader
{
    /// <summary>
    /// 测试新的小红书下载器 - 2025年最新版本
    /// </summary>
    public class TestXiaohongshuNew
    {
        public static async Task Main(string[] args)
        {
            try
            {
                Console.WriteLine("=== 小红书下载器测试 - 2025年最新版本 ===");
                Console.WriteLine();

                // 初始化下载器
                var downloader = new XiaohongshuDownloader();
                
                // 测试URL支持检测
                await TestUrlSupport(downloader);
                
                // 测试视频信息解析
                await TestVideoInfoParsing(downloader);
                
                // 测试下载链接获取
                await TestDownloadUrls(downloader);
                
                Console.WriteLine("\n=== 测试完成 ===");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
                Console.ReadKey();
            }
        }

        private static async Task TestUrlSupport(XiaohongshuDownloader downloader)
        {
            Console.WriteLine("1. 测试URL支持检测");
            Console.WriteLine("==================");
            
            var testUrls = new[]
            {
                "https://www.xiaohongshu.com/explore/abc123",
                "https://xhslink.com/def456", 
                "https://www.xiaohongshu.com/discovery/item/ghi789",
                "https://www.bilibili.com/video/BV1234567890", // 不支持的URL
                "https://www.youtube.com/watch?v=abc123" // 不支持的URL
            };

            foreach (var url in testUrls)
            {
                var isSupported = downloader.IsUrlSupported(url);
                Console.WriteLine($"URL: {url}");
                Console.WriteLine($"支持: {(isSupported ? "✓" : "✗")}");
                Console.WriteLine();
            }
        }

        private static async Task TestVideoInfoParsing(XiaohongshuDownloader downloader)
        {
            Console.WriteLine("2. 测试视频信息解析");
            Console.WriteLine("==================");
            
            // 使用一个示例URL（实际使用时需要真实的小红书链接）
            var testUrl = "https://www.xiaohongshu.com/explore/abc123";
            
            Console.WriteLine($"测试URL: {testUrl}");
            Console.WriteLine("正在解析视频信息...");
            
            try
            {
                var videoInfo = await downloader.ParseVideoInfoAsync(testUrl);
                
                if (videoInfo != null)
                {
                    Console.WriteLine("✓ 解析成功");
                    Console.WriteLine($"标题: {videoInfo.Title}");
                    Console.WriteLine($"作者: {videoInfo.Author}");
                    Console.WriteLine($"描述: {videoInfo.Description}");
                    Console.WriteLine($"平台: {videoInfo.Platform}");
                    Console.WriteLine($"内容类型: {videoInfo.ContentType}");
                    Console.WriteLine($"观看次数: {videoInfo.ViewCount}");
                    Console.WriteLine($"点赞数: {videoInfo.LikeCount}");
                }
                else
                {
                    Console.WriteLine("✗ 解析失败 - 返回null");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 解析失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }

        private static async Task TestDownloadUrls(XiaohongshuDownloader downloader)
        {
            Console.WriteLine("3. 测试下载链接获取");
            Console.WriteLine("==================");
            
            // 创建一个模拟的VideoInfo对象
            var mockVideoInfo = new Models.VideoInfo
            {
                Title = "测试小红书视频",
                Author = "测试作者",
                Description = "这是一个测试视频",
                Url = "https://test-video-url.com/video.mp4",
                CoverUrl = "https://test-cover-url.com/cover.jpg",
                Platform = "小红书",
                ContentType = "video"
            };

            try
            {
                var downloadUrls = await downloader.GetDownloadUrlsAsync(mockVideoInfo, "高清");
                
                Console.WriteLine($"获取到 {downloadUrls.Count} 个下载链接:");
                
                for (int i = 0; i < downloadUrls.Count; i++)
                {
                    var url = downloadUrls[i];
                    Console.WriteLine($"链接 {i + 1}:");
                    Console.WriteLine($"  URL: {url.Url}");
                    Console.WriteLine($"  质量: {url.Quality}");
                    Console.WriteLine($"  格式: {url.Format}");
                    Console.WriteLine($"  文件大小: {url.FileSize} bytes");
                    Console.WriteLine($"  请求头数量: {url.Headers.Count}");
                    Console.WriteLine();
                }
                
                // 测试质量选项
                var qualities = await downloader.GetAvailableQualitiesAsync(mockVideoInfo);
                Console.WriteLine($"可用质量选项: {qualities.Count} 个");
                
                foreach (var quality in qualities)
                {
                    Console.WriteLine($"  - {quality.Quality}: {quality.Description} ({quality.Width}x{quality.Height})");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 获取下载链接失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }
    }
}
