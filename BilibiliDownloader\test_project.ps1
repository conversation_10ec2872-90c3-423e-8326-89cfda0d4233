# 测试项目结构和编译
Write-Host "当前目录: $(Get-Location)" -ForegroundColor Green

Write-Host "检查项目文件..." -ForegroundColor Yellow
if (Test-Path "BilibiliDownloader.csproj") {
    Write-Host "✓ 找到项目文件: BilibiliDownloader.csproj" -ForegroundColor Green
} else {
    Write-Host "✗ 未找到项目文件" -ForegroundColor Red
    Get-ChildItem -Name "*.csproj"
}

Write-Host "检查主要源文件..." -ForegroundColor Yellow
$files = @("MainWindow.xaml", "MainWindow.xaml.cs", "App.xaml", "App.xaml.cs")
foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        Write-Host "✗ $file" -ForegroundColor Red
    }
}

Write-Host "尝试编译..." -ForegroundColor Yellow
dotnet build --verbosity minimal

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 编译成功!" -ForegroundColor Green
    Write-Host "尝试运行..." -ForegroundColor Yellow
    Start-Process "dotnet" -ArgumentList "run" -NoNewWindow
} else {
    Write-Host "✗ 编译失败" -ForegroundColor Red
}

Read-Host "按任意键退出"
