# 增强解析功能说明

## 🚀 新增功能

### 1. 多格式URL支持
现在支持更多种类的B站链接格式：

#### 标准视频链接
- `https://www.bilibili.com/video/BV1xx411c7mD`
- `https://www.bilibili.com/video/av12345`
- `https://www.bilibili.com/video/BV1xx411c7mD?p=2`

#### 短链接
- `https://b23.tv/shortlink` (自动解析重定向)

#### 番剧链接
- `https://www.bilibili.com/bangumi/play/ss33073`
- `https://www.bilibili.com/bangumi/play/ep394932`

#### 直接输入
- `BV1xx411c7mD`
- `av12345`
- `ss33073`
- `ep394932`

### 2. 智能AV号转换
- 自动将AV号转换为BV号
- 使用B站官方算法确保准确性
- 支持大数值AV号

### 3. 备用解析系统
当主要解析方法失败时，自动尝试备用方案：

#### yt-dlp集成
- 自动检测系统中的yt-dlp
- 支持多种安装路径
- 解析yt-dlp的JSON输出

#### 多API策略
- 尝试不同的API端点
- 使用不同的请求参数
- 智能错误恢复

### 4. 增强的付费绕过
- 4种不同的绕过策略
- 自动策略选择
- 详细的日志记录

## 📋 使用方法

### 基本使用
1. 在URL输入框中粘贴任意支持的链接格式
2. 勾选"尝试绕过付费检测"（如需要）
3. 点击"解析视频"
4. 程序会自动尝试最佳解析方法

### 特殊链接处理
对于无法解析的特殊链接，程序会：
1. 首先尝试主要解析方法
2. 如果失败，自动使用备用解析器
3. 显示详细的错误信息和建议

### yt-dlp集成
要使用yt-dlp备用解析功能：

1. **下载yt-dlp**：
   - 访问 https://github.com/yt-dlp/yt-dlp/releases
   - 下载最新版本的yt-dlp.exe

2. **安装位置**（任选其一）：
   - 放在程序同目录下
   - 放在 `tools/` 子目录下
   - 添加到系统PATH环境变量

3. **验证安装**：
   - 程序启动时会显示备用解析器状态
   - 看到"✓ yt-dlp"表示可用

## 🔧 技术特性

### URL解析增强
- 正则表达式模式匹配
- 支持复杂URL参数
- 自动短链接解析
- 番剧和合集识别

### 错误处理
- 多层错误恢复
- 详细的日志记录
- 用户友好的错误提示
- 自动重试机制

### 性能优化
- 异步URL解析
- 智能缓存机制
- 并发请求处理
- 超时控制

## 🐛 故障排除

### 常见问题

**Q: 短链接解析失败**
A: 检查网络连接，某些短链接可能需要特殊处理

**Q: yt-dlp未找到**
A: 确保yt-dlp.exe在正确位置，或添加到PATH环境变量

**Q: 特殊视频无法解析**
A: 尝试启用付费绕过功能，或使用不同的链接格式

**Q: 解析速度慢**
A: 这是正常现象，程序会尝试多种方法确保成功

### 日志分析
程序会记录详细的解析过程：
- URL格式识别
- 解析策略选择
- 错误和重试信息
- 最终结果状态

## 📈 未来计划

### 即将添加的功能
- [ ] 更多第三方解析服务集成
- [ ] 自定义解析策略配置
- [ ] 批量链接解析
- [ ] 解析结果缓存
- [ ] 更多视频平台支持

### 性能改进
- [ ] 并行解析策略
- [ ] 智能策略学习
- [ ] 网络请求优化
- [ ] 内存使用优化

## ⚠️ 注意事项

1. **合法使用**：请遵守相关法律法规和平台服务条款
2. **网络要求**：某些功能需要稳定的网络连接
3. **系统要求**：yt-dlp需要.NET运行时支持
4. **更新维护**：定期更新yt-dlp以获得最佳兼容性

## 🤝 贡献

如果您发现新的无法解析的链接格式，请：
1. 提供具体的链接示例
2. 描述期望的行为
3. 包含错误日志信息

我们会持续改进解析能力，支持更多的链接格式和特殊情况。
