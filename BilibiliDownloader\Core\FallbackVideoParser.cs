using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core
{
    /// <summary>
    /// 备用视频解析器，使用多种策略解析特殊链接
    /// </summary>
    public class FallbackVideoParser
    {
        private readonly string _ytDlpPath;
        private readonly string _workingDirectory;

        public FallbackVideoParser(string workingDirectory = null)
        {
            _workingDirectory = workingDirectory ?? Environment.CurrentDirectory;
            _ytDlpPath = FindYtDlpExecutable();
        }

        /// <summary>
        /// 尝试使用备用方法解析视频信息
        /// </summary>
        public async Task<VideoInfo> ParseVideoAsync(string url)
        {
            Logger.Instance.Info("开始使用备用解析器解析视频");

            // 策略1: 使用yt-dlp
            if (!string.IsNullOrEmpty(_ytDlpPath))
            {
                try
                {
                    var videoInfo = await ParseWithYtDlpAsync(url);
                    if (videoInfo != null)
                    {
                        Logger.Instance.Info("yt-dlp解析成功");
                        return videoInfo;
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance.Warning($"yt-dlp解析失败: {ex.Message}");
                }
            }

            // 策略2: 使用不同的API端点
            try
            {
                var videoInfo = await ParseWithAlternativeApiAsync(url);
                if (videoInfo != null)
                {
                    Logger.Instance.Info("备用API解析成功");
                    return videoInfo;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"备用API解析失败: {ex.Message}");
            }

            // 策略3: 使用第三方解析服务
            try
            {
                var videoInfo = await ParseWithThirdPartyServiceAsync(url);
                if (videoInfo != null)
                {
                    Logger.Instance.Info("第三方服务解析成功");
                    return videoInfo;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"第三方服务解析失败: {ex.Message}");
            }

            throw new Exception("所有备用解析策略均失败");
        }

        private async Task<VideoInfo> ParseWithYtDlpAsync(string url)
        {
            if (string.IsNullOrEmpty(_ytDlpPath))
            {
                throw new Exception("yt-dlp未找到");
            }

            var startInfo = new ProcessStartInfo
            {
                FileName = _ytDlpPath,
                Arguments = $"--dump-json --no-download \"{url}\"",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true,
                WorkingDirectory = _workingDirectory
            };

            using var process = new Process { StartInfo = startInfo };
            process.Start();

            var output = await process.StandardOutput.ReadToEndAsync();
            var error = await process.StandardError.ReadToEndAsync();

            await process.WaitForExitAsync();

            if (process.ExitCode != 0)
            {
                throw new Exception($"yt-dlp执行失败: {error}");
            }

            // 解析yt-dlp的JSON输出
            return ParseYtDlpJson(output);
        }

        private VideoInfo ParseYtDlpJson(string jsonOutput)
        {
            try
            {
                using var document = JsonDocument.Parse(jsonOutput);
                var root = document.RootElement;

                var videoInfo = new VideoInfo
                {
                    VideoId = root.GetProperty("id").GetString() ?? "",
                    Title = root.GetProperty("title").GetString() ?? "",
                    Description = root.TryGetProperty("description", out var desc) ? desc.GetString() ?? "" : "",
                    Author = root.TryGetProperty("uploader", out var uploader) ? uploader.GetString() ?? "" : "",
                    Duration = root.TryGetProperty("duration", out var duration) ? duration.GetInt64() : 0,
                    OriginalUrl = root.TryGetProperty("webpage_url", out var url) ? url.GetString() ?? "" : ""
                };

                // 解析可用格式
                if (root.TryGetProperty("formats", out var formats))
                {
                    foreach (var format in formats.EnumerateArray())
                    {
                        var quality = new VideoQuality
                        {
                            Quality = format.TryGetProperty("height", out var height) ? height.GetInt32() : 0,
                            QualityName = format.TryGetProperty("format_note", out var note) ? note.GetString() ?? "" : "",
                            VideoUrl = format.TryGetProperty("url", out var formatUrl) ? formatUrl.GetString() ?? "" : "",
                            FileSize = format.TryGetProperty("filesize", out var size) ? size.GetInt64() : 0,
                            Format = format.TryGetProperty("ext", out var ext) ? ext.GetString() ?? "" : "",
                            Codec = format.TryGetProperty("vcodec", out var codec) ? codec.GetString() ?? "" : ""
                        };

                        if (!string.IsNullOrEmpty(quality.VideoUrl))
                        {
                            videoInfo.AvailableQualities.Add(quality);
                        }
                    }
                }

                return videoInfo;
            }
            catch (Exception ex)
            {
                throw new Exception($"解析yt-dlp JSON输出失败: {ex.Message}", ex);
            }
        }

        private async Task<VideoInfo> ParseWithAlternativeApiAsync(string url)
        {
            // 实现备用API解析逻辑
            // 这里可以尝试使用不同的B站API端点或第三方API
            await Task.Delay(100); // 占位符
            return null;
        }

        private async Task<VideoInfo> ParseWithThirdPartyServiceAsync(string url)
        {
            // 实现第三方解析服务
            // 例如使用公开的解析API
            await Task.Delay(100); // 占位符
            return null;
        }

        private string FindYtDlpExecutable()
        {
            var possiblePaths = new[]
            {
                "yt-dlp.exe",
                "yt-dlp",
                Path.Combine(_workingDirectory, "yt-dlp.exe"),
                Path.Combine(_workingDirectory, "tools", "yt-dlp.exe"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Programs", "yt-dlp", "yt-dlp.exe")
            };

            foreach (var path in possiblePaths)
            {
                try
                {
                    if (File.Exists(path))
                    {
                        Logger.Instance.Info($"找到yt-dlp: {path}");
                        return path;
                    }
                }
                catch
                {
                    // 忽略权限错误等
                }
            }

            // 尝试在PATH中查找
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "where",
                    Arguments = "yt-dlp",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    var output = process.StandardOutput.ReadToEnd().Trim();
                    process.WaitForExit();

                    if (process.ExitCode == 0 && !string.IsNullOrEmpty(output))
                    {
                        var firstPath = output.Split('\n')[0].Trim();
                        Logger.Instance.Info($"在PATH中找到yt-dlp: {firstPath}");
                        return firstPath;
                    }
                }
            }
            catch
            {
                // 忽略错误
            }

            Logger.Instance.Warning("未找到yt-dlp，备用解析功能将受限");
            return null;
        }

        /// <summary>
        /// 检查是否有可用的备用解析器
        /// </summary>
        public bool HasAvailableParsers()
        {
            return !string.IsNullOrEmpty(_ytDlpPath);
        }

        /// <summary>
        /// 获取可用解析器的状态信息
        /// </summary>
        public string GetParserStatus()
        {
            var status = new List<string>();

            if (!string.IsNullOrEmpty(_ytDlpPath))
            {
                status.Add("✓ yt-dlp");
            }
            else
            {
                status.Add("✗ yt-dlp (未找到)");
            }

            return string.Join(", ", status);
        }
    }
}
