<Window x:Class="BilibiliDownloader.UI.AccountManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:BilibiliDownloader.UI"
        mc:Ignorable="d"
        Title="🐱 像素喵 - 多平台账号管理" Height="650" Width="800"
        WindowStartupLocation="CenterOwner" ResizeMode="CanResize" MinHeight="650" MinWidth="800">

    <Window.Resources>
        <!-- 样式资源 -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FF005A9B"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#FFCCCCCC"/>
                    <Setter Property="Foreground" Value="#FF666666"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="AccountItemStyle" TargetType="ListBoxItem">
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="0"/>
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="#FFE3F2FD"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#FF2B2B2B" Padding="15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🐱 像素喵多平台账号管理" Foreground="White" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                <TextBlock Text="  |  支持多个视频平台的账号管理" Foreground="#FFAAAAAA" FontSize="12" VerticalAlignment="Center" Margin="10,0,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- 主要内容 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1.2*"/>
                <ColumnDefinition Width="1.5*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：账号列表 -->
            <GroupBox Grid.Column="0" Header="📋 已保存的账号" Margin="0,0,15,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 平台筛选 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="🔍 筛选平台:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <ComboBox Name="cmbPlatformFilter" Width="120" SelectionChanged="CmbPlatformFilter_SelectionChanged"/>
                    </StackPanel>

                    <ListBox Name="lbAccounts" Grid.Row="1" SelectionChanged="LbAccounts_SelectionChanged"
                             ItemContainerStyle="{StaticResource AccountItemStyle}">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border BorderBrush="#FFDDDDDD" BorderThickness="1" Padding="10" CornerRadius="6"
                                        Background="White" Margin="2">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 平台图标 -->
                                        <TextBlock Grid.Column="0" Text="{Binding PlatformIcon}" FontSize="20"
                                                   VerticalAlignment="Center" Margin="0,0,10,0"/>

                                        <!-- 账号信息 -->
                                        <StackPanel Grid.Column="1">
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="{Binding Platform}" FontWeight="Bold" Foreground="#FF0066CC"/>
                                                <TextBlock Text=" - " Foreground="#FF666666"/>
                                                <TextBlock Text="{Binding DisplayName}" FontWeight="Bold"/>
                                            </StackPanel>
                                            <TextBlock Text="{Binding Username}" FontSize="11" Foreground="#FF666666" Margin="0,2,0,0"/>
                                            <TextBlock FontSize="10" Foreground="Gray" Margin="0,2,0,0">
                                                <Run Text="最后登录: "/>
                                                <Run Text="{Binding LastLoginTime, StringFormat=yyyy-MM-dd HH:mm}"/>
                                            </TextBlock>
                                        </StackPanel>

                                        <!-- 状态指示器 -->
                                        <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                                            <Ellipse Width="10" Height="10" VerticalAlignment="Center" Margin="0,0,5,0">
                                                <Ellipse.Style>
                                                    <Style TargetType="Ellipse">
                                                        <Setter Property="Fill" Value="Red"/>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsValid}" Value="True">
                                                                <Setter Property="Fill" Value="Green"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Ellipse.Style>
                                            </Ellipse>
                                            <TextBlock FontSize="10" VerticalAlignment="Center">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Setter Property="Text" Value="无效"/>
                                                        <Setter Property="Foreground" Value="Red"/>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsValid}" Value="True">
                                                                <Setter Property="Text" Value="有效"/>
                                                                <Setter Property="Foreground" Value="Green"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,15,0,0" HorizontalAlignment="Center">
                        <Button Name="btnUseAccount" Content="✅ 使用此账号" Style="{StaticResource ModernButtonStyle}"
                                Click="BtnUseAccount_Click" IsEnabled="False"/>
                        <Button Name="btnValidateAccount" Content="🔍 验证账号" Style="{StaticResource ModernButtonStyle}"
                                Click="BtnValidateAccount_Click" IsEnabled="False"/>
                        <Button Name="btnDeleteAccount" Content="🗑️ 删除账号" Style="{StaticResource ModernButtonStyle}"
                                Click="BtnDeleteAccount_Click" IsEnabled="False" Background="#FFDC3545"/>
                    </StackPanel>
                </Grid>
            </GroupBox>
            
            <!-- 右侧：添加/编辑账号 -->
            <GroupBox Grid.Column="1" Header="➕ 添加新账号" Margin="15,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <!-- 平台选择 -->
                        <TextBlock Text="🌐 选择平台:" FontWeight="Bold" Margin="0,0,0,8"/>
                        <ComboBox Name="cmbPlatform" Height="35" DisplayMemberPath="Name"
                                  SelectionChanged="CmbPlatform_SelectionChanged"/>

                        <Separator Margin="0,15,0,15"/>

                        <!-- 基本信息 -->
                        <TextBlock Text="👤 账号信息:" FontWeight="Bold" Margin="0,0,0,10"/>

                        <TextBlock Text="用户名/邮箱/手机号:" Margin="0,0,0,5"/>
                        <TextBox Name="txtUsername" Height="32" VerticalContentAlignment="Center"
                                 BorderBrush="#FFCCCCCC" BorderThickness="1"/>

                        <TextBlock Text="昵称 (可选):" Margin="0,10,0,5"/>
                        <TextBox Name="txtNickname" Height="32" VerticalContentAlignment="Center"
                                 BorderBrush="#FFCCCCCC" BorderThickness="1"/>

                        <Separator Margin="0,15,0,15"/>

                        <!-- 登录方式 -->
                        <TextBlock Text="🔐 登录方式:" FontWeight="Bold" Margin="0,0,0,10"/>

                        <RadioButton Name="rbPassword" Content="🔑 密码登录" IsChecked="True" Margin="0,5,0,10"/>
                        <StackPanel Name="pnlPassword">
                            <TextBlock Text="密码:" Margin="0,0,0,5"/>
                            <PasswordBox Name="txtPassword" Height="32" VerticalContentAlignment="Center"
                                         BorderBrush="#FFCCCCCC" BorderThickness="1"/>
                            <CheckBox Name="chkSavePassword" Content="保存密码到本地" Margin="0,8,0,0" IsChecked="True"/>
                        </StackPanel>

                        <RadioButton Name="rbCookie" Content="🍪 Cookie登录" Margin="0,15,0,10"/>
                        <StackPanel Name="pnlCookie" Visibility="Collapsed">
                            <TextBlock Text="Cookie内容:" Margin="0,0,0,5"/>
                            <TextBox Name="txtCookie" Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto" VerticalContentAlignment="Top"
                                     FontFamily="Consolas" FontSize="10" BorderBrush="#FFCCCCCC" BorderThickness="1"/>
                            <TextBlock Text="💡 提示：在浏览器登录后，从开发者工具中复制Cookie"
                                       FontSize="10" Foreground="#FF666666" Margin="0,5,0,0" TextWrapping="Wrap"/>
                        </StackPanel>

                        <Separator Margin="0,20,0,15"/>

                        <!-- 操作按钮 -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Name="btnAddAccount" Content="➕ 添加账号" Style="{StaticResource ModernButtonStyle}"
                                    Click="BtnAddAccount_Click"/>
                            <Button Name="btnTestLogin" Content="🔍 测试登录" Style="{StaticResource ModernButtonStyle}"
                                    Click="BtnTestLogin_Click" Background="#FF28A745"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </GroupBox>
        </Grid>
        
        <!-- 底部状态栏 -->
        <Border Grid.Row="2" Background="#FFF8F9FA" BorderBrush="#FFDDDDDD" BorderThickness="0,1,0,0" Padding="15">
            <Grid>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center">
                    <TextBlock Name="txtAccountStats" Text="📊 统计信息加载中..." Foreground="#FF666666"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Name="btnRefreshAccounts" Content="🔄 刷新" Style="{StaticResource ModernButtonStyle}"
                            Click="BtnRefreshAccounts_Click" Background="#FF6C757D"/>
                    <Button Name="btnOpenAccountFile" Content="📁 打开文件" Style="{StaticResource ModernButtonStyle}"
                            Click="BtnOpenAccountFile_Click" Background="#FF17A2B8"/>
                    <Button Name="btnClose" Content="❌ 关闭" Style="{StaticResource ModernButtonStyle}"
                            Click="BtnClose_Click" Background="#FF6C757D"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
