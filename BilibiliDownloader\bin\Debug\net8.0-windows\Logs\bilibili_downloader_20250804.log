[2025-08-04 16:14:55.912] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 16:14:55.916] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 16:14:55.964] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 16:14:55.964] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 16:14:55.965] [Info] 已加载 7 个平台下载器
[2025-08-04 16:14:55.965] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 16:14:55.968] [Info] 程序初始化完成
[2025-08-04 16:16:55.927] [Info] 检测到平台: 小红书 🌹
[2025-08-04 16:16:55.927] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 16:16:55.929] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 16:16:55.929] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 16:16:55.930] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 16:16:56.545] [Info] 获取到HTML内容，长度: 275573
[2025-08-04 16:16:56.545] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 16:16:57.555] [Info] 开始解析HTML页面数据
[2025-08-04 16:16:57.574] [Info] 找到目标脚本，长度: 8040
[2025-08-04 16:16:57.574] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 16:16:57.574] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 16:16:57.574] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:16:57.575] [Info] 使用正则表达式直接提取note数据
[2025-08-04 16:16:57.575] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:16:57.575] [Info] JSON数据长度: 8015
[2025-08-04 16:16:57.576] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 16:16:57.577] [Info] 修复后JSON长度: 7895
[2025-08-04 16:16:57.577] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 16:16:57.577] [Info] 修复后JSON长度: 7895
[2025-08-04 16:16:57.585] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 16:16:57.585] [Info] 找到note元素
[2025-08-04 16:16:57.586] [Info] 找到note.noteDetailMap路径
[2025-08-04 16:16:57.586] [Info] noteDetailMap类型: Object
[2025-08-04 16:16:57.586] [Info] noteDetailMap属性 1: 'null', 值类型: Object
[2025-08-04 16:16:57.586] [Info] 在属性 'null' 中找到note数据
[2025-08-04 16:16:57.586] [Info] Note数据内容: {}
[2025-08-04 16:16:57.587] [Info] Note数据包含的属性:
[2025-08-04 16:16:57.587] [Info] 处理媒体内容，类型: 
[2025-08-04 16:16:57.587] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 16:16:57.588] [Info] 开始提取视频URL
[2025-08-04 16:16:57.588] [Warning] 未找到video属性
[2025-08-04 16:16:57.588] [Info] 提取到 0 个图片URL
[2025-08-04 16:16:57.588] [Info] 成功提取作品详情
[2025-08-04 16:16:57.589] [Info] 成功解析作品: 
[2025-08-04 16:16:57.592] [Info] 开始获取小红书作品详情: 
[2025-08-04 16:16:58.002] [Info] 获取到HTML内容，长度: 339871
[2025-08-04 16:16:58.002] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 16:16:59.007] [Info] 开始解析HTML页面数据
[2025-08-04 16:16:59.018] [Info] 找到目标脚本，长度: 45684
[2025-08-04 16:16:59.018] [Info] JSON数据长度: 45659
[2025-08-04 16:16:59.018] [Info] JavaScript对象字符串长度: 45659
[2025-08-04 16:16:59.018] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:16:59.018] [Info] 使用正则表达式直接提取note数据
[2025-08-04 16:16:59.018] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 16:16:59.018] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:16:59.018] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 16:16:59.019] [Info] 修复后JSON长度: 45549
[2025-08-04 16:16:59.019] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 16:16:59.019] [Info] 修复后JSON长度: 45549
[2025-08-04 16:16:59.020] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 16:16:59.020] [Info] 找到note元素
[2025-08-04 16:16:59.020] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 16:16:59.020] [Warning] 未找到video属性
[2025-08-04 16:16:59.020] [Info] 开始提取视频URL
[2025-08-04 16:16:59.020] [Info] noteDetailMap属性 1: 'null', 值类型: Object
[2025-08-04 16:16:59.020] [Info] 在属性 'null' 中找到note数据
[2025-08-04 16:16:59.020] [Info] Note数据内容: {}
[2025-08-04 16:16:59.020] [Info] Note数据包含的属性:
[2025-08-04 16:16:59.020] [Info] 找到note.noteDetailMap路径
[2025-08-04 16:16:59.020] [Info] 处理媒体内容，类型: 
[2025-08-04 16:16:59.020] [Info] noteDetailMap类型: Object
[2025-08-04 16:16:59.020] [Info] 提取到 0 个图片URL
[2025-08-04 16:16:59.020] [Info] 成功提取作品详情
[2025-08-04 16:17:00.443] [Info] 开始获取小红书作品详情: 
[2025-08-04 16:17:00.810] [Info] 获取到HTML内容，长度: 340258
[2025-08-04 16:17:00.810] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 16:17:01.820] [Info] 开始解析HTML页面数据
[2025-08-04 16:17:01.824] [Info] 找到目标脚本，长度: 45846
[2025-08-04 16:17:01.824] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 16:17:01.824] [Info] JavaScript对象字符串长度: 45821
[2025-08-04 16:17:01.824] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:17:01.824] [Info] 使用正则表达式直接提取note数据
[2025-08-04 16:17:01.824] [Info] JSON数据长度: 45821
[2025-08-04 16:17:01.824] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:17:01.824] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 16:17:01.824] [Info] 修复后JSON长度: 45711
[2025-08-04 16:17:01.825] [Info] noteDetailMap属性 1: 'null', 值类型: Object
[2025-08-04 16:17:01.825] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 16:17:01.825] [Warning] 未找到video属性
[2025-08-04 16:17:01.825] [Info] 成功提取作品详情
[2025-08-04 16:17:01.825] [Info] 找到note元素
[2025-08-04 16:17:01.825] [Info] 找到note.noteDetailMap路径
[2025-08-04 16:17:01.825] [Info] noteDetailMap类型: Object
[2025-08-04 16:17:01.825] [Info] 在属性 'null' 中找到note数据
[2025-08-04 16:17:01.825] [Info] Note数据内容: {}
[2025-08-04 16:17:01.825] [Info] Note数据包含的属性:
[2025-08-04 16:17:01.824] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 16:17:01.825] [Info] 处理媒体内容，类型: 
[2025-08-04 16:17:01.824] [Info] 修复后JSON长度: 45711
[2025-08-04 16:17:01.825] [Info] 开始提取视频URL
[2025-08-04 16:17:01.825] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 16:17:01.825] [Info] 提取到 0 个图片URL
[2025-08-04 16:17:01.825] [Info] 获取到 0 个下载链接
[2025-08-04 16:30:22.194] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 16:30:22.198] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 16:30:22.249] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 16:30:22.250] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 16:30:22.251] [Info] 已加载 7 个平台下载器
[2025-08-04 16:30:22.252] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 16:30:22.254] [Info] 程序初始化完成
[2025-08-04 16:32:44.530] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 16:32:44.536] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 16:32:44.589] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 16:32:44.589] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 16:32:44.591] [Info] 已加载 7 个平台下载器
[2025-08-04 16:32:44.591] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 16:32:44.594] [Info] 程序初始化完成
[2025-08-04 19:05:42.017] [Info] 检测到平台: 小红书 🌹
[2025-08-04 19:05:42.018] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 19:05:42.019] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 19:05:42.020] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 19:05:42.020] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 19:05:42.647] [Info] 获取到HTML内容，长度: 275572
[2025-08-04 19:05:42.648] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:05:43.651] [Info] 开始解析HTML页面数据
[2025-08-04 19:05:43.666] [Info] 找到目标脚本，长度: 8040
[2025-08-04 19:05:43.666] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:05:43.666] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:05:43.666] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 19:05:43.667] [Info] 使用正则表达式直接提取note数据
[2025-08-04 19:05:43.668] [Info] JSON数据长度: 8015
[2025-08-04 19:05:43.668] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:05:43.668] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:05:43.668] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:05:43.669] [Info] 修复后JSON长度: 7895
[2025-08-04 19:05:43.669] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:05:43.669] [Info] 修复后JSON长度: 7895
[2025-08-04 19:05:43.669] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:05:43.669] [Info] 修复后JSON长度: 7895
[2025-08-04 19:05:43.678] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:05:43.678] [Info] 根JSON对象属性:
[2025-08-04 19:05:43.679] [Info]   - global: Object
[2025-08-04 19:05:43.679] [Info]   - search: Object
[2025-08-04 19:05:43.679] [Info]   - layout: Object
[2025-08-04 19:05:43.679] [Info]   - board: Object
[2025-08-04 19:05:43.679] [Info]   - login: Object
[2025-08-04 19:05:43.680] [Info] 查找属性: note
[2025-08-04 19:05:43.679] [Info]   - user: Object
[2025-08-04 19:05:43.680] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:05:43.679] [Info]   - note: Object
[2025-08-04 19:05:43.679] [Info]   - notification: Object
[2025-08-04 19:05:43.679] [Info]   - nioStore: Object
[2025-08-04 19:05:43.679] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:05:43.680] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:05:43.679] [Info]   - feed: Object
[2025-08-04 19:05:43.680] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:05:43.680] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:05:43.680] [Info] 查找属性: noteDetailMap
[2025-08-04 19:05:43.680] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:05:43.680] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:05:43.679] [Info]   - activity: Object
[2025-08-04 19:05:43.680] [Info] 处理数组索引: -1
[2025-08-04 19:05:43.682] [Info] 对象包含 1 个属性
[2025-08-04 19:05:43.682] [Info] 使用最后一个索引: 0
[2025-08-04 19:05:43.682] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:05:43.682] [Info] 索引后数据类型: Object
[2025-08-04 19:05:43.682] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:05:43.682] [Info] 查找属性: note
[2025-08-04 19:05:43.682] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:05:43.682] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:05:43.682] [Info] 使用标准路径找到note数据
[2025-08-04 19:05:43.683] [Info] Note数据包含的属性:
[2025-08-04 19:05:43.683] [Info] 处理媒体内容，类型: 
[2025-08-04 19:05:43.683] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:05:43.683] [Info] 开始提取视频URL
[2025-08-04 19:05:43.683] [Warning] 未找到video属性
[2025-08-04 19:05:43.684] [Info] 提取到 0 个图片URL
[2025-08-04 19:05:43.684] [Info] 成功提取作品详情
[2025-08-04 19:05:43.685] [Info] 成功解析作品: 
[2025-08-04 19:05:43.688] [Info] 开始获取小红书作品详情: 
[2025-08-04 19:05:44.083] [Info] 获取到HTML内容，长度: 330207
[2025-08-04 19:05:44.084] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:05:45.099] [Info] 开始解析HTML页面数据
[2025-08-04 19:05:45.102] [Info] 找到目标脚本，长度: 41829
[2025-08-04 19:05:45.102] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:05:45.102] [Info] JavaScript对象字符串长度: 41804
[2025-08-04 19:05:45.102] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:05:45.102] [Info] 使用正则表达式直接提取note数据
[2025-08-04 19:05:45.102] [Info] JSON数据长度: 41804
[2025-08-04 19:05:45.102] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:05:45.102] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:05:45.102] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:05:45.103] [Info] 修复后JSON长度: 41694
[2025-08-04 19:05:45.111] [Info]   - feed: Object
[2025-08-04 19:05:45.111] [Info]   - activity: Object
[2025-08-04 19:05:45.111] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:05:45.103] [Info] 修复后JSON长度: 41694
[2025-08-04 19:05:45.111] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:05:45.111] [Info] 查找属性: note
[2025-08-04 19:05:45.111] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:05:45.111] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:05:45.111] [Info] 查找属性: noteDetailMap
[2025-08-04 19:05:45.111] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:05:45.111] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:05:45.111] [Info] 处理数组索引: -1
[2025-08-04 19:05:45.111] [Info] 对象包含 1 个属性
[2025-08-04 19:05:45.111] [Info] 使用最后一个索引: 0
[2025-08-04 19:05:45.111] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:05:45.111] [Info] 索引后数据类型: Object
[2025-08-04 19:05:45.111] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:05:45.111] [Info] 查找属性: note
[2025-08-04 19:05:45.111] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:05:45.111] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:05:45.112] [Info] Note数据包含的属性:
[2025-08-04 19:05:45.112] [Info] 处理媒体内容，类型: 
[2025-08-04 19:05:45.112] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:05:45.112] [Info] 开始提取视频URL
[2025-08-04 19:05:45.112] [Warning] 未找到video属性
[2025-08-04 19:05:45.112] [Info] 提取到 0 个图片URL
[2025-08-04 19:05:45.112] [Info] 成功提取作品详情
[2025-08-04 19:05:45.111] [Info]   - search: Object
[2025-08-04 19:05:45.111] [Info]   - note: Object
[2025-08-04 19:05:45.103] [Info] 修复后JSON长度: 41694
[2025-08-04 19:05:45.111] [Info]   - nioStore: Object
[2025-08-04 19:05:45.111] [Info]   - notification: Object
[2025-08-04 19:05:45.103] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:05:45.112] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:05:45.112] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:05:45.112] [Info] 使用标准路径找到note数据
[2025-08-04 19:05:45.111] [Info] 根JSON对象属性:
[2025-08-04 19:05:45.111] [Info]   - global: Object
[2025-08-04 19:05:45.111] [Info]   - user: Object
[2025-08-04 19:05:45.111] [Info]   - board: Object
[2025-08-04 19:05:45.111] [Info]   - login: Object
[2025-08-04 19:05:45.103] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:05:45.111] [Info]   - layout: Object
[2025-08-04 19:06:01.497] [Info] 开始获取小红书作品详情: 
[2025-08-04 19:06:01.888] [Info] 获取到HTML内容，长度: 351737
[2025-08-04 19:06:01.889] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:06:02.891] [Info] 开始解析HTML页面数据
[2025-08-04 19:06:02.895] [Info] 找到目标脚本，长度: 50057
[2025-08-04 19:06:02.895] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:06:02.895] [Info] JavaScript对象字符串长度: 50032
[2025-08-04 19:06:02.895] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:06:02.896] [Info] 使用正则表达式直接提取note数据
[2025-08-04 19:06:02.896] [Info] JSON数据长度: 50032
[2025-08-04 19:06:02.896] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:06:02.896] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:06:02.897] [Info] 修复后JSON长度: 49922
[2025-08-04 19:06:02.896] [Info] 修复后JSON长度: 49922
[2025-08-04 19:06:02.897] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:06:02.896] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:06:02.897] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:06:02.897] [Info] 修复后JSON长度: 49922
[2025-08-04 19:06:02.898] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:06:02.898] [Info] 根JSON对象属性:
[2025-08-04 19:06:02.898] [Info]   - global: Object
[2025-08-04 19:06:02.898] [Info]   - board: Object
[2025-08-04 19:06:02.898] [Info]   - feed: Object
[2025-08-04 19:06:02.898] [Info]   - user: Object
[2025-08-04 19:06:02.898] [Info]   - login: Object
[2025-08-04 19:06:02.899] [Info]   - layout: Object
[2025-08-04 19:06:02.899] [Info]   - search: Object
[2025-08-04 19:06:02.899] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:06:02.899] [Info]   - note: Object
[2025-08-04 19:06:02.899] [Info]   - nioStore: Object
[2025-08-04 19:06:02.899] [Info]   - notification: Object
[2025-08-04 19:06:02.899] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:06:02.899] [Info]   - activity: Object
[2025-08-04 19:06:02.899] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:06:02.899] [Info] 查找属性: note
[2025-08-04 19:06:02.900] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:06:02.900] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:06:02.900] [Info] 查找属性: noteDetailMap
[2025-08-04 19:06:02.900] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:06:02.901] [Info] 查找属性: note
[2025-08-04 19:06:02.900] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:06:02.900] [Info] 处理数组索引: -1
[2025-08-04 19:06:02.900] [Info] 对象包含 1 个属性
[2025-08-04 19:06:02.900] [Info] 使用最后一个索引: 0
[2025-08-04 19:06:02.901] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:06:02.901] [Info] 索引后数据类型: Object
[2025-08-04 19:06:02.901] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:06:02.901] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:06:02.901] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:06:02.901] [Info] 使用标准路径找到note数据
[2025-08-04 19:06:02.901] [Info] Note数据包含的属性:
[2025-08-04 19:06:02.902] [Info] 获取到 0 个下载链接
[2025-08-04 19:06:02.902] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:06:02.902] [Info] 开始提取视频URL
[2025-08-04 19:06:02.902] [Warning] 未找到video属性
[2025-08-04 19:06:02.902] [Info] 提取到 0 个图片URL
[2025-08-04 19:06:02.902] [Info] 成功提取作品详情
[2025-08-04 19:06:02.902] [Info] 处理媒体内容，类型: 
[2025-08-04 19:15:39.702] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 19:15:39.706] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 19:15:39.758] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 19:15:39.758] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 19:15:39.760] [Info] 已加载 7 个平台下载器
[2025-08-04 19:15:39.760] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 19:15:39.763] [Info] 程序初始化完成
[2025-08-04 19:18:38.301] [Info] 检测到平台: 小红书 🌹
[2025-08-04 19:18:38.302] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 19:18:38.304] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 19:18:38.304] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 19:18:38.304] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 19:18:38.849] [Info] 获取到HTML内容，长度: 275572
[2025-08-04 19:18:38.849] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:18:39.864] [Info] 开始解析HTML页面数据
[2025-08-04 19:18:39.880] [Info] 找到目标脚本，长度: 8040
[2025-08-04 19:18:39.880] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:18:39.880] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:18:39.880] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 19:18:39.881] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:18:39.881] [Info] JSON数据长度: 8015
[2025-08-04 19:18:39.881] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:18:39.881] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:18:39.881] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:18:39.883] [Info] 修复后JSON长度: 7895
[2025-08-04 19:18:39.883] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:18:39.883] [Info] 修复后JSON长度: 7895
[2025-08-04 19:18:39.883] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:18:39.883] [Info] 修复后JSON长度: 7895
[2025-08-04 19:18:39.892] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:18:39.892] [Info] 根JSON对象属性:
[2025-08-04 19:18:39.893] [Info]   - global: Object
[2025-08-04 19:18:39.893] [Info]   - user: Object
[2025-08-04 19:18:39.893] [Info]   - activity: Object
[2025-08-04 19:18:39.893] [Info]   - login: Object
[2025-08-04 19:18:39.893] [Info]   - feed: Object
[2025-08-04 19:18:39.893] [Info]   - layout: Object
[2025-08-04 19:18:39.893] [Info]   - board: Object
[2025-08-04 19:18:39.893] [Info]   - search: Object
[2025-08-04 19:18:39.893] [Info]   - note: Object
[2025-08-04 19:18:39.893] [Info]   - notification: Object
[2025-08-04 19:18:39.893] [Info]   - nioStore: Object
[2025-08-04 19:18:39.893] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:18:39.894] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:18:39.894] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:18:39.894] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:18:39.894] [Info] 查找属性: note
[2025-08-04 19:18:39.894] [Info] 查找属性: noteDetailMap
[2025-08-04 19:18:39.896] [Info] 索引后数据类型: Object
[2025-08-04 19:18:39.894] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:18:39.894] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:18:39.894] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:18:39.894] [Info] 处理数组索引: -1
[2025-08-04 19:18:39.896] [Info] 对象包含 1 个属性
[2025-08-04 19:18:39.896] [Info] 使用最后一个索引: 0
[2025-08-04 19:18:39.897] [Info] Note数据包含的属性:
[2025-08-04 19:18:39.896] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:18:39.896] [Info] 查找属性: note
[2025-08-04 19:18:39.896] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:18:39.896] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:18:39.896] [Info] 使用标准路径找到note数据
[2025-08-04 19:18:39.896] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:18:39.898] [Info] 成功提取作品详情
[2025-08-04 19:18:39.897] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:18:39.897] [Info] 开始提取视频URL
[2025-08-04 19:18:39.898] [Warning] 未找到video属性
[2025-08-04 19:18:39.898] [Info] 提取到 0 个图片URL
[2025-08-04 19:18:39.897] [Info] 处理媒体内容，类型: 
[2025-08-04 19:18:39.899] [Info] 成功解析作品: 
[2025-08-04 19:18:39.902] [Info] 开始获取小红书作品详情: 
[2025-08-04 19:18:40.306] [Info] 获取到HTML内容，长度: 347930
[2025-08-04 19:18:40.307] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:18:41.317] [Info] 开始解析HTML页面数据
[2025-08-04 19:18:41.331] [Info] 找到目标脚本，长度: 48613
[2025-08-04 19:18:41.332] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:18:41.332] [Info] JavaScript对象字符串长度: 48588
[2025-08-04 19:18:41.332] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:18:41.332] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:18:41.332] [Info] JSON数据长度: 48588
[2025-08-04 19:18:41.332] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:18:41.332] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:18:41.332] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:18:41.333] [Info] 修复后JSON长度: 48478
[2025-08-04 19:18:41.333] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:18:41.334] [Info]   - search: Object
[2025-08-04 19:18:41.334] [Info]   - notification: Object
[2025-08-04 19:18:41.333] [Info] 修复后JSON长度: 48478
[2025-08-04 19:18:41.334] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:18:41.334] [Info] 查找属性: note
[2025-08-04 19:18:41.334] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:18:41.334] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:18:41.334] [Info] 查找属性: noteDetailMap
[2025-08-04 19:18:41.334] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:18:41.334] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:18:41.334] [Info] 处理数组索引: -1
[2025-08-04 19:18:41.334] [Info] 对象包含 1 个属性
[2025-08-04 19:18:41.334] [Info] 使用最后一个索引: 0
[2025-08-04 19:18:41.334] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:18:41.334] [Info] 索引后数据类型: Object
[2025-08-04 19:18:41.334] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:18:41.334] [Info] 查找属性: note
[2025-08-04 19:18:41.334] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:18:41.334] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:18:41.334] [Info] 使用标准路径找到note数据
[2025-08-04 19:18:41.334] [Info] Note数据包含的属性:
[2025-08-04 19:18:41.334] [Info] 处理媒体内容，类型: 
[2025-08-04 19:18:41.334] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:18:41.334] [Info] 开始提取视频URL
[2025-08-04 19:18:41.334] [Warning] 未找到video属性
[2025-08-04 19:18:41.334] [Info] 提取到 0 个图片URL
[2025-08-04 19:18:41.334] [Info] 成功提取作品详情
[2025-08-04 19:18:41.334] [Info]   - activity: Object
[2025-08-04 19:18:41.334] [Info]   - note: Object
[2025-08-04 19:18:41.333] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:18:41.334] [Info]   - nioStore: Object
[2025-08-04 19:18:41.334] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:18:41.334] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:18:41.333] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:18:41.333] [Info] 根JSON对象属性:
[2025-08-04 19:18:41.333] [Info]   - global: Object
[2025-08-04 19:18:41.333] [Info]   - user: Object
[2025-08-04 19:18:41.333] [Info]   - board: Object
[2025-08-04 19:18:41.333] [Info]   - login: Object
[2025-08-04 19:18:41.334] [Info]   - feed: Object
[2025-08-04 19:18:41.334] [Info]   - layout: Object
[2025-08-04 19:18:41.333] [Info] 修复后JSON长度: 48478
[2025-08-04 19:18:48.914] [Info] 开始获取小红书作品详情: 
[2025-08-04 19:18:49.324] [Info] 获取到HTML内容，长度: 330549
[2025-08-04 19:18:49.324] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:18:50.338] [Info] 开始解析HTML页面数据
[2025-08-04 19:18:50.341] [Info] 找到目标脚本，长度: 41853
[2025-08-04 19:18:50.341] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:18:50.341] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:18:50.341] [Info] JavaScript对象字符串长度: 41828
[2025-08-04 19:18:50.341] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:18:50.342] [Info] JSON数据长度: 41828
[2025-08-04 19:18:50.342] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:18:50.342] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:18:50.342] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:18:50.342] [Info] 修复后JSON长度: 41718
[2025-08-04 19:18:50.343] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:18:50.343] [Info] 修复后JSON长度: 41718
[2025-08-04 19:18:50.343] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:18:50.343] [Info] 修复后JSON长度: 41718
[2025-08-04 19:18:50.344] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:18:50.344] [Info] 根JSON对象属性:
[2025-08-04 19:18:50.344] [Info]   - user: Object
[2025-08-04 19:18:50.344] [Info]   - global: Object
[2025-08-04 19:18:50.344] [Info]   - board: Object
[2025-08-04 19:18:50.344] [Info]   - login: Object
[2025-08-04 19:18:50.344] [Info]   - feed: Object
[2025-08-04 19:18:50.345] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:18:50.345] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:18:50.345] [Info]   - search: Object
[2025-08-04 19:18:50.346] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:18:50.346] [Info] 查找属性: noteDetailMap
[2025-08-04 19:18:50.345] [Info]   - note: Object
[2025-08-04 19:18:50.345] [Info]   - nioStore: Object
[2025-08-04 19:18:50.346] [Info] 处理数组索引: -1
[2025-08-04 19:18:50.346] [Info] 对象包含 1 个属性
[2025-08-04 19:18:50.347] [Info] 使用最后一个索引: 0
[2025-08-04 19:18:50.345] [Info] 查找属性: note
[2025-08-04 19:18:50.347] [Info] 索引后数据类型: Object
[2025-08-04 19:18:50.345] [Info]   - activity: Object
[2025-08-04 19:18:50.346] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:18:50.346] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:18:50.345] [Info]   - notification: Object
[2025-08-04 19:18:50.348] [Info] 使用标准路径找到note数据
[2025-08-04 19:18:50.345] [Info]   - layout: Object
[2025-08-04 19:18:50.347] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:18:50.346] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:18:50.347] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:18:50.347] [Info] 查找属性: note
[2025-08-04 19:18:50.347] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:18:50.347] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:18:50.345] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:18:50.348] [Info] Note数据包含的属性:
[2025-08-04 19:18:50.348] [Info] 处理媒体内容，类型: 
[2025-08-04 19:18:50.348] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:18:50.348] [Info] 开始提取视频URL
[2025-08-04 19:18:50.348] [Warning] 未找到video属性
[2025-08-04 19:18:50.349] [Info] 提取到 0 个图片URL
[2025-08-04 19:18:50.349] [Info] 成功提取作品详情
[2025-08-04 19:18:50.349] [Info] 获取到 0 个下载链接
[2025-08-04 19:24:11.680] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 19:24:11.684] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 19:24:11.733] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 19:24:11.733] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 19:24:11.734] [Info] 已加载 7 个平台下载器
[2025-08-04 19:24:11.735] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 19:24:11.737] [Info] 程序初始化完成
[2025-08-04 19:25:16.660] [Info] 检测到平台: 小红书 🌹
[2025-08-04 19:25:16.661] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 19:25:16.662] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 19:25:16.663] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 19:25:16.663] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 19:25:17.155] [Info] 获取到HTML内容，长度: 275572
[2025-08-04 19:25:17.155] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:25:18.163] [Info] 开始解析HTML页面数据
[2025-08-04 19:25:18.179] [Info] 找到目标脚本，长度: 8040
[2025-08-04 19:25:18.179] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:25:18.179] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 19:25:18.179] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:25:18.180] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:25:18.180] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:25:18.180] [Info] JSON数据长度: 8015
[2025-08-04 19:25:18.180] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:25:18.181] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:25:18.182] [Info] 修复后JSON长度: 7895
[2025-08-04 19:25:18.182] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:25:18.182] [Info] 修复后JSON长度: 7895
[2025-08-04 19:25:18.182] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:25:18.182] [Info] 修复后JSON长度: 7895
[2025-08-04 19:25:18.191] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:25:18.191] [Info] 根JSON对象属性:
[2025-08-04 19:25:18.192] [Info]   - global: Object
[2025-08-04 19:25:18.192] [Info]   - user: Object
[2025-08-04 19:25:18.192] [Info]   - layout: Object
[2025-08-04 19:25:18.192] [Info]   - activity: Object
[2025-08-04 19:25:18.192] [Info]   - nioStore: Object
[2025-08-04 19:25:18.192] [Info]   - board: Object
[2025-08-04 19:25:18.192] [Info]   - login: Object
[2025-08-04 19:25:18.192] [Info]   - search: Object
[2025-08-04 19:25:18.192] [Info]   - feed: Object
[2025-08-04 19:25:18.193] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:25:18.192] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:25:18.192] [Info]   - notification: Object
[2025-08-04 19:25:18.193] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:25:18.193] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:25:18.193] [Info] 查找属性: note
[2025-08-04 19:25:18.193] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:25:18.193] [Info] 查找属性: noteDetailMap
[2025-08-04 19:25:18.192] [Info]   - note: Object
[2025-08-04 19:25:18.193] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:25:18.193] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:25:18.194] [Info] 处理数组索引: -1
[2025-08-04 19:25:18.195] [Info] 对象包含 1 个属性
[2025-08-04 19:25:18.195] [Info] 使用最后一个索引: 0
[2025-08-04 19:25:18.195] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:25:18.196] [Info] Note数据包含的属性: 
[2025-08-04 19:25:18.195] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:25:18.195] [Info] 查找属性: note
[2025-08-04 19:25:18.195] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:25:18.195] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:25:18.195] [Info] 使用标准路径找到note数据
[2025-08-04 19:25:18.196] [Info] Note数据类型: Object
[2025-08-04 19:25:18.195] [Info] 索引后数据类型: Object
[2025-08-04 19:25:18.196] [Info] Note数据包含的属性:
[2025-08-04 19:25:18.196] [Info] 处理媒体内容，类型: 
[2025-08-04 19:25:18.197] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:25:18.197] [Info] 开始提取视频URL
[2025-08-04 19:25:18.197] [Warning] 未找到video属性
[2025-08-04 19:25:18.197] [Info] 提取到 0 个图片URL
[2025-08-04 19:25:18.197] [Info] 成功提取作品详情
[2025-08-04 19:25:18.198] [Info] 成功解析作品: 
[2025-08-04 19:25:18.202] [Info] 开始获取小红书作品详情: 
[2025-08-04 19:25:18.573] [Info] 获取到HTML内容，长度: 330304
[2025-08-04 19:25:18.573] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:25:19.576] [Info] 开始解析HTML页面数据
[2025-08-04 19:25:19.580] [Info] 找到目标脚本，长度: 41881
[2025-08-04 19:25:19.580] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:25:19.580] [Info] JavaScript对象字符串长度: 41856
[2025-08-04 19:25:19.580] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:25:19.580] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:25:19.580] [Info] JSON数据长度: 41856
[2025-08-04 19:25:19.580] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:25:19.592] [Info]   - board: Object
[2025-08-04 19:25:19.592] [Info]   - feed: Object
[2025-08-04 19:25:19.592] [Info]   - activity: Object
[2025-08-04 19:25:19.592] [Info]   - nioStore: Object
[2025-08-04 19:25:19.592] [Info]   - notification: Object
[2025-08-04 19:25:19.593] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:25:19.593] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:25:19.593] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:25:19.593] [Info] 查找属性: note
[2025-08-04 19:25:19.593] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:25:19.593] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:25:19.593] [Info] 查找属性: noteDetailMap
[2025-08-04 19:25:19.593] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:25:19.593] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:25:19.593] [Info] 处理数组索引: -1
[2025-08-04 19:25:19.593] [Info] 对象包含 1 个属性
[2025-08-04 19:25:19.593] [Info] 使用最后一个索引: 0
[2025-08-04 19:25:19.593] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:25:19.593] [Info] 索引后数据类型: Object
[2025-08-04 19:25:19.581] [Info] 修复后JSON长度: 41746
[2025-08-04 19:25:19.593] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:25:19.593] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:25:19.593] [Info] 使用标准路径找到note数据
[2025-08-04 19:25:19.593] [Info] Note数据类型: Object
[2025-08-04 19:25:19.593] [Info] Note数据包含的属性: 
[2025-08-04 19:25:19.593] [Info] Note数据包含的属性:
[2025-08-04 19:25:19.593] [Info] 处理媒体内容，类型: 
[2025-08-04 19:25:19.593] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:25:19.593] [Info] 开始提取视频URL
[2025-08-04 19:25:19.593] [Warning] 未找到video属性
[2025-08-04 19:25:19.593] [Info] 提取到 0 个图片URL
[2025-08-04 19:25:19.593] [Info] 成功提取作品详情
[2025-08-04 19:25:19.592] [Info]   - note: Object
[2025-08-04 19:25:19.581] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:25:19.593] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:25:19.593] [Info] 查找属性: note
[2025-08-04 19:25:19.581] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:25:19.581] [Info] 修复后JSON长度: 41746
[2025-08-04 19:25:19.592] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:25:19.592] [Info] 根JSON对象属性:
[2025-08-04 19:25:19.592] [Info]   - global: Object
[2025-08-04 19:25:19.580] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:25:19.592] [Info]   - user: Object
[2025-08-04 19:25:19.580] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:25:19.592] [Info]   - login: Object
[2025-08-04 19:25:19.592] [Info]   - layout: Object
[2025-08-04 19:25:19.581] [Info] 修复后JSON长度: 41746
[2025-08-04 19:25:19.592] [Info]   - search: Object
[2025-08-04 19:25:27.327] [Info] 开始获取小红书作品详情: 
[2025-08-04 19:25:27.780] [Info] 获取到HTML内容，长度: 325855
[2025-08-04 19:25:27.780] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:25:28.792] [Info] 开始解析HTML页面数据
[2025-08-04 19:25:28.795] [Info] 找到目标脚本，长度: 40197
[2025-08-04 19:25:28.795] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:25:28.795] [Info] JavaScript对象字符串长度: 40172
[2025-08-04 19:25:28.795] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:25:28.795] [Info] JSON数据长度: 40172
[2025-08-04 19:25:28.796] [Info]   - user: Object
[2025-08-04 19:25:28.796] [Info]   - search: Object
[2025-08-04 19:25:28.796] [Info]   - activity: Object
[2025-08-04 19:25:28.796] [Info]   - note: Object
[2025-08-04 19:25:28.796] [Info]   - nioStore: Object
[2025-08-04 19:25:28.796] [Info]   - notification: Object
[2025-08-04 19:25:28.796] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:25:28.796] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:25:28.796] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:25:28.796] [Info] 查找属性: note
[2025-08-04 19:25:28.796] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:25:28.796] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:25:28.796] [Info] 查找属性: noteDetailMap
[2025-08-04 19:25:28.796] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:25:28.796] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:25:28.796] [Info] 处理数组索引: -1
[2025-08-04 19:25:28.796] [Info] 对象包含 1 个属性
[2025-08-04 19:25:28.796] [Info] 使用最后一个索引: 0
[2025-08-04 19:25:28.796] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:25:28.796] [Info] 索引后数据类型: Object
[2025-08-04 19:25:28.796] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:25:28.796] [Info] 查找属性: note
[2025-08-04 19:25:28.796] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:25:28.796] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:25:28.796] [Info] 使用标准路径找到note数据
[2025-08-04 19:25:28.796] [Info] Note数据类型: Object
[2025-08-04 19:25:28.797] [Info] Note数据包含的属性: 
[2025-08-04 19:25:28.797] [Info] Note数据包含的属性:
[2025-08-04 19:25:28.797] [Info] 处理媒体内容，类型: 
[2025-08-04 19:25:28.797] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:25:28.797] [Info] 开始提取视频URL
[2025-08-04 19:25:28.797] [Warning] 未找到video属性
[2025-08-04 19:25:28.797] [Info] 提取到 0 个图片URL
[2025-08-04 19:25:28.797] [Info] 成功提取作品详情
[2025-08-04 19:25:28.797] [Info] 获取到 0 个下载链接
[2025-08-04 19:25:28.795] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:25:28.795] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:25:28.796] [Info] 修复后JSON长度: 40062
[2025-08-04 19:25:28.796] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:25:28.796] [Info] 修复后JSON长度: 40062
[2025-08-04 19:25:28.796] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:25:28.796] [Info] 修复后JSON长度: 40062
[2025-08-04 19:25:28.796] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:25:28.796] [Info] 根JSON对象属性:
[2025-08-04 19:25:28.796] [Info]   - global: Object
[2025-08-04 19:25:28.796] [Info]   - board: Object
[2025-08-04 19:25:28.796] [Info]   - login: Object
[2025-08-04 19:25:28.796] [Info]   - feed: Object
[2025-08-04 19:25:28.796] [Info]   - layout: Object
[2025-08-04 19:25:28.795] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:25:28.795] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:26:26.949] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 19:26:26.953] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 19:26:27.010] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 19:26:27.010] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 19:26:27.012] [Info] 已加载 7 个平台下载器
[2025-08-04 19:26:27.012] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 19:26:27.016] [Info] 程序初始化完成
[2025-08-04 19:27:28.133] [Info] 检测到平台: 小红书 🌹
[2025-08-04 19:27:28.134] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 19:27:28.135] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 19:27:28.136] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 19:27:28.137] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 19:27:28.692] [Info] 获取到HTML内容，长度: 275572
[2025-08-04 19:27:28.692] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:27:29.708] [Info] 开始解析HTML页面数据
[2025-08-04 19:27:29.723] [Info] 找到目标脚本，长度: 8040
[2025-08-04 19:27:29.724] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:27:29.724] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 19:27:29.724] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:27:29.725] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:27:29.725] [Info] JSON数据长度: 8015
[2025-08-04 19:27:29.725] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:27:29.725] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:27:29.725] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:27:29.727] [Info] 修复后JSON长度: 7895
[2025-08-04 19:27:29.727] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:27:29.727] [Info] 修复后JSON长度: 7895
[2025-08-04 19:27:29.727] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:27:29.727] [Info] 修复后JSON长度: 7895
[2025-08-04 19:27:29.736] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:27:29.737] [Info] 根JSON对象属性:
[2025-08-04 19:27:29.738] [Info]   - global: Object
[2025-08-04 19:27:29.738] [Info]   - feed: Object
[2025-08-04 19:27:29.738] [Info]   - user: Object
[2025-08-04 19:27:29.738] [Info]   - nioStore: Object
[2025-08-04 19:27:29.738] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:27:29.738] [Info]   - layout: Object
[2025-08-04 19:27:29.738] [Info]   - activity: Object
[2025-08-04 19:27:29.738] [Info]   - search: Object
[2025-08-04 19:27:29.738] [Info]   - board: Object
[2025-08-04 19:27:29.738] [Info]   - note: Object
[2025-08-04 19:27:29.738] [Info]   - notification: Object
[2025-08-04 19:27:29.738] [Info]   - login: Object
[2025-08-04 19:27:29.739] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:27:29.739] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:27:29.739] [Info] 查找属性: note
[2025-08-04 19:27:29.739] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:27:29.739] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:27:29.739] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:27:29.741] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:27:29.739] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:27:29.741] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:27:29.741] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:27:29.741] [Info] 使用最后一个索引: 0
[2025-08-04 19:27:29.741] [Info] 查找属性: noteDetailMap
[2025-08-04 19:27:29.741] [Info] 索引后数据类型: Object
[2025-08-04 19:27:29.742] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:27:29.741] [Info] 查找属性: note
[2025-08-04 19:27:29.739] [Info] 查找属性: noteDetailMap
[2025-08-04 19:27:29.741] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:27:29.742] [Info]     null.currentTime: Number
[2025-08-04 19:27:29.741] [Info] Note数据类型: Object
[2025-08-04 19:27:29.743] [Info] Note数据包含的属性:
[2025-08-04 19:27:29.743] [Info] 处理媒体内容，类型: 
[2025-08-04 19:27:29.741] [Warning] Note对象为空，检查noteDetailMap结构
[2025-08-04 19:27:29.743] [Info] 开始提取视频URL
[2025-08-04 19:27:29.743] [Warning] 未找到video属性
[2025-08-04 19:27:29.744] [Info] 提取到 0 个图片URL
[2025-08-04 19:27:29.744] [Info] 成功提取作品详情
[2025-08-04 19:27:29.741] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:27:29.742] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:27:29.741] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:27:29.742] [Info] noteDetailMap结构:
[2025-08-04 19:27:29.742] [Info]   null: Object
[2025-08-04 19:27:29.742] [Info]     null.comments: Object
[2025-08-04 19:27:29.741] [Info] 使用标准路径找到note数据
[2025-08-04 19:27:29.742] [Info]     null.note: Object
[2025-08-04 19:27:29.741] [Info] Note数据包含的属性: 
[2025-08-04 19:27:29.739] [Info] 处理数组索引: -1
[2025-08-04 19:27:29.743] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:27:29.741] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:27:29.741] [Info] 查找属性: note
[2025-08-04 19:27:29.741] [Info] 对象包含 1 个属性
[2025-08-04 19:27:29.741] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:27:29.745] [Info] 成功解析作品: 
[2025-08-04 19:27:29.748] [Info] 开始获取小红书作品详情: 
[2025-08-04 19:27:30.137] [Info] 获取到HTML内容，长度: 344525
[2025-08-04 19:27:30.137] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:27:31.144] [Info] 开始解析HTML页面数据
[2025-08-04 19:27:31.151] [Info] 找到目标脚本，长度: 47461
[2025-08-04 19:27:31.151] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:27:31.151] [Info] JavaScript对象字符串长度: 47436
[2025-08-04 19:27:31.151] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:27:31.151] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:27:31.151] [Info] JSON数据长度: 47436
[2025-08-04 19:27:31.151] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:27:31.151] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:27:31.151] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:27:31.169] [Info] 修复后JSON长度: 47326
[2025-08-04 19:27:31.169] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:27:31.169] [Info] 修复后JSON长度: 47326
[2025-08-04 19:27:31.170] [Info]   - board: Object
[2025-08-04 19:27:31.171] [Info]   - note: Object
[2025-08-04 19:27:31.170] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:27:31.171] [Info] 查找属性: note
[2025-08-04 19:27:31.170] [Info]   - global: Object
[2025-08-04 19:27:31.171] [Info] 查找属性: noteDetailMap
[2025-08-04 19:27:31.171] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:27:31.171] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:27:31.171] [Info] 处理数组索引: -1
[2025-08-04 19:27:31.171] [Info] 对象包含 1 个属性
[2025-08-04 19:27:31.171] [Info] 使用最后一个索引: 0
[2025-08-04 19:27:31.171] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:27:31.171] [Info] 索引后数据类型: Object
[2025-08-04 19:27:31.171] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:27:31.171] [Info] 查找属性: note
[2025-08-04 19:27:31.171] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:27:31.171] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:27:31.171] [Info] 使用标准路径找到note数据
[2025-08-04 19:27:31.171] [Info] Note数据类型: Object
[2025-08-04 19:27:31.171] [Info] Note数据包含的属性: 
[2025-08-04 19:27:31.171] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:27:31.171] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:27:31.171] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:27:31.171] [Info] 查找属性: note
[2025-08-04 19:27:31.171] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:27:31.171] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:27:31.172] [Info] 查找属性: noteDetailMap
[2025-08-04 19:27:31.172] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:27:31.172] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:27:31.172] [Info] noteDetailMap结构:
[2025-08-04 19:27:31.172] [Info]   null: Object
[2025-08-04 19:27:31.172] [Info]     null.comments: Object
[2025-08-04 19:27:31.171] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:27:31.172] [Info]     null.note: Object
[2025-08-04 19:27:31.172] [Info] Note数据包含的属性:
[2025-08-04 19:27:31.172] [Info] 处理媒体内容，类型: 
[2025-08-04 19:27:31.172] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:27:31.172] [Info] 开始提取视频URL
[2025-08-04 19:27:31.172] [Warning] 未找到video属性
[2025-08-04 19:27:31.172] [Info] 提取到 0 个图片URL
[2025-08-04 19:27:31.171] [Info]   - feed: Object
[2025-08-04 19:27:31.171] [Info]   - layout: Object
[2025-08-04 19:27:31.171] [Info]   - search: Object
[2025-08-04 19:27:31.171] [Info]   - activity: Object
[2025-08-04 19:27:31.169] [Info] 修复后JSON长度: 47326
[2025-08-04 19:27:31.171] [Info]   - nioStore: Object
[2025-08-04 19:27:31.171] [Info]   - notification: Object
[2025-08-04 19:27:31.171] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:27:31.172] [Info]     null.currentTime: Number
[2025-08-04 19:27:31.170] [Info] 根JSON对象属性:
[2025-08-04 19:27:31.171] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:27:31.171] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:27:31.171] [Warning] Note对象为空，检查noteDetailMap结构
[2025-08-04 19:27:31.170] [Info]   - user: Object
[2025-08-04 19:27:31.169] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:27:31.170] [Info]   - login: Object
[2025-08-04 19:27:31.172] [Info] 成功提取作品详情
[2025-08-04 19:27:47.093] [Info] 开始获取小红书作品详情: 
[2025-08-04 19:27:47.495] [Info] 获取到HTML内容，长度: 326964
[2025-08-04 19:27:47.495] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:27:48.506] [Info] 开始解析HTML页面数据
[2025-08-04 19:27:48.510] [Info] 找到目标脚本，长度: 40522
[2025-08-04 19:27:48.510] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:27:48.510] [Info] JavaScript对象字符串长度: 40497
[2025-08-04 19:27:48.510] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:27:48.510] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:27:48.510] [Info] JSON数据长度: 40497
[2025-08-04 19:27:48.511] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:27:48.511] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:27:48.511] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:27:48.511] [Info] 修复后JSON长度: 40387
[2025-08-04 19:27:48.511] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:27:48.512] [Info] 修复后JSON长度: 40387
[2025-08-04 19:27:48.512] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:27:48.512] [Info] 修复后JSON长度: 40387
[2025-08-04 19:27:48.513] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:27:48.513] [Info] 根JSON对象属性:
[2025-08-04 19:27:48.513] [Info]   - global: Object
[2025-08-04 19:27:48.513] [Info]   - user: Object
[2025-08-04 19:27:48.513] [Info]   - board: Object
[2025-08-04 19:27:48.513] [Info]   - login: Object
[2025-08-04 19:27:48.513] [Info]   - feed: Object
[2025-08-04 19:27:48.513] [Info]   - layout: Object
[2025-08-04 19:27:48.514] [Info]   - search: Object
[2025-08-04 19:27:48.514] [Info]   - activity: Object
[2025-08-04 19:27:48.514] [Info]   - note: Object
[2025-08-04 19:27:48.514] [Info]   - nioStore: Object
[2025-08-04 19:27:48.514] [Info]   - notification: Object
[2025-08-04 19:27:48.514] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:27:48.515] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:27:48.515] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:27:48.515] [Info] 查找属性: note
[2025-08-04 19:27:48.515] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:27:48.515] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:27:48.516] [Info] 查找属性: noteDetailMap
[2025-08-04 19:27:48.516] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:27:48.516] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:27:48.516] [Info] 处理数组索引: -1
[2025-08-04 19:27:48.516] [Info] 对象包含 1 个属性
[2025-08-04 19:27:48.517] [Info] 使用最后一个索引: 0
[2025-08-04 19:27:48.517] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:27:48.517] [Info] 索引后数据类型: Object
[2025-08-04 19:27:48.517] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:27:48.517] [Info] 查找属性: note
[2025-08-04 19:27:48.517] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:27:48.518] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:27:48.518] [Info] 使用标准路径找到note数据
[2025-08-04 19:27:48.518] [Info] Note数据类型: Object
[2025-08-04 19:27:48.518] [Info] Note数据包含的属性: 
[2025-08-04 19:27:48.518] [Warning] Note对象为空，检查noteDetailMap结构
[2025-08-04 19:27:48.519] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:27:48.519] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:27:48.519] [Info] 查找属性: note
[2025-08-04 19:27:48.519] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:27:48.519] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:27:48.519] [Info] 查找属性: noteDetailMap
[2025-08-04 19:27:48.520] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:27:48.520] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:27:48.520] [Info] noteDetailMap结构:
[2025-08-04 19:27:48.520] [Info]   null: Object
[2025-08-04 19:27:48.520] [Info]     null.comments: Object
[2025-08-04 19:27:48.521] [Info]     null.currentTime: Number
[2025-08-04 19:27:48.521] [Info]     null.note: Object
[2025-08-04 19:27:48.521] [Info] Note数据包含的属性:
[2025-08-04 19:27:48.529] [Info] 处理媒体内容，类型: 
[2025-08-04 19:27:48.530] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:27:48.530] [Info] 开始提取视频URL
[2025-08-04 19:27:48.530] [Warning] 未找到video属性
[2025-08-04 19:27:48.530] [Info] 提取到 0 个图片URL
[2025-08-04 19:27:48.530] [Info] 成功提取作品详情
[2025-08-04 19:27:48.531] [Info] 获取到 0 个下载链接
[2025-08-04 19:32:09.903] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 19:32:09.907] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 19:32:09.957] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 19:32:09.957] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 19:32:09.959] [Info] 已加载 7 个平台下载器
[2025-08-04 19:32:09.959] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 19:32:09.962] [Info] 程序初始化完成
[2025-08-04 19:32:47.343] [Info] 检测到平台: 小红书 🌹
[2025-08-04 19:32:47.344] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 19:32:47.345] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 19:32:47.346] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 19:32:47.346] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 19:32:47.869] [Info] 获取到HTML内容，长度: 275572
[2025-08-04 19:32:47.869] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:32:48.886] [Info] 开始解析HTML页面数据
[2025-08-04 19:32:48.903] [Info] 找到目标脚本，长度: 8040
[2025-08-04 19:32:48.903] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:32:48.903] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:32:48.903] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 19:32:48.904] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:32:48.905] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:32:48.905] [Info] JSON数据长度: 8015
[2025-08-04 19:32:48.905] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:32:48.905] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:32:48.907] [Info] 修复后JSON长度: 7895
[2025-08-04 19:32:48.907] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:32:48.907] [Info] 修复后JSON长度: 7895
[2025-08-04 19:32:48.907] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:32:48.907] [Info] 修复后JSON长度: 7895
[2025-08-04 19:32:48.916] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:32:48.917] [Info] 根JSON对象属性:
[2025-08-04 19:32:48.918] [Info]   - global: Object
[2025-08-04 19:32:48.918] [Info]   - user: Object
[2025-08-04 19:32:48.918] [Info]   - board: Object
[2025-08-04 19:32:48.918] [Info]   - feed: Object
[2025-08-04 19:32:48.918] [Info]   - login: Object
[2025-08-04 19:32:48.918] [Info]   - notification: Object
[2025-08-04 19:32:48.919] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:32:48.918] [Info]   - activity: Object
[2025-08-04 19:32:48.918] [Info]   - note: Object
[2025-08-04 19:32:48.919] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:32:48.918] [Info]   - nioStore: Object
[2025-08-04 19:32:48.918] [Info]   - search: Object
[2025-08-04 19:32:48.919] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:32:48.918] [Info]   - layout: Object
[2025-08-04 19:32:48.919] [Info] 查找属性: note
[2025-08-04 19:32:48.920] [Info] 处理数组索引: -1
[2025-08-04 19:32:48.919] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:32:48.919] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:32:48.919] [Info] 查找属性: noteDetailMap
[2025-08-04 19:32:48.919] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:32:48.920] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:32:48.921] [Info] 对象包含 1 个属性
[2025-08-04 19:32:48.921] [Info] 使用最后一个索引: 0
[2025-08-04 19:32:48.922] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:32:48.922] [Info] 索引后数据类型: Object
[2025-08-04 19:32:48.922] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:32:48.922] [Info] 查找属性: note
[2025-08-04 19:32:48.922] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:32:48.922] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:32:48.922] [Info] 使用标准路径找到note数据
[2025-08-04 19:32:48.922] [Info] Note数据类型: Object
[2025-08-04 19:32:48.923] [Info] Note数据包含的属性: 
[2025-08-04 19:32:48.923] [Warning] Note对象为空，检查noteDetailMap结构
[2025-08-04 19:32:48.923] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:32:48.923] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:32:48.923] [Info] 查找属性: note
[2025-08-04 19:32:48.923] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:32:48.923] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:32:48.923] [Info] 查找属性: noteDetailMap
[2025-08-04 19:32:48.923] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:32:48.923] [Info] noteDetailMap结构:
[2025-08-04 19:32:48.923] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:32:48.923] [Info]   null: Object
[2025-08-04 19:32:48.924] [Info]     null.comments: Object
[2025-08-04 19:32:48.925] [Info] Note数据包含的属性:
[2025-08-04 19:32:48.925] [Info] 处理媒体内容，类型: 
[2025-08-04 19:32:48.924] [Info]     null.note: Object
[2025-08-04 19:32:48.924] [Info] 找到实际的note数据，键: null
[2025-08-04 19:32:48.924] [Info] 实际Note数据包含的属性: 
[2025-08-04 19:32:48.924] [Info]     null.currentTime: Number
[2025-08-04 19:32:48.925] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:32:48.926] [Info] 开始提取视频URL
[2025-08-04 19:32:48.926] [Warning] 未找到video属性
[2025-08-04 19:32:48.926] [Info] 提取到 0 个图片URL
[2025-08-04 19:32:48.927] [Info] 成功提取作品详情
[2025-08-04 19:32:48.929] [Info] 成功解析作品: 
[2025-08-04 19:32:48.934] [Info] 开始获取小红书作品详情: 
[2025-08-04 19:32:49.415] [Info] 获取到HTML内容，长度: 337467
[2025-08-04 19:32:49.416] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:32:50.434] [Info] 开始解析HTML页面数据
[2025-08-04 19:32:50.450] [Info] 找到目标脚本，长度: 44529
[2025-08-04 19:32:50.450] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:32:50.450] [Info] JavaScript对象字符串长度: 44504
[2025-08-04 19:32:50.450] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:32:50.451] [Info] 修复后JSON长度: 44394
[2025-08-04 19:32:50.450] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:32:50.450] [Info] JSON数据长度: 44504
[2025-08-04 19:32:50.450] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:32:50.450] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:32:50.450] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:32:50.452] [Info]   - login: Object
[2025-08-04 19:32:50.452] [Info]   - search: Object
[2025-08-04 19:32:50.452] [Info]   - note: Object
[2025-08-04 19:32:50.452] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:32:50.452] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:32:50.452] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:32:50.452] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:32:50.452] [Info] 查找属性: noteDetailMap
[2025-08-04 19:32:50.452] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:32:50.452] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:32:50.452] [Info] 处理数组索引: -1
[2025-08-04 19:32:50.452] [Info] 对象包含 1 个属性
[2025-08-04 19:32:50.452] [Info]   - layout: Object
[2025-08-04 19:32:50.452] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:32:50.452] [Info] 索引后数据类型: Object
[2025-08-04 19:32:50.452] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:32:50.452] [Info] 查找属性: note
[2025-08-04 19:32:50.453] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:32:50.453] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:32:50.452] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:32:50.453] [Info] Note数据类型: Object
[2025-08-04 19:32:50.453] [Info] Note数据包含的属性: 
[2025-08-04 19:32:50.453] [Warning] Note对象为空，检查noteDetailMap结构
[2025-08-04 19:32:50.453] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:32:50.453] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:32:50.451] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:32:50.453] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:32:50.453] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:32:50.453] [Info] 查找属性: noteDetailMap
[2025-08-04 19:32:50.453] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:32:50.453] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:32:50.453] [Info] noteDetailMap结构:
[2025-08-04 19:32:50.453] [Info]   null: Object
[2025-08-04 19:32:50.453] [Info]     null.comments: Object
[2025-08-04 19:32:50.453] [Info]     null.currentTime: Number
[2025-08-04 19:32:50.453] [Info]     null.note: Object
[2025-08-04 19:32:50.453] [Info] 找到实际的note数据，键: null
[2025-08-04 19:32:50.452] [Info] 根JSON对象属性:
[2025-08-04 19:32:50.453] [Info] Note数据包含的属性:
[2025-08-04 19:32:50.453] [Info] 处理媒体内容，类型: 
[2025-08-04 19:32:50.453] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:32:50.453] [Info] 开始提取视频URL
[2025-08-04 19:32:50.453] [Warning] 未找到video属性
[2025-08-04 19:32:50.453] [Info] 提取到 0 个图片URL
[2025-08-04 19:32:50.453] [Info] 成功提取作品详情
[2025-08-04 19:32:50.451] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:32:50.452] [Info]   - activity: Object
[2025-08-04 19:32:50.452] [Info]   - nioStore: Object
[2025-08-04 19:32:50.451] [Info] 修复后JSON长度: 44394
[2025-08-04 19:32:50.452] [Info]   - notification: Object
[2025-08-04 19:32:50.452] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:32:50.453] [Info] 使用标准路径找到note数据
[2025-08-04 19:32:50.452] [Info] 查找属性: note
[2025-08-04 19:32:50.453] [Info] 实际Note数据包含的属性: 
[2025-08-04 19:32:50.452] [Info]   - global: Object
[2025-08-04 19:32:50.452] [Info]   - user: Object
[2025-08-04 19:32:50.452] [Info]   - board: Object
[2025-08-04 19:32:50.453] [Info] 查找属性: note
[2025-08-04 19:32:50.452] [Info]   - feed: Object
[2025-08-04 19:32:50.451] [Info] 修复后JSON长度: 44394
[2025-08-04 19:32:50.452] [Info] 使用最后一个索引: 0
[2025-08-04 19:33:32.463] [Info] 开始获取小红书作品详情: 
[2025-08-04 19:33:33.082] [Info] 获取到HTML内容，长度: 330305
[2025-08-04 19:33:33.082] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:33:34.094] [Info] 开始解析HTML页面数据
[2025-08-04 19:33:34.100] [Info] 找到目标脚本，长度: 41881
[2025-08-04 19:33:34.100] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:33:34.100] [Info] JavaScript对象字符串长度: 41856
[2025-08-04 19:33:34.101] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:33:34.100] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:33:34.101] [Info] JSON数据长度: 41856
[2025-08-04 19:33:34.101] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:33:34.101] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:33:34.101] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:33:34.102] [Info] 修复后JSON长度: 41746
[2025-08-04 19:33:34.102] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:33:34.102] [Info] 修复后JSON长度: 41746
[2025-08-04 19:33:34.102] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:33:34.103] [Info] 修复后JSON长度: 41746
[2025-08-04 19:33:34.104] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:33:34.104] [Info] 根JSON对象属性:
[2025-08-04 19:33:34.104] [Info]   - global: Object
[2025-08-04 19:33:34.104] [Info]   - user: Object
[2025-08-04 19:33:34.105] [Info]   - board: Object
[2025-08-04 19:33:34.105] [Info]   - login: Object
[2025-08-04 19:33:34.105] [Info]   - feed: Object
[2025-08-04 19:33:34.105] [Info]   - layout: Object
[2025-08-04 19:33:34.105] [Info]   - search: Object
[2025-08-04 19:33:34.105] [Info]   - activity: Object
[2025-08-04 19:33:34.106] [Info]   - note: Object
[2025-08-04 19:33:34.106] [Info]   - nioStore: Object
[2025-08-04 19:33:34.106] [Info]   - notification: Object
[2025-08-04 19:33:34.106] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:33:34.106] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:33:34.107] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:33:34.107] [Info] 查找属性: note
[2025-08-04 19:33:34.107] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:33:34.107] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:33:34.107] [Info] 查找属性: noteDetailMap
[2025-08-04 19:33:34.108] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:33:34.108] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:33:34.108] [Info] 处理数组索引: -1
[2025-08-04 19:33:34.108] [Info] 对象包含 1 个属性
[2025-08-04 19:33:34.108] [Info] 使用最后一个索引: 0
[2025-08-04 19:33:34.109] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:33:34.109] [Info] 索引后数据类型: Object
[2025-08-04 19:33:34.109] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:33:34.109] [Info] 查找属性: note
[2025-08-04 19:33:34.110] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:33:34.110] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:33:34.110] [Info] 使用标准路径找到note数据
[2025-08-04 19:33:34.110] [Info] Note数据类型: Object
[2025-08-04 19:33:34.110] [Info] Note数据包含的属性: 
[2025-08-04 19:33:34.118] [Warning] Note对象为空，检查noteDetailMap结构
[2025-08-04 19:33:34.118] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:33:34.119] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:33:34.119] [Info] 查找属性: note
[2025-08-04 19:33:34.119] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:33:34.119] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:33:34.119] [Info] 查找属性: noteDetailMap
[2025-08-04 19:33:34.119] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:33:34.120] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:33:34.120] [Info] noteDetailMap结构:
[2025-08-04 19:33:34.120] [Info]   null: Object
[2025-08-04 19:33:34.120] [Info]     null.comments: Object
[2025-08-04 19:33:34.120] [Info]     null.currentTime: Number
[2025-08-04 19:33:34.120] [Info]     null.note: Object
[2025-08-04 19:33:34.120] [Info] 找到实际的note数据，键: null
[2025-08-04 19:33:34.121] [Info] 实际Note数据包含的属性: 
[2025-08-04 19:33:34.121] [Info] Note数据包含的属性:
[2025-08-04 19:33:34.121] [Info] 处理媒体内容，类型: 
[2025-08-04 19:33:34.121] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:33:34.121] [Info] 开始提取视频URL
[2025-08-04 19:33:34.121] [Warning] 未找到video属性
[2025-08-04 19:33:34.122] [Info] 提取到 0 个图片URL
[2025-08-04 19:33:34.122] [Info] 成功提取作品详情
[2025-08-04 19:33:34.122] [Info] 获取到 0 个下载链接
