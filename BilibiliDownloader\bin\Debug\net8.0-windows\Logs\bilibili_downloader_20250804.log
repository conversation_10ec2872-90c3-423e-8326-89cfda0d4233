[2025-08-04 00:02:24.113] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 00:02:24.117] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 00:02:24.175] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 00:02:24.175] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 00:02:24.176] [Info] 已加载 7 个平台下载器
[2025-08-04 00:02:24.177] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 00:02:24.180] [Info] 程序初始化完成
[2025-08-04 00:03:13.139] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 00:03:13.143] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 00:03:13.188] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 00:03:13.189] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 00:03:13.189] [Info] 已加载 7 个平台下载器
[2025-08-04 00:03:13.189] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 00:03:13.192] [Info] 程序初始化完成
[2025-08-04 00:03:18.300] [Info] 检测到平台: AcFun 🅰️
[2025-08-04 00:03:18.301] [Info] 解析AcFun视频: https://www.acfun.cn/v/ac47667810
[2025-08-04 00:04:07.445] [Info] 检测到平台: 哔哩哔哩 📺
[2025-08-04 00:04:07.446] [Info] 解析B站视频: https://www.bilibili.com/video/BV15D4y1U7j5/?spm_id_from=333.337.search-card.all.click&vd_source=65708e9424b5f3409807c8675f4da097
[2025-08-04 00:04:07.448] [Info] 开始解析视频链接: https://www.bilibili.com/video/BV15D4y1U7j5/?spm_id_from=333.337.search-card.all.click&vd_source=65708e9424b5f3409807c8675f4da097
[2025-08-04 00:04:07.449] [Info] 开始解析URL: https://www.bilibili.com/video/BV15D4y1U7j5/?spm_id_from=333.337.search-card.all.click&vd_source=65708e9424b5f3409807c8675f4da097
[2025-08-04 00:04:07.452] [Info] 提取到BV号: BV15D4y1U7j5
[2025-08-04 00:04:07.452] [Info] 提取到视频ID: BV15D4y1U7j5, 页面: 1
[2025-08-04 00:04:07.698] [Info] 获取视频基本信息成功: 【中字】Godot 3.2像素风ARPG制作教程（全集）
[2025-08-04 00:04:07.699] [Info] 检测到合集视频，合集标题: 【中字】Godot 3.2像素风ARPG制作教程（全集）
[2025-08-04 00:04:07.699] [Info] 当前页面标题: 最后成果、资源导入、项目设置、角色移动基础 (P1)
[2025-08-04 00:04:07.840] [Info] 选择页面 1，CID: 219439520
[2025-08-04 00:04:07.840] [Info] 请求播放信息URL: https://api.bilibili.com/x/player/playurl?bvid=BV15D4y1U7j5&cid=219439520&qn=120&fnval=4048&fourk=1&try_look=1
[2025-08-04 00:04:08.013] [Info] 播放信息API响应: code=0, message=0
[2025-08-04 00:04:08.013] [Info] 获取视频播放信息成功
[2025-08-04 00:04:08.017] [Info] 开始合并视频播放信息，API响应码: 0
[2025-08-04 00:04:08.017] [Info] 播放信息数据获取成功，开始解析清晰度
[2025-08-04 00:04:08.018] [Info] 检测到DASH格式，开始解析DASH清晰度
[2025-08-04 00:04:08.020] [Info] 开始解析DASH清晰度信息
[2025-08-04 00:04:08.021] [Info] 找到 12 个视频流
[2025-08-04 00:04:08.023] [Info] 解析视频流: ID=80, 名称=1080P
[2025-08-04 00:04:08.028] [Info] 为清晰度 1080P 找到音频流
[2025-08-04 00:04:08.029] [Info] 成功添加清晰度选项: 1080P
[2025-08-04 00:04:08.029] [Info] 解析视频流: ID=80, 名称=1080P
[2025-08-04 00:04:08.029] [Info] 成功添加清晰度选项: 1080P
[2025-08-04 00:04:08.029] [Info] 为清晰度 1080P 找到音频流
[2025-08-04 00:04:08.029] [Info] 成功添加清晰度选项: 1080P
[2025-08-04 00:04:08.029] [Info] 解析视频流: ID=80, 名称=1080P
[2025-08-04 00:04:08.029] [Info] 为清晰度 1080P 找到音频流
[2025-08-04 00:04:08.029] [Info] 解析视频流: ID=64, 名称=720P
[2025-08-04 00:04:08.029] [Info] 为清晰度 720P 找到音频流
[2025-08-04 00:04:08.029] [Info] 成功添加清晰度选项: 720P
[2025-08-04 00:04:08.029] [Info] 解析视频流: ID=64, 名称=720P
[2025-08-04 00:04:08.029] [Info] 为清晰度 720P 找到音频流
[2025-08-04 00:04:08.030] [Info] 成功添加清晰度选项: 720P
[2025-08-04 00:04:08.031] [Info] 成功添加清晰度选项: 360P
[2025-08-04 00:04:08.030] [Info] 为清晰度 720P 找到音频流
[2025-08-04 00:04:08.030] [Info] 成功添加清晰度选项: 720P
[2025-08-04 00:04:08.032] [Info] 解析视频流: ID=16, 名称=360P
[2025-08-04 00:04:08.032] [Info] 为清晰度 360P 找到音频流
[2025-08-04 00:04:08.032] [Info] 成功添加清晰度选项: 360P
[2025-08-04 00:04:08.032] [Info] 清晰度解析完成，共找到 12 个清晰度选项
[2025-08-04 00:04:08.032] [Info] 视频信息解析完成，可用清晰度: 12
[2025-08-04 00:04:08.030] [Info] 成功添加清晰度选项: 480P
[2025-08-04 00:04:08.031] [Info] 解析视频流: ID=32, 名称=480P
[2025-08-04 00:04:08.031] [Info] 为清晰度 480P 找到音频流
[2025-08-04 00:04:08.031] [Info] 成功添加清晰度选项: 480P
[2025-08-04 00:04:08.031] [Info] 解析视频流: ID=16, 名称=360P
[2025-08-04 00:04:08.031] [Info] 为清晰度 360P 找到音频流
[2025-08-04 00:04:08.030] [Info] 解析视频流: ID=64, 名称=720P
[2025-08-04 00:04:08.031] [Info] 解析视频流: ID=16, 名称=360P
[2025-08-04 00:04:08.031] [Info] 为清晰度 360P 找到音频流
[2025-08-04 00:04:08.032] [Info] 成功添加清晰度选项: 360P
[2025-08-04 00:04:08.030] [Info] 解析视频流: ID=32, 名称=480P
[2025-08-04 00:04:08.030] [Info] 为清晰度 480P 找到音频流
[2025-08-04 00:04:08.030] [Info] 成功添加清晰度选项: 480P
[2025-08-04 00:04:08.030] [Info] 解析视频流: ID=32, 名称=480P
[2025-08-04 00:04:08.030] [Info] 为清晰度 480P 找到音频流
[2025-08-04 00:04:08.114] [Info] 选择页面 1，CID: 219439520
[2025-08-04 00:04:08.114] [Info] 请求播放信息URL: https://api.bilibili.com/x/player/playurl?bvid=BV15D4y1U7j5&cid=219439520&qn=120&fnval=4048&fourk=1&try_look=1
[2025-08-04 00:04:08.248] [Info] 播放信息API响应: code=0, message=0
[2025-08-04 00:04:28.352] [Info] 检测到平台: AcFun 🅰️
[2025-08-04 00:04:28.353] [Info] 解析AcFun视频: https://www.acfun.cn/v/ac47667810
[2025-08-04 00:10:59.427] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 00:10:59.431] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 00:10:59.480] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 00:10:59.480] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 00:10:59.480] [Info] 已加载 7 个平台下载器
[2025-08-04 00:10:59.480] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 00:10:59.483] [Info] 程序初始化完成
[2025-08-04 02:45:34.399] [Info] 检测到平台: AcFun 🅰️
[2025-08-04 02:45:34.401] [Info] 解析AcFun视频: https://www.acfun.cn/v/ac47667810
[2025-08-04 02:47:02.024] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 02:47:02.028] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 02:47:02.075] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 02:47:02.075] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 02:47:02.075] [Info] 已加载 7 个平台下载器
[2025-08-04 02:47:02.076] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 02:47:02.079] [Info] 程序初始化完成
[2025-08-04 02:47:04.370] [Info] 检测到平台: 小红书 📖
[2025-08-04 02:48:11.642] [Info] 已保存 1 个账号
[2025-08-04 02:48:14.687] [Info] 已保存 1 个账号
[2025-08-04 02:48:18.065] [Info] 检测到平台: 小红书 📖
[2025-08-04 02:48:18.067] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/64434a740000000027029c8d?xsec_token=AB_8NHkEaApmLpq5oKcQ57Pc75X0-zLcdM5AeXW31eLxQ=&xsec_source=pc_feed
[2025-08-04 02:48:18.067] [Error] 小红书解析失败: 小红书需要登录才能解析内容，请在账号管理中添加小红书账号
[2025-08-04 02:48:29.804] [Info] 已保存 1 个账号
[2025-08-04 02:48:35.157] [Info] 已保存 0 个账号
[2025-08-04 02:50:38.415] [Info] 已保存 1 个账号
[2025-08-04 02:50:41.090] [Info] 已保存 1 个账号
[2025-08-04 02:50:44.045] [Info] 已保存 1 个账号
[2025-08-04 02:50:47.431] [Info] 检测到平台: 小红书 📖
[2025-08-04 02:50:47.431] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/64434a740000000027029c8d?xsec_token=AB_8NHkEaApmLpq5oKcQ57Pc75X0-zLcdM5AeXW31eLxQ=&xsec_source=pc_feed
[2025-08-04 02:50:47.431] [Error] 小红书解析失败: 小红书需要登录才能解析内容，请在账号管理中添加小红书账号
[2025-08-04 02:52:07.817] [Info] 检测到平台: 抖音 🎵
[2025-08-04 02:52:07.818] [Info] 解析抖音视频: https://www.douyin.com/video/7528981403955334434
[2025-08-04 02:57:21.767] [Info] 已加载 1 个账号
[2025-08-04 02:57:21.828] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 02:57:21.829] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 02:57:21.882] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 02:57:21.882] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 02:57:21.882] [Info] 已加载 7 个平台下载器
[2025-08-04 02:57:21.883] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 02:57:21.885] [Info] 程序初始化完成
[2025-08-04 02:58:03.580] [Info] 已保存 0 个账号
[2025-08-04 02:58:27.076] [Info] 已保存 1 个账号
[2025-08-04 02:58:29.499] [Info] 已保存 1 个账号
[2025-08-04 02:58:31.902] [Info] 已保存 1 个账号
[2025-08-04 02:58:38.663] [Info] 检测到平台: 抖音 🎵
[2025-08-04 02:58:38.664] [Info] 解析抖音视频: https://www.douyin.com/video/7528981403955334434
[2025-08-04 02:59:34.855] [Info] 检测到平台: 小红书 📖
[2025-08-04 02:59:34.856] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688f7bb800000000040032b1?xsec_token=ABThFRTuXqTQmEbN7l7mv4tLI0qpNzmAFCmHhDz21xlro=&xsec_source=pc_feed
[2025-08-04 02:59:34.856] [Info] 获取小红书Cookie的方法：
[2025-08-04 02:59:34.856] [Warning] 小红书需要登录才能解析内容
[2025-08-04 02:59:34.856] [Info] 6. 在账号管理中添加小红书账号，粘贴Cookie
[2025-08-04 02:59:34.856] [Info] 3. 登录后按F12打开开发者工具
[2025-08-04 02:59:34.856] [Info] 2. 使用手机号验证码或扫码登录
[2025-08-04 02:59:34.856] [Info] 4. 切换到Network标签，刷新页面
[2025-08-04 02:59:34.856] [Info] 1. 打开浏览器，访问 https://www.xiaohongshu.com
[2025-08-04 02:59:34.856] [Info] 5. 找到任意请求，复制Cookie值
[2025-08-04 02:59:34.856] [Info] 尝试无登录解析（成功率较低）...
[2025-08-04 03:05:04.205] [Info] 已加载 1 个账号
[2025-08-04 03:05:04.265] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 03:05:04.267] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 03:05:04.316] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 03:05:04.316] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 03:05:04.317] [Info] 已加载 7 个平台下载器
[2025-08-04 03:05:04.317] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 03:05:04.320] [Info] 程序初始化完成
[2025-08-04 03:08:59.106] [Info] 检测到平台: 小红书 📖
[2025-08-04 03:08:59.107] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688f7bb800000000040032b1?xsec_token=ABThFRTuXqTQmEbN7l7mv4tLI0qpNzmAFCmHhDz21xlro=&xsec_source=pc_feed
[2025-08-04 03:08:59.108] [Warning] 小红书需要登录才能解析内容
[2025-08-04 03:08:59.108] [Info] 获取小红书Cookie的方法：
[2025-08-04 03:08:59.108] [Info] 1. 打开浏览器，访问 https://www.xiaohongshu.com
[2025-08-04 03:08:59.108] [Info] 2. 使用手机号验证码或扫码登录
[2025-08-04 03:08:59.108] [Info] 4. 切换到Network标签，刷新页面
[2025-08-04 03:08:59.108] [Info] 3. 登录后按F12打开开发者工具
[2025-08-04 03:08:59.108] [Info] 5. 找到任意请求，复制Cookie值
[2025-08-04 03:08:59.108] [Info] 尝试无登录解析（成功率较低）...
[2025-08-04 03:08:59.108] [Info] 6. 在账号管理中添加小红书账号，粘贴Cookie
[2025-08-04 03:13:41.151] [Info] 已加载 1 个账号
[2025-08-04 03:13:41.212] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 03:13:41.213] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 03:13:41.264] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 03:13:41.264] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 03:13:41.264] [Info] 已加载 7 个平台下载器
[2025-08-04 03:13:41.265] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 03:13:41.267] [Info] 程序初始化完成
[2025-08-04 03:13:51.887] [Info] 检测到平台: 小红书 📖
[2025-08-04 03:13:51.888] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688f7bb800000000040032b1?xsec_token=ABThFRTuXqTQmEbN7l7mv4tLI0qpNzmAFCmHhDz21xlro=&xsec_source=pc_feed
[2025-08-04 03:13:51.888] [Info] 1. 打开浏览器，访问 https://www.xiaohongshu.com
[2025-08-04 03:13:51.888] [Info] 4. 切换到Network标签，刷新页面
[2025-08-04 03:13:51.888] [Info] 获取小红书Cookie的方法：
[2025-08-04 03:13:51.888] [Info] 2. 使用手机号验证码或扫码登录
[2025-08-04 03:13:51.888] [Warning] 小红书需要登录才能解析内容
[2025-08-04 03:13:51.888] [Info] 3. 登录后按F12打开开发者工具
[2025-08-04 03:13:51.888] [Info] 5. 找到任意请求，复制Cookie值
[2025-08-04 03:13:51.888] [Info] 尝试无登录解析（成功率较低）...
[2025-08-04 03:13:51.888] [Info] 6. 在账号管理中添加小红书账号，粘贴Cookie
[2025-08-04 03:14:02.871] [Error] 获取小红书下载链接失败: 需要登录小红书账号
[2025-08-04 03:14:29.232] [Info] 已保存 1 个账号
[2025-08-04 03:18:39.844] [Info] 已加载 1 个账号
[2025-08-04 03:18:39.901] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 03:18:39.903] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 03:18:39.952] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 03:18:39.952] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 03:18:39.952] [Info] 已加载 7 个平台下载器
[2025-08-04 03:18:39.953] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 03:18:39.955] [Info] 程序初始化完成
[2025-08-04 03:20:06.214] [Info] 检测到平台: 小红书 📖
[2025-08-04 03:20:06.215] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688f7bb800000000040032b1?xsec_token=ABThFRTuXqTQmEbN7l7mv4tLI0qpNzmAFCmHhDz21xlro=&xsec_source=pc_feed
[2025-08-04 03:20:06.215] [Info] 1. 打开浏览器，访问 https://www.xiaohongshu.com
[2025-08-04 03:20:06.215] [Info] 获取小红书Cookie的方法：
[2025-08-04 03:20:06.215] [Info] 5. 找到任意请求，复制Cookie值
[2025-08-04 03:20:06.215] [Warning] 小红书需要登录才能解析内容
[2025-08-04 03:20:06.215] [Info] 2. 使用手机号验证码或扫码登录
[2025-08-04 03:20:06.215] [Info] 4. 切换到Network标签，刷新页面
[2025-08-04 03:20:06.215] [Info] 3. 登录后按F12打开开发者工具
[2025-08-04 03:20:06.215] [Info] 6. 在账号管理中添加小红书账号，粘贴Cookie
[2025-08-04 03:20:06.215] [Info] 尝试无登录解析（成功率较低）...
[2025-08-04 03:20:13.987] [Error] 获取小红书下载链接失败: 需要登录小红书账号
[2025-08-04 03:21:10.935] [Info] 已加载 1 个账号
[2025-08-04 03:21:10.993] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 03:21:10.995] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 03:21:11.045] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 03:21:11.045] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 03:21:11.045] [Info] 已加载 7 个平台下载器
[2025-08-04 03:21:11.046] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 03:21:11.049] [Info] 程序初始化完成
[2025-08-04 03:21:16.941] [Info] 检测到平台: 小红书 📖
[2025-08-04 03:21:16.942] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688f7bb800000000040032b1?xsec_token=ABThFRTuXqTQmEbN7l7mv4tLI0qpNzmAFCmHhDz21xlro=&xsec_source=pc_feed
[2025-08-04 03:21:16.942] [Warning] 小红书需要登录才能解析内容
[2025-08-04 03:21:16.942] [Info] 4. 切换到Network标签，刷新页面
[2025-08-04 03:21:16.942] [Info] 5. 找到任意请求，复制Cookie值
[2025-08-04 03:21:16.942] [Info] 1. 打开浏览器，访问 https://www.xiaohongshu.com
[2025-08-04 03:21:16.942] [Info] 获取小红书Cookie的方法：
[2025-08-04 03:21:16.942] [Info] 3. 登录后按F12打开开发者工具
[2025-08-04 03:21:16.942] [Info] 6. 在账号管理中添加小红书账号，粘贴Cookie
[2025-08-04 03:21:16.942] [Info] 2. 使用手机号验证码或扫码登录
[2025-08-04 03:21:16.942] [Info] 尝试无登录解析（成功率较低）...
[2025-08-04 03:23:57.710] [Info] 已加载 1 个账号
[2025-08-04 03:24:11.805] [Info] 已保存 0 个账号
[2025-08-04 03:24:23.263] [Info] 已保存 1 个账号
[2025-08-04 03:24:27.562] [Info] 检测到平台: 小红书 📖
[2025-08-04 03:24:27.562] [Warning] 小红书需要登录才能解析内容
[2025-08-04 03:24:27.563] [Info] 2. 使用手机号验证码或扫码登录
[2025-08-04 03:24:27.563] [Info] 5. 找到任意请求，复制Cookie值
[2025-08-04 03:24:27.562] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688f7bb800000000040032b1?xsec_token=ABThFRTuXqTQmEbN7l7mv4tLI0qpNzmAFCmHhDz21xlro=&xsec_source=pc_feed
[2025-08-04 03:24:27.563] [Info] 1. 打开浏览器，访问 https://www.xiaohongshu.com
[2025-08-04 03:24:27.563] [Info] 3. 登录后按F12打开开发者工具
[2025-08-04 03:24:27.562] [Info] 获取小红书Cookie的方法：
[2025-08-04 03:24:27.563] [Info] 4. 切换到Network标签，刷新页面
[2025-08-04 03:24:27.563] [Info] 6. 在账号管理中添加小红书账号，粘贴Cookie
[2025-08-04 03:24:27.563] [Info] 尝试无登录解析（成功率较低）...
[2025-08-04 03:24:29.760] [Error] 获取小红书下载链接失败: 需要登录小红书账号
[2025-08-04 03:24:50.396] [Info] 已保存 0 个账号
[2025-08-04 03:25:16.187] [Info] 已保存 1 个账号
[2025-08-04 03:26:26.321] [Info] 已加载 1 个账号
[2025-08-04 03:26:26.380] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 03:26:26.382] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 03:26:26.430] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 03:26:26.430] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 03:26:26.431] [Info] 已加载 7 个平台下载器
[2025-08-04 03:26:26.431] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 03:26:26.434] [Info] 程序初始化完成
[2025-08-04 03:27:08.861] [Info] 检测到平台: 小红书 📖
[2025-08-04 03:27:08.862] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688f7bb800000000040032b1?xsec_token=ABThFRTuXqTQmEbN7l7mv4tLI0qpNzmAFCmHhDz21xlro=&xsec_source=pc_feed
[2025-08-04 03:27:08.863] [Warning] 小红书需要登录才能解析内容
[2025-08-04 03:27:08.863] [Info] 3. 登录后按F12打开开发者工具
[2025-08-04 03:27:08.863] [Info] 获取小红书Cookie的方法：
[2025-08-04 03:27:08.863] [Info] 2. 使用手机号验证码或扫码登录
[2025-08-04 03:27:08.863] [Info] 4. 切换到Network标签，刷新页面
[2025-08-04 03:27:08.863] [Info] 1. 打开浏览器，访问 https://www.xiaohongshu.com
[2025-08-04 03:27:08.863] [Info] 5. 找到任意请求，复制Cookie值
[2025-08-04 03:27:08.863] [Info] 6. 在账号管理中添加小红书账号，粘贴Cookie
[2025-08-04 03:27:08.863] [Info] 尝试无登录解析（成功率较低）...
[2025-08-04 03:34:50.956] [Info] 已加载 1 个账号
[2025-08-04 03:34:51.015] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 03:34:51.017] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 03:34:51.065] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 03:34:51.065] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 03:34:51.067] [Info] 已加载 7 个平台下载器
[2025-08-04 03:34:51.067] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 03:34:51.069] [Info] 程序初始化完成
[2025-08-04 03:35:48.311] [Info] 已保存 1 个账号
[2025-08-04 03:35:51.067] [Info] 已保存 0 个账号
[2025-08-04 03:46:26.974] [Info] 已加载 0 个账号
[2025-08-04 03:46:27.031] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 03:46:27.032] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 03:46:27.078] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 03:46:27.078] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 03:46:27.079] [Info] 已加载 7 个平台下载器
[2025-08-04 03:46:27.079] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 03:46:27.082] [Info] 程序初始化完成
[2025-08-04 08:39:34.017] [Warning] 小红书Cookie无效或已过期
[2025-08-04 08:40:07.507] [Warning] 小红书Cookie无效或已过期
[2025-08-04 08:48:29.557] [Warning] 小红书Cookie无效或已过期
[2025-08-04 08:48:47.721] [Warning] 小红书Cookie无效或已过期
[2025-08-04 08:48:53.385] [Warning] 小红书Cookie无效或已过期
[2025-08-04 08:50:11.246] [Warning] 小红书Cookie无效或已过期
[2025-08-04 08:50:13.801] [Warning] 小红书Cookie无效或已过期
[2025-08-04 08:50:45.016] [Warning] 小红书Cookie无效或已过期
[2025-08-04 08:52:35.623] [Warning] 小红书Cookie无效或已过期
[2025-08-04 08:54:07.592] [Warning] 小红书Cookie无效或已过期
[2025-08-04 09:00:06.065] [Warning] 小红书Cookie无效或已过期
[2025-08-04 09:00:57.567] [Warning] 小红书Cookie无效或已过期
[2025-08-04 09:03:41.242] [Info] 已加载 0 个账号
[2025-08-04 09:03:41.302] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 09:03:41.304] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 09:03:41.351] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 09:03:41.351] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 09:03:41.352] [Info] 已加载 7 个平台下载器
[2025-08-04 09:03:41.353] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 09:03:41.356] [Info] 程序初始化完成
[2025-08-04 09:04:28.579] [Info] 已保存 1 个账号
[2025-08-04 09:04:31.986] [Info] 已保存 1 个账号
[2025-08-04 09:04:38.498] [Info] 开始验证小红书Cookie...
[2025-08-04 09:04:38.498] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/selfinfo
[2025-08-04 09:04:38.673] [Info] 响应状态: InternalServerError
[2025-08-04 09:04:38.673] [Info] 响应内容: create invoker failed, service: jarvis-gateway-default...
[2025-08-04 09:04:38.673] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/selfinfo
[2025-08-04 09:04:38.736] [Info] 响应状态: InternalServerError
[2025-08-04 09:04:38.737] [Info] 响应内容: create invoker failed, service: jarvis-gateway-default...
[2025-08-04 09:04:38.737] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/profile
[2025-08-04 09:04:38.800] [Info] 响应状态: InternalServerError
[2025-08-04 09:04:38.800] [Info] 响应内容: create invoker failed, service: jarvis-gateway-default...
[2025-08-04 09:04:38.800] [Warning] 所有验证端点都失败，Cookie可能无效
[2025-08-04 09:04:38.800] [Warning] 小红书Cookie无效或已过期
[2025-08-04 09:06:59.407] [Info] 已加载 1 个账号
[2025-08-04 09:06:59.465] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 09:06:59.466] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 09:06:59.515] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 09:06:59.515] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 09:06:59.516] [Info] 已加载 7 个平台下载器
[2025-08-04 09:06:59.517] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 09:06:59.519] [Info] 程序初始化完成
[2025-08-04 09:08:33.327] [Info] 开始验证小红书Cookie...
[2025-08-04 09:08:33.327] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/selfinfo
[2025-08-04 09:08:33.535] [Info] 响应状态: InternalServerError
[2025-08-04 09:08:33.535] [Info] 响应内容: create invoker failed, service: jarvis-gateway-default...
[2025-08-04 09:08:33.535] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/selfinfo
[2025-08-04 09:08:33.598] [Info] 响应状态: InternalServerError
[2025-08-04 09:08:33.598] [Info] 响应内容: create invoker failed, service: jarvis-gateway-default...
[2025-08-04 09:08:33.598] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/profile
[2025-08-04 09:08:33.661] [Info] 响应状态: InternalServerError
[2025-08-04 09:08:33.661] [Info] 响应内容: create invoker failed, service: jarvis-gateway-default...
[2025-08-04 09:08:33.661] [Warning] 所有验证端点都失败，Cookie可能无效
[2025-08-04 09:08:33.661] [Warning] 小红书Cookie无效或已过期
[2025-08-04 09:09:29.956] [Info] 开始验证小红书Cookie...
[2025-08-04 09:09:29.957] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/selfinfo
[2025-08-04 09:09:30.019] [Info] 响应状态: InternalServerError
[2025-08-04 09:09:30.020] [Info] 响应内容: create invoker failed, service: jarvis-gateway-default...
[2025-08-04 09:09:30.020] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/selfinfo
[2025-08-04 09:09:30.081] [Info] 响应状态: InternalServerError
[2025-08-04 09:09:30.082] [Info] 响应内容: create invoker failed, service: jarvis-gateway-default...
[2025-08-04 09:09:30.082] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/profile
[2025-08-04 09:09:30.147] [Info] 响应状态: InternalServerError
[2025-08-04 09:09:30.147] [Info] 响应内容: create invoker failed, service: jarvis-gateway-default...
[2025-08-04 09:09:30.147] [Warning] 所有验证端点都失败，Cookie可能无效
[2025-08-04 09:09:30.147] [Warning] 小红书Cookie无效或已过期
[2025-08-04 09:18:15.535] [Info] 已加载 1 个账号
[2025-08-04 09:18:15.594] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 09:18:15.596] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 09:18:15.643] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 09:18:15.644] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 09:18:15.645] [Info] 已加载 7 个平台下载器
[2025-08-04 09:18:15.645] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 09:18:15.647] [Info] 程序初始化完成
[2025-08-04 09:19:24.689] [Info] 开始验证小红书Cookie...
[2025-08-04 09:19:24.689] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:19:24.689] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/selfinfo
[2025-08-04 09:19:24.953] [Info] 响应状态码: InternalServerError
[2025-08-04 09:19:24.955] [Info] 响应头: Date=Mon, 04 Aug 2025 01:19:24 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Connection=keep-alive, Server=openresty, X-Kong-Upstream-Latency=4, X-Kong-Proxy-Latency=5, Via=kong/1.2.1, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, xhs-real-ip=*************
[2025-08-04 09:19:24.955] [Info] 响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:19:24.955] [Warning] 请求失败，状态码: InternalServerError
[2025-08-04 09:19:24.955] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/profile
[2025-08-04 09:19:25.022] [Info] 响应状态码: InternalServerError
[2025-08-04 09:19:25.023] [Info] 响应头: Date=Mon, 04 Aug 2025 01:19:24 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Connection=keep-alive, Server=openresty, X-Kong-Upstream-Latency=4, X-Kong-Proxy-Latency=5, Via=kong/1.2.1, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, xhs-real-ip=*************
[2025-08-04 09:19:25.023] [Info] 响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:19:25.023] [Warning] 请求失败，状态码: InternalServerError
[2025-08-04 09:19:25.023] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/homefeed
[2025-08-04 09:19:25.087] [Info] 响应状态码: InternalServerError
[2025-08-04 09:19:25.088] [Info] 响应头: Date=Mon, 04 Aug 2025 01:19:24 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Connection=keep-alive, Server=openresty, X-Kong-Upstream-Latency=3, X-Kong-Proxy-Latency=4, Via=kong/1.2.1, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, xhs-real-ip=*************
[2025-08-04 09:19:25.088] [Info] 响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:19:25.088] [Warning] 请求失败，状态码: InternalServerError
[2025-08-04 09:19:25.088] [Warning] 所有验证端点都失败，Cookie可能无效
[2025-08-04 09:19:25.088] [Warning] 小红书Cookie无效或已过期
[2025-08-04 09:22:30.293] [Info] 已加载 1 个账号
[2025-08-04 09:22:30.352] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 09:22:30.354] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 09:22:30.402] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 09:22:30.402] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 09:22:30.403] [Info] 已加载 7 个平台下载器
[2025-08-04 09:22:30.404] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 09:22:30.406] [Info] 程序初始化完成
[2025-08-04 09:22:43.566] [Info] 开始验证小红书Cookie...
[2025-08-04 09:22:43.566] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:22:43.566] [Info] 尝试验证端点: https://www.xiaohongshu.com/
[2025-08-04 09:22:44.147] [Info] 响应状态码: OK
[2025-08-04 09:22:44.149] [Info] 响应头: Date=Mon, 04 Aug 2025 01:22:43 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Transfer-Encoding=chunked, Connection=keep-alive, Server=openresty, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, XHS-REQUEST-TIME=0.069, xhs-real-ip=*************
[2025-08-04 09:22:44.149] [Info] 响应内容: �         ���YKo���+2��h�_���xvf3��g���0�͢E��6��x-ݒ[�\r�%A��"@6H~�n6?#_�!Q�����TwW׻�*=��c�n�T�I��I���I�e8U� ��4bu�
N��4HWa,jd�u�e�ɚt���	��鄄J��ĥ��O>����rEE[�b�;y�W}��m�{Wĕ�nSY�Fy�ẚ�8�;.I߈72��T���/���mS����L��v����"i�Whi$�T��w<�\#X�d9$ �d^Ds˲l�@���e.����������^��OA(���$[��T��H�Kڣ��
y��2����72T���H�bn��i����TR��rOje�U�R�d-�!%�������QE����	��%k76Z��_�km��*g�&c�M��-�#�Ps��@���Bʺ{��cR��V�5�l�g���fv�.�	����5:N�i���ƇqR���
y�b��u�na:�˄e�e���>�&U0��؄FF��v����I&...
[2025-08-04 09:22:44.149] [Info] 请求成功，检查响应内容
[2025-08-04 09:22:44.156] [Warning] JSON解析失败，但这可能是正常的HTML响应: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:22:44.156] [Info] Cookie验证成功 - 能够正常访问小红书页面
[2025-08-04 09:22:44.157] [Info] 小红书Cookie登录成功
[2025-08-04 09:22:46.657] [Info] 已保存 2 个账号
[2025-08-04 09:22:59.324] [Info] 检测到平台: 小红书 📖
[2025-08-04 09:23:06.027] [Info] 已保存 1 个账号
[2025-08-04 09:23:08.418] [Info] 已保存 1 个账号
[2025-08-04 09:23:18.077] [Info] 已保存 0 个账号
[2025-08-04 09:23:52.385] [Info] 已保存 1 个账号
[2025-08-04 09:23:54.741] [Info] 已保存 1 个账号
[2025-08-04 09:24:01.757] [Info] 检测到平台: 小红书 📖
[2025-08-04 09:24:13.119] [Info] 已保存 2 个账号
[2025-08-04 09:24:16.671] [Info] 已保存 1 个账号
[2025-08-04 09:24:18.485] [Info] 已保存 0 个账号
[2025-08-04 09:24:28.325] [Info] 已保存 1 个账号
[2025-08-04 09:24:30.338] [Info] 已保存 1 个账号
[2025-08-04 09:24:33.230] [Info] 检测到平台: 小红书 📖
[2025-08-04 09:24:33.231] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688f6e52000000002302beed?xsec_token=ABThFRTuXqTQmEbN7l7mv4tPyfd_b6gn0WmH5Cp_6aXKs=&xsec_source=pc_feed
[2025-08-04 09:24:33.231] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:24:33.231] [Info] 开始验证小红书Cookie...
[2025-08-04 09:24:33.231] [Info] 尝试验证端点: https://www.xiaohongshu.com/
[2025-08-04 09:24:33.814] [Info] 响应状态码: OK
[2025-08-04 09:24:33.814] [Info] 响应头: Date=Mon, 04 Aug 2025 01:24:32 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Transfer-Encoding=chunked, Connection=keep-alive, Server=openresty, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, XHS-REQUEST-TIME=0.077, xhs-real-ip=*************
[2025-08-04 09:24:33.814] [Info] 响应内容: �         ���Wko���+���50�`ƃrw2��f�U�\]i4BM�=c�Qw{�߷�B&������ǩ�)�kƒ��K�J߿�>����i���4�VYJ[*����>�3\���m���NUCE��zG�"[�0�� f9V���m���D���h/�Y}�>������+0C��F��;�1'���2iT0�O�/%נ�f�Y�e�/.\	¤`Ը�$Q9Ixj@��	��)q��(�����*:O!�<����l��P�������G�&�_1$\@����4�P�/��f��x��
[��IqS�νPu$r/���|�!e��RU
�DY�����>�'�:v�U� k�(��0���yr6K~uU�9�ml�:�KB��t��{,L�U�Ko3d���l��E�*�x^�����§���儺��"rz�M:ǧ��ԭ5k=��AY<�I�W��|����[̹�g3����2sGW:U3:�$^��ƞ�n�d⛐O��W@u��6�,����~��	...
[2025-08-04 09:24:33.814] [Info] 请求成功，检查响应内容
[2025-08-04 09:24:33.815] [Warning] JSON解析失败，但这可能是正常的HTML响应: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:24:33.815] [Info] Cookie验证成功 - 能够正常访问小红书页面
[2025-08-04 09:24:33.815] [Info] 小红书Cookie登录成功
[2025-08-04 09:24:33.816] [Info] 提取到笔记ID: 688f6e52000000002302beed
[2025-08-04 09:24:33.887] [Error] 小红书解析失败: 无法获取笔记信息
[2025-08-04 09:24:46.134] [Info] 开始验证小红书Cookie...
[2025-08-04 09:24:46.134] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:24:46.134] [Info] 尝试验证端点: https://www.xiaohongshu.com/
[2025-08-04 09:24:46.597] [Info] 响应状态码: OK
[2025-08-04 09:24:46.597] [Info] 响应头: Date=Mon, 04 Aug 2025 01:24:45 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Transfer-Encoding=chunked, Connection=keep-alive, Server=openresty, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, XHS-REQUEST-TIME=0.070, xhs-real-ip=*************
[2025-08-04 09:24:46.597] [Info] 响应内容: �         ���YKo���+2��h�_���xvf3��g���0�͢E��6��x-ݒ[�\r�%A��"@6H~�n6?#_�!Q�����TwW׻�*=��c�n�T�I��I���I�e8U� ��4bu�
N��4HWa,jd�u�e�ɚt���	��鄄J��ĥ��O>����rEE[�b�;y�W}��m�{Wĕ�nSY�Fy�ẚ�8�;.I߈72��T���/���mS����L��v����"i�Whi$�T��w<�\#X�d9$ �d^Ds˲l�@���e.����������^��OA(���$[��T��H�Kڣ��
y��2����72T���H�bn��i����TR��rOje�U�R�d-�!%�������QE����	��%k76Z��_�km��*g�&c�M��-�#�Ps��@���Bʺ{��cR��V�5�l�g���fv�.�	����5:N�i���ƇqR���
y�b��u�na:�˄e�e���>�&U0��؄FF��v����I&...
[2025-08-04 09:24:46.598] [Warning] JSON解析失败，但这可能是正常的HTML响应: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:24:46.598] [Info] 请求成功，检查响应内容
[2025-08-04 09:24:46.598] [Info] Cookie验证成功 - 能够正常访问小红书页面
[2025-08-04 09:24:46.598] [Info] 小红书Cookie登录成功
[2025-08-04 09:24:53.930] [Info] 已保存 2 个账号
[2025-08-04 09:24:55.331] [Info] 检测到平台: 小红书 📖
[2025-08-04 09:24:55.332] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688f6e52000000002302beed?xsec_token=ABThFRTuXqTQmEbN7l7mv4tPyfd_b6gn0WmH5Cp_6aXKs=&xsec_source=pc_feed
[2025-08-04 09:24:55.332] [Info] 开始验证小红书Cookie...
[2025-08-04 09:24:55.332] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:24:55.332] [Info] 尝试验证端点: https://www.xiaohongshu.com/
[2025-08-04 09:24:55.724] [Info] 响应状态码: OK
[2025-08-04 09:24:55.724] [Info] 响应头: Date=Mon, 04 Aug 2025 01:24:54 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Transfer-Encoding=chunked, Connection=keep-alive, Server=openresty, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, XHS-REQUEST-TIME=0.066, xhs-real-ip=*************
[2025-08-04 09:24:55.724] [Info] 响应内容: �         ���Wko���+���50�`ƃrw2��f�U�\]i4BM�=c�Qw{�߷�B&������ǩ�)�kƒ��K�J߿�>����i���4�VYJ[*����>�3\���m���NUCE��zG�"[�0�� f9V���m���D���h/�Y}�>������+0C��F��;�1'���2iT0�O�/%נ�f�Y�e�/.\	¤`Ը�$Q9Ixj@��	��)q��(�����*:O!�<����l��P�������G�&�_1$\@����4�P�/��f��x��
[��IqS�νPu$r/���|�!e��RU
�DY�����>�'�:v�U� k�(��0���yr6K~uU�9�ml�:�KB��t��{,L�U�Ko3d���l��E�*�x^�����§���儺��"rz�M:ǧ��ԭ5k=��AY<�I�W��|����[̹�g3����2sGW:U3:�$^��ƞ�n�d⛐O��W@u��6�,����~��	...
[2025-08-04 09:24:55.724] [Info] 请求成功，检查响应内容
[2025-08-04 09:24:55.725] [Warning] JSON解析失败，但这可能是正常的HTML响应: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:24:55.725] [Info] Cookie验证成功 - 能够正常访问小红书页面
[2025-08-04 09:24:55.725] [Info] 小红书Cookie登录成功
[2025-08-04 09:24:55.725] [Info] 提取到笔记ID: 688f6e52000000002302beed
[2025-08-04 09:24:55.795] [Error] 小红书解析失败: 无法获取笔记信息
[2025-08-04 09:28:34.731] [Info] 已加载 2 个账号
[2025-08-04 09:28:34.797] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 09:28:34.799] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 09:28:34.849] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 09:28:34.849] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 09:28:34.850] [Info] 已加载 7 个平台下载器
[2025-08-04 09:28:34.850] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 09:28:34.852] [Info] 程序初始化完成
[2025-08-04 09:30:03.296] [Info] 已保存 1 个账号
[2025-08-04 09:30:05.693] [Info] 已保存 1 个账号
[2025-08-04 09:30:38.081] [Info] 检测到平台: 小红书 📖
[2025-08-04 09:30:38.082] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688f6e52000000002302beed?xsec_token=ABThFRTuXqTQmEbN7l7mv4tPyfd_b6gn0WmH5Cp_6aXKs=&xsec_source=pc_feed
[2025-08-04 09:30:38.084] [Info] 开始验证小红书Cookie...
[2025-08-04 09:30:38.084] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:30:38.084] [Info] 尝试验证端点: https://www.xiaohongshu.com/
[2025-08-04 09:30:38.563] [Info] 响应状态码: OK
[2025-08-04 09:30:38.564] [Info] 响应头: Date=Mon, 04 Aug 2025 01:30:37 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Transfer-Encoding=chunked, Connection=keep-alive, Server=openresty, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, XHS-REQUEST-TIME=0.069, xhs-real-ip=*************
[2025-08-04 09:30:38.565] [Info] 响应内容: �         ���Wko���+���50�`ƃrw2��f�U�\]i4BM�=c�Qw{�߷�B&������ǩ�)�kƒ��K�J߿�>����i���4�VYJ[*����>�3\���m���NUCE��zG�"[�0�� f9V���m���D���h/�Y}�>������+0C��F��;�1'���2iT0�O�/%נ�f�Y�e�/.\	¤`Ը�$Q9Ixj@��	��)q��(�����*:O!�<����l��P�������G�&�_1$\@����4�P�/��f��x��
[��IqS�νPu$r/���|�!e��RU
�DY�����>�'�:v�U� k�(��0���yr6K~uU�9�ml�:�KB��t��{,L�U�Ko3d���l��E�*�x^�����§���儺��"rz�M:ǧ��ԭ5k=��AY<�I�W��|����[̹�g3����2sGW:U3:�$^��ƞ�n�d⛐O��W@u��6�,����~��	...
[2025-08-04 09:30:38.565] [Info] 请求成功，检查响应内容
[2025-08-04 09:30:38.566] [Warning] JSON解析失败，但这可能是正常的HTML响应: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:30:38.566] [Info] 小红书Cookie登录成功
[2025-08-04 09:30:38.566] [Info] Cookie验证成功 - 能够正常访问小红书页面
[2025-08-04 09:30:38.567] [Info] 提取到笔记ID: 688f6e52000000002302beed
[2025-08-04 09:30:38.568] [Info] 开始获取笔记信息: 688f6e52000000002302beed
[2025-08-04 09:30:38.568] [Info] 尝试API端点: https://www.xiaohongshu.com/api/sns/web/v1/feed?source_note_id=688f6e52000000002302beed
[2025-08-04 09:30:38.638] [Info] API响应状态: InternalServerError
[2025-08-04 09:30:38.638] [Info] API响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:30:38.638] [Warning] API请求失败: InternalServerError, 内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:30:38.638] [Info] 尝试API端点: https://www.xiaohongshu.com/api/sns/web/v1/note/688f6e52000000002302beed
[2025-08-04 09:30:38.707] [Info] API响应状态: InternalServerError
[2025-08-04 09:30:38.707] [Info] API响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:30:38.707] [Warning] API请求失败: InternalServerError, 内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:30:38.707] [Info] 尝试API端点: https://www.xiaohongshu.com/explore/688f6e52000000002302beed?xhsshare=CopyLink&appuid=&apptime=
[2025-08-04 09:30:39.026] [Info] API响应状态: OK
[2025-08-04 09:30:39.026] [Info] API响应内容: �      �Y[o���+2KѢ��n�l����v�[�1YL��v8���P��[��
��о-�R�]��I������"Q��l���a���3�~�s�h+����Rm�&��G�b��G	��T�F�����T�pB�ǣTpƢF&Y�X6���I�ݕ���L'$T�D$.�xW~�u�ו'�(��;������vn��K\9��&1��k����ٌG�b�%��k�J�S����f��Q��m
O8<�)s�NwFa�H��ZI*�b�O)�V6Y	�*��ܲ,[8P���%A��.v�� �-k!��W��(TJ`�-mh*Ji(�%��VÆ<��2�����2T���H{bn��i����TR��rOje�U�R�d-�!%�������aE����	��%k7m�R���+��f�U�4M��N�G,t��Vq�f龅�u��5�Ǥ4��,kn�4�d�����[�h��g����v
�y��qR���5��b��q\c�0�e�2ʲoasE���*1��؄�F��v����I&�ɡ�͌T��0�	�$���"`�
K���\8e����V�R,�7ĕ58�5�å��-,
r���2j�C�׹F������<��|�!v��f��8j
��^�8�̾���lf.���|(��r�B@�dF���ޚhWf'������n�$4AҰ������������������Ӄ�Ӌ���O/NN�O;�*@ۜ�(�,:U��J��E�[������3e2�Yt��|���ZD�u��M�>��,N��Y�%T�R�/�"�>�\jY�'��Z�$)9���%Cq�4��9�&�bޕ�9$%��m�1�v��T;R�|�YTK�3��^���?���Q=QL��A8�@'��"��,��_���{�0ʌN��i�8!�@G}�x��d�fd`�=?M�Y���v�IpL��1��U�:��Vl�&�m5$Dl�����8��.2��D�@�ѕC��g�SCC��ke_�.��x2�f7�Dr��N ��-����Γ{&�P��R�pI�}eqly2...
[2025-08-04 09:30:39.027] [Warning] 所有API端点都失败了，返回模拟数据
[2025-08-04 09:30:39.026] [Warning] JSON解析失败: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:30:39.027] [Info] 创建模拟笔记信息: 688f6e52000000002302beed
[2025-08-04 09:30:41.986] [Info] 获取小红书下载链接: 688f6e52000000002302beed, 质量: 1080p
[2025-08-04 09:30:41.986] [Info] 开始验证小红书Cookie...
[2025-08-04 09:30:41.986] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:30:41.986] [Info] 尝试验证端点: https://www.xiaohongshu.com/
[2025-08-04 09:30:42.362] [Info] 响应状态码: OK
[2025-08-04 09:30:42.362] [Info] 响应内容: �         ���Wko���+���50�`ƃrw2��f�U�\]i4BM�=c�Qw{�߷�B&������ǩ�)�kƒ��K�J߿�>����i���4�VYJ[*����>�3\���m���NUCE��zG�"[�0�� f9V���m���D���h/�Y}�>������+0C��F��;�1'���2iT0�O�/%נ�f�Y�e�/.\	¤`Ը�$Q9Ixj@��	��)q��(�����*:O!�<����l��P�������G�&�_1$\@����4�P�/��f��x��
[��IqS�νPu$r/���|�!e��RU
�DY�����>�'�:v�U� k�(��0���yr6K~uU�9�ml�:�KB��t��{,L�U�Ko3d���l��E�*�x^�����§���儺��"rz�M:ǧ��ԭ5k=��AY<�I�W��|����[̹�g3����2sGW:U3:�$^��ƞ�n�d⛐O��W@u��6�,����~��	...
[2025-08-04 09:30:42.362] [Info] 响应头: Date=Mon, 04 Aug 2025 01:30:41 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Transfer-Encoding=chunked, Connection=keep-alive, Server=openresty, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, XHS-REQUEST-TIME=0.126, xhs-real-ip=*************
[2025-08-04 09:30:42.362] [Info] 请求成功，检查响应内容
[2025-08-04 09:30:42.362] [Warning] JSON解析失败，但这可能是正常的HTML响应: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:30:42.362] [Info] 小红书Cookie登录成功
[2025-08-04 09:30:42.362] [Info] Cookie验证成功 - 能够正常访问小红书页面
[2025-08-04 09:30:42.363] [Info] 尝试API端点: https://www.xiaohongshu.com/api/sns/web/v1/feed?source_note_id=688f6e52000000002302beed
[2025-08-04 09:30:42.363] [Info] 开始获取笔记信息: 688f6e52000000002302beed
[2025-08-04 09:30:42.431] [Info] API响应状态: InternalServerError
[2025-08-04 09:30:42.431] [Info] API响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:30:42.431] [Warning] API请求失败: InternalServerError, 内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:30:42.431] [Info] 尝试API端点: https://www.xiaohongshu.com/api/sns/web/v1/note/688f6e52000000002302beed
[2025-08-04 09:30:42.501] [Info] API响应状态: InternalServerError
[2025-08-04 09:30:42.501] [Info] API响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:30:42.501] [Warning] API请求失败: InternalServerError, 内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:30:42.501] [Info] 尝试API端点: https://www.xiaohongshu.com/explore/688f6e52000000002302beed?xhsshare=CopyLink&appuid=&apptime=
[2025-08-04 09:30:42.840] [Info] API响应状态: OK
[2025-08-04 09:30:42.840] [Info] API响应内容: �      �Wmo�H�+�"[5lC S宩tw����IU���ܚ]��n������h�`vv^�yy�o�b��z��^f��֟@�wo��J�!�yF�2�:]B�]�s�S�[`���c��-y#���y��ɀ��b$߼q6�\G<ϲ�h'�I~�?�������0M��Z��Y�1+���"i�s�w��/)V �z�^�D���mq�gTۧ$Q9I�L��&	:�����P!�kC�t�A�8��	BqM��Y��B�#iו�Q�~Ő��lp��He9��}��u\�'I�yi�<?�T׿'T��)j��eH���d�9���<����B�?�jK�	��5NTE��_pFir2K5~MU�9�m��*WBW�l���;,t���K�3��ͱl�EiKW;N�P���ܥ��v儺��<���C�K˥���P�k?��BY<���g���a�ľv�
�\Ó�R�ӄ2}K�Zu3Z�$^�
RaO�|��r���Gn�K�*�`rSM�F?��
�(�J�Pڡ�Z�Ƃ�;âs
7}���`Fс��c���ǔ��B�Tz5�(�!���y��n��4nu������6(���~�/��)W�|�B�l�R����n׶�"r����I�� i9EX�/
�Eև��??�\M�&��������������r�} L�!���D�����4,m�4!�����-YQ��
?}.F���s6Z���Qj�}"̞3�LA�Kyu��Y�.� p�J�;���x����)��	6
Vr�C��*V���ݦ�
�[~�7��#�(���9�2�U�_����g�uq�@[i�SF�Xl�r�m�3O�^��΄=��f����(�=�c�>�T,��E^��<�g��ɪT���b��5m��rvY5p�gg��({���l6	���T��d0-�?�UG�a��6�S������?,�'F�\��qA�Mj�R4eQX_]$�6���K*%LJ_ge�VEBN3%Y$B����߉kYʿ�KTd)��@- 4%���D�q)j��
,3+
�_)...
[2025-08-04 09:30:42.841] [Warning] JSON解析失败: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:30:42.841] [Warning] 所有API端点都失败了，返回模拟数据
[2025-08-04 09:30:42.841] [Info] 创建模拟笔记信息: 688f6e52000000002302beed
[2025-08-04 09:30:42.841] [Info] 成功获取 1 个下载链接
[2025-08-04 09:31:25.676] [Warning] 未找到支持的平台: https://www.xiaohongshu.com/user/profile/5fe70e6e0000000001004cb8?xsec_token=ABIx3cdMkXeuTex-4Pb55go-JDEtA-_2w-dsW5T12Q6g0=&xsec_source=pc_note
[2025-08-04 09:31:34.536] [Info] 检测到平台: 小红书 📖
[2025-08-04 09:31:34.536] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688841b6000000002201ed12?xsec_token=ABsc82Of7ufb2V33AsOShP8EcF5c2_in9Hz0ebfFsIA0Q=&xsec_source=pc_user
[2025-08-04 09:31:34.536] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:31:34.536] [Info] 开始验证小红书Cookie...
[2025-08-04 09:31:34.536] [Info] 尝试验证端点: https://www.xiaohongshu.com/
[2025-08-04 09:31:34.907] [Info] 响应状态码: OK
[2025-08-04 09:31:34.907] [Info] 响应头: Date=Mon, 04 Aug 2025 01:31:34 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Transfer-Encoding=chunked, Connection=keep-alive, Server=openresty, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, XHS-REQUEST-TIME=0.082, xhs-real-ip=*************
[2025-08-04 09:31:34.907] [Info] 响应内容: �      �Wko���+���50�`ƃrw2��f�U�\]i4BM�=c�Qw{�߷�B&������ǩ�)�kƒ��K�J߿�>����i���4�VYJ[*����>�3\���m���NUCE��zG�"[�0�� f9V���m���D���h/�Y}�>������+0C��F��;�1'���2iT0�O�/%נ�f�Y�e�/.\	¤`Ը�$Q9Ixj@��	��)q��(�����*:O!�<����l��P�������G�&�_1$\@����4�P�/��f��x��
[��IqS�νPu$r/���|�!e��RU
�DY�����>�'�:v�U� k�(��0���yr6K~uU�9�ml�:�KB��t��{,L�U�Ko3d���l��E�*�x^�����§���儺��"rz�M:ǧ��ԭ5k=��AY<�I�W��|����[̹�g3����2sGW:U3:�$^��ƞ�n�d⛐O��W@u��6�,����~��	\S��...
[2025-08-04 09:31:34.908] [Info] 请求成功，检查响应内容
[2025-08-04 09:31:34.908] [Warning] JSON解析失败，但这可能是正常的HTML响应: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:31:34.908] [Info] 尝试API端点: https://www.xiaohongshu.com/api/sns/web/v1/feed?source_note_id=688841b6000000002201ed12
[2025-08-04 09:31:34.908] [Info] 小红书Cookie登录成功
[2025-08-04 09:31:34.908] [Info] 提取到笔记ID: 688841b6000000002201ed12
[2025-08-04 09:31:34.908] [Info] Cookie验证成功 - 能够正常访问小红书页面
[2025-08-04 09:31:34.908] [Info] 开始获取笔记信息: 688841b6000000002201ed12
[2025-08-04 09:31:34.977] [Info] API响应状态: InternalServerError
[2025-08-04 09:31:34.977] [Info] API响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:31:34.977] [Warning] API请求失败: InternalServerError, 内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:31:34.977] [Info] 尝试API端点: https://www.xiaohongshu.com/api/sns/web/v1/note/688841b6000000002201ed12
[2025-08-04 09:31:35.046] [Info] API响应状态: InternalServerError
[2025-08-04 09:31:35.046] [Info] API响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:31:35.046] [Warning] API请求失败: InternalServerError, 内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:31:35.046] [Info] 尝试API端点: https://www.xiaohongshu.com/explore/688841b6000000002201ed12?xhsshare=CopyLink&appuid=&apptime=
[2025-08-04 09:31:35.355] [Info] API响应状态: OK
[2025-08-04 09:31:35.355] [Info] API响应内容: �      �Y[o���+2KѢ��n�l����v�[�1YL��v8���P��[��
��о-�R�]��I������"Q��l���a���3�~�s�h+����Rm�&��G�b��G	��T�F�����T�pB�ǣTpƢF&Y�X6���I�ݕ���L'$T�D$.�xW~�u�ו'�(��;������vn��K\9��&1��k����ٌG�b�%��k�J�S����f��Q��m
O8<�)s�NwFa�H��ZI*�b�O)�V6Y	�*��ܲ,[8P���%A��.v�� �-k!��W��(TJ`�-mh*Ji(�%��VÆ<��2�����2T���H{bn��i����TR��rOje�U�R�d-�!%�������aE����	��%k7m�R���+��f�U�4M��N�G,t��Vq�f龅�u��5�Ǥ4��,kn�4�d�����[�h��g����v
�y��qR���5��b��q\c�0�e�2ʲoasE���*1��؄�F��v����I&�ɡ�͌T��0�	�$���"`�
K���\8e����V�R,�7ĕ58�5�å��-,
r���2j�C�׹F������<��|�!v��f��8j
��^�8�̾���lf.���|(��r�B@�dF���ޚhWf'������n�$4AҰ������������������Ӄ�Ӌ���O/NN�O;�*@ۜ�(�,:U��J��E�[������3e2�Yt��|���ZD�u��M�>��,N��Y�%T�R�/�"�>�\jY�'��Z�$)9���%Cq�4��9�&�bޕ�9$%��m�1�v��T;R�|�YTK�3��^���?���Q=QL��A8�@'��"��,��_���{�0ʌN��i�8!�@G}�x��d�fd`�=?M�Y���v�IpL��1��U�:��Vl�&�m5$Dl�����8��.2��D�@�ѕC��g�SCC��ke_�.��x2�f7�Dr��N ��-����Γ{&�P��R�pI�}eqly2...
[2025-08-04 09:31:35.355] [Warning] JSON解析失败: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:31:35.355] [Warning] 所有API端点都失败了，返回模拟数据
[2025-08-04 09:31:35.355] [Info] 创建模拟笔记信息: 688841b6000000002201ed12
[2025-08-04 09:31:48.561] [Info] 获取小红书下载链接: 688841b6000000002201ed12, 质量: 1080p
[2025-08-04 09:31:48.562] [Info] 开始验证小红书Cookie...
[2025-08-04 09:31:48.562] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:31:48.562] [Info] 尝试验证端点: https://www.xiaohongshu.com/
[2025-08-04 09:31:49.038] [Info] 响应状态码: OK
[2025-08-04 09:31:49.039] [Info] 响应头: Date=Mon, 04 Aug 2025 01:31:48 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Transfer-Encoding=chunked, Connection=keep-alive, Server=openresty, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, XHS-REQUEST-TIME=0.176, xhs-real-ip=*************
[2025-08-04 09:31:49.039] [Info] 响应内容: �         ���YKo���+2��h�_���xvf3��g���0�͢E��6��x-ݒ[�\r�%A��"@6H~�n6?#_�!Q�����TwW׻�*=��c�n�T�I��I���I�e8U� ��4bu�
N��4HWa,jd�u�e�ɚt���	��鄄J��ĥ��O>����rEE[�b�;y�W}��m�{Wĕ�nSY�Fy�ẚ�8�;.I߈72��T���/���mS����L��v����"i�Whi$�T��w<�\#X�d9$ �d^Ds˲l�@���e.����������^��OA(���$[��T��H�Kڣ��
y��2����72T���H�bn��i����TR��rOje�U�R�d-�!%�������QE����	��%k76Z��_�km��*g�&c�M��-�#�Ps��@���Bʺ{��cR��V�5�l�g���fv�.�	����5:N�i���ƇqR���
y�b��u�na:�˄e�e���>�&U0��؄FF��v����I&...
[2025-08-04 09:31:49.039] [Info] 请求成功，检查响应内容
[2025-08-04 09:31:49.040] [Warning] JSON解析失败，但这可能是正常的HTML响应: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:31:49.040] [Info] Cookie验证成功 - 能够正常访问小红书页面
[2025-08-04 09:31:49.040] [Info] 小红书Cookie登录成功
[2025-08-04 09:31:49.041] [Info] 开始获取笔记信息: 688841b6000000002201ed12
[2025-08-04 09:31:49.041] [Info] 尝试API端点: https://www.xiaohongshu.com/api/sns/web/v1/feed?source_note_id=688841b6000000002201ed12
[2025-08-04 09:31:49.106] [Info] API响应状态: InternalServerError
[2025-08-04 09:31:49.106] [Info] API响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:31:49.107] [Warning] API请求失败: InternalServerError, 内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:31:49.107] [Info] 尝试API端点: https://www.xiaohongshu.com/api/sns/web/v1/note/688841b6000000002201ed12
[2025-08-04 09:31:49.175] [Info] API响应状态: InternalServerError
[2025-08-04 09:31:49.175] [Info] API响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:31:49.176] [Warning] API请求失败: InternalServerError, 内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:31:49.176] [Info] 尝试API端点: https://www.xiaohongshu.com/explore/688841b6000000002201ed12?xhsshare=CopyLink&appuid=&apptime=
[2025-08-04 09:31:49.451] [Info] API响应状态: OK
[2025-08-04 09:31:49.451] [Info] API响应内容: �      �Y[o���+2KѺ��pg�����"0����bB
�á���Eߊ>(P�݇�mї�E�L�������eeۇ��̜9��G[~��͔jc5�?*��?J����r�F�.S��	���*�E�L�n�l^1Y�nsW>b�2��P���T�]��'֭^W�H�h�]�x#���/�۹-�c�-q弣��T֮Q^f��f3jŎKR��╌�$����ċ�d{��px,8S榝 �a�H��ZI*�b�O)�V6Y	�*��ܲ,[8P���%A��.v�� �-k!��W��S
*%0ɖ64�4���h�aC� �L�����U�<�FҮ�[�y�/,$5ՅT�0�ܓZ�kճ2Y�gH	h~�)-kTQ�f"w��}��
��V*�Wz�Z��l�ʙ���a�it㈅.��*.�,ݷ��n��������e�-��l����wm��p���p�=�f��~�ԧ�~M���X�w���[��2aeٷ�����I���lB#�FC�����$��H�fF*މ�Z��X�J�y�3�F%}��
.�2��]Rv�R)����Κ��Rf��o��V�ס���\#���tຮIn����HY��U���h/Qf�RKj63��ey>Ix9^! K2#`�joM�+3����t�d7g� iX�Q~�|ns�xv|r��p�b��������x�������a�Uh��ŜE�*�P��}�hbr���>;=~�L�Lt�9��.�f�*y���tC3�i�z�.E	����ȵ��&��ܓ���b�p����������
<y����X�w%qDI�lw�}���}>Վ��7�k�R�̬��W� }���1vv�'���;��=Y�@���;�z���3�2��#i1N�=��G߇,��2��q�Kd�$q�N8	��)=����V'8ۊ��ļ���ȂMR3�BR���E&���H9�rț��ujd�~-���օO���L�&�H���	d^���x��йr�d.
sVJ.	��,�M#/B�Y"����P�6�B�N�P��...
[2025-08-04 09:31:49.452] [Warning] JSON解析失败: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:31:49.452] [Warning] 所有API端点都失败了，返回模拟数据
[2025-08-04 09:31:49.452] [Info] 创建模拟笔记信息: 688841b6000000002201ed12
[2025-08-04 09:31:49.452] [Info] 成功获取 1 个下载链接
[2025-08-04 09:38:58.239] [Info] 已加载 1 个账号
[2025-08-04 09:38:58.300] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 09:38:58.301] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 09:38:58.351] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 09:38:58.351] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 09:38:58.352] [Info] 已加载 7 个平台下载器
[2025-08-04 09:38:58.352] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 09:38:58.355] [Info] 程序初始化完成
[2025-08-04 09:45:14.269] [Info] 已保存 0 个账号
[2025-08-04 09:45:17.920] [Info] 开始验证小红书Cookie...
[2025-08-04 09:45:17.921] [Info] Cookie内容: document.cookie...
[2025-08-04 09:45:17.921] [Info] 尝试验证端点: https://www.xiaohongshu.com/
[2025-08-04 09:45:18.501] [Info] 响应状态码: OK
[2025-08-04 09:45:18.502] [Info] 响应头: Date=Mon, 04 Aug 2025 01:45:17 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Transfer-Encoding=chunked, Connection=keep-alive, Server=openresty, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, XHS-REQUEST-TIME=0.066, xhs-real-ip=*************
[2025-08-04 09:45:18.502] [Info] 响应内容: �         ���Wko���+���50�`ƃrw2��f�U�\]i4BM�=c�Qw{�߷�B&������ǩ�)�kƒ��K�J߿�>����i���4�VYJ[*����>�3\���m���NUCE��zG�"[�0�� f9V���m���D���h/�Y}�>������+0C��F��;�1'���2iT0�O�/%נ�f�Y�e�/.\	¤`Ը�$Q9Ixj@��	��)q��(�����*:O!�<����l��P�������G�&�_1$\@����4�P�/��f��x��
[��IqS�νPu$r/���|�!e��RU
�DY�����>�'�:v�U� k�(��0���yr6K~uU�9�ml�:�KB��t��{,L�U�Ko3d���l��E�*�x^�����§���儺��"rz�M:ǧ��ԭ5k=��AY<�I�W��|����[̹�g3����2sGW:U3:�$^��ƞ�n�d⛐O��W@u��6�,����~��	...
[2025-08-04 09:45:18.502] [Info] 请求成功，检查响应内容
[2025-08-04 09:45:18.504] [Warning] JSON解析失败，但这可能是正常的HTML响应: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:45:18.504] [Info] Cookie验证成功 - 能够正常访问小红书页面
[2025-08-04 09:45:18.504] [Info] 小红书Cookie登录成功
[2025-08-04 09:46:08.956] [Info] 开始验证小红书Cookie...
[2025-08-04 09:46:08.956] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:46:08.956] [Info] 尝试验证端点: https://www.xiaohongshu.com/
[2025-08-04 09:46:09.561] [Info] 响应状态码: OK
[2025-08-04 09:46:09.561] [Info] 响应头: Date=Mon, 04 Aug 2025 01:46:08 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Transfer-Encoding=chunked, Connection=keep-alive, Server=openresty, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, XHS-REQUEST-TIME=0.217, xhs-real-ip=*************
[2025-08-04 09:46:09.561] [Info] 响应内容: �         ���YKo���+2��h�_���xvf3��g���0�͢E��6��x-ݒ[�\r�%A��"@6H~�n6?#_�!Q�����TwW׻�*=��c�n�T�I��I���I�e8U� ��4bu�
N��4HWa,jd�u�e�ɚt���	��鄄J��ĥ��O>����rEE[�b�;y�W}��m�{Wĕ�nSY�Fy�ẚ�8�;.I߈72��T���/���mS����L��v����"i�Whi$�T��w<�\#X�d9$ �d^Ds˲l�@���e.����������^��OA(���$[��T��H�Kڣ��
y��2����72T���H�bn��i����TR��rOje�U�R�d-�!%�������QE����	��%k76Z��_�km��*g�&c�M��-�#�Ps��@���Bʺ{��cR��V�5�l�g���fv�.�	����5:N�i���ƇqR���
y�b��u�na:�˄e�e���>�&U0��؄FF��v����I&...
[2025-08-04 09:46:09.561] [Info] 请求成功，检查响应内容
[2025-08-04 09:46:09.561] [Warning] JSON解析失败，但这可能是正常的HTML响应: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:46:09.561] [Info] Cookie验证成功 - 能够正常访问小红书页面
[2025-08-04 09:46:09.562] [Info] 小红书Cookie登录成功
[2025-08-04 09:46:10.470] [Info] 已保存 1 个账号
[2025-08-04 09:46:14.744] [Info] 已保存 1 个账号
[2025-08-04 09:46:21.718] [Info] 检测到平台: 小红书 📖
[2025-08-04 09:46:21.718] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688841b6000000002201ed12?xsec_token=ABsc82Of7ufb2V33AsOShP8EcF5c2_in9Hz0ebfFsIA0Q=&xsec_source=pc_user
[2025-08-04 09:46:21.718] [Info] 开始验证小红书Cookie...
[2025-08-04 09:46:21.718] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:46:21.718] [Info] 尝试验证端点: https://www.xiaohongshu.com/
[2025-08-04 09:46:22.182] [Info] 响应状态码: OK
[2025-08-04 09:46:22.182] [Info] 响应头: Date=Mon, 04 Aug 2025 01:46:21 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Transfer-Encoding=chunked, Connection=keep-alive, Server=openresty, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, XHS-REQUEST-TIME=0.139, xhs-real-ip=*************
[2025-08-04 09:46:22.182] [Info] 响应内容: �         ���Wko���+���50�`ƃrw2��f�U�\]i4BM�=c�Qw{�߷�B&������ǩ�)�kƒ��K�J߿�>����i���4�VYJ[*����>�3\���m���NUCE��zG�"[�0�� f9V���m���D���h/�Y}�>������+0C��F��;�1'���2iT0�O�/%נ�f�Y�e�/.\	¤`Ը�$Q9Ixj@��	��)q��(�����*:O!�<����l��P�������G�&�_1$\@����4�P�/��f��x��
[��IqS�νPu$r/���|�!e��RU
�DY�����>�'�:v�U� k�(��0���yr6K~uU�9�ml�:�KB��t��{,L�U�Ko3d���l��E�*�x^�����§���儺��"rz�M:ǧ��ԭ5k=��AY<�I�W��|����[̹�g3����2sGW:U3:�$^��ƞ�n�d⛐O��W@u��6�,����~��	...
[2025-08-04 09:46:22.182] [Info] 请求成功，检查响应内容
[2025-08-04 09:46:22.182] [Warning] JSON解析失败，但这可能是正常的HTML响应: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:46:22.182] [Info] Cookie验证成功 - 能够正常访问小红书页面
[2025-08-04 09:46:22.182] [Info] 小红书Cookie登录成功
[2025-08-04 09:46:22.183] [Info] 提取到笔记ID: 688841b6000000002201ed12
[2025-08-04 09:46:22.184] [Info] 开始获取笔记信息: 688841b6000000002201ed12
[2025-08-04 09:46:22.184] [Info] 尝试API端点: https://www.xiaohongshu.com/api/sns/web/v1/feed?source_note_id=688841b6000000002201ed12
[2025-08-04 09:46:22.243] [Info] API响应状态: InternalServerError
[2025-08-04 09:46:22.243] [Info] API响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:46:22.243] [Warning] API请求失败: InternalServerError, 内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:46:22.243] [Info] 尝试API端点: https://www.xiaohongshu.com/api/sns/web/v1/note/688841b6000000002201ed12
[2025-08-04 09:46:22.305] [Info] API响应状态: InternalServerError
[2025-08-04 09:46:22.305] [Info] API响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:46:22.305] [Warning] API请求失败: InternalServerError, 内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:46:22.305] [Info] 尝试API端点: https://www.xiaohongshu.com/explore/688841b6000000002201ed12?xhsshare=CopyLink&appuid=&apptime=
[2025-08-04 09:46:22.595] [Info] API响应状态: OK
[2025-08-04 09:46:22.595] [Info] API响应内容: �      �Y[o���+2KѺ��pg�����"0����bB
�á���Eߊ>(P�݇�mї�E�L�������eeۇ��̜9��G[~��͔jc5�?*��?J����r�F�.S��	���*�E�L�n�l^1Y�nsW>b�2��P���T�]��'֭^W�H�h�]�x#���/�۹-�c�-q弣��T֮Q^f��f3jŎKR��╌�$����ċ�d{��px,8S榝 �a�H��ZI*�b�O)�V6Y	�*��ܲ,[8P���%A��.v�� �-k!��W��S
*%0ɖ64�4���h�aC� �L�����U�<�FҮ�[�y�/,$5ՅT�0�ܓZ�kճ2Y�gH	h~�)-kTQ�f"w��}��
��V*�Wz�Z��l�ʙ���a�it㈅.��*.�,ݷ��n��������e�-��l����wm��p���p�=�f��~�ԧ�~M���X�w���[��2aeٷ�����I���lB#�FC�����$��H�fF*މ�Z��X�J�y�3�F%}��
.�2��]Rv�R)����Κ��Rf��o��V�ס���\#���tຮIn����HY��U���h/Qf�RKj63��ey>Ix9^! K2#`�joM�+3����t�d7g� iX�Q~�|ns�xv|r��p�b��������x�������a�Uh��ŜE�*�P��}�hbr���>;=~�L�Lt�9��.�f�*y���tC3�i�z�.E	����ȵ��&��ܓ���b�p����������
<y����X�w%qDI�lw�}���}>Վ��7�k�R�̬��W� }���1vv�'���;��=Y�@���;�z���3�2��#i1N�=��G߇,��2��q�Kd�$q�N8	��)=����V'8ۊ��ļ���ȂMR3�BR���E&���H9�rț��ujd�~-���օO���L�&�H���	d^���x��йr�d.
sVJ.	��,�M#/B�Y"����P�6�B�N�P��...
[2025-08-04 09:46:22.596] [Warning] JSON解析失败: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:46:22.596] [Warning] 所有API端点都失败了，返回模拟数据
[2025-08-04 09:46:22.596] [Info] 创建模拟笔记信息: 688841b6000000002201ed12
[2025-08-04 09:46:24.960] [Info] 获取小红书下载链接: 688841b6000000002201ed12, 质量: 1080p
[2025-08-04 09:46:24.960] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:46:24.960] [Info] 开始验证小红书Cookie...
[2025-08-04 09:46:24.960] [Info] 尝试验证端点: https://www.xiaohongshu.com/
[2025-08-04 09:46:25.299] [Info] 响应状态码: OK
[2025-08-04 09:46:25.300] [Info] 响应头: Date=Mon, 04 Aug 2025 01:46:24 GMT, Alt-Svc=h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-27=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443"; ma=2592000,h3-Q043=":443"; ma=2592000,h3-Q039=":443"; ma=2592000,quic=":443"; ma=2592000, Transfer-Encoding=chunked, Connection=keep-alive, Server=openresty, Access-Control-Allow-Origin=https://www.xiaohongshu.com, Access-Control-Allow-Credentials=true, Access-Control-Allow-Methods=GET,OPTIONS, Access-Control-Max-Age=86400, XHS-REQUEST-TIME=0.071, xhs-real-ip=*************
[2025-08-04 09:46:25.300] [Info] 响应内容: �         ���YKo���+2��h�_���xvf3��g���0�͢E��6��x-ݒ[�\r�%A��"@6H~�n6?#_�!Q�����TwW׻�*=��c�n�T�I��I���I�e8U� ��4bu�
N��4HWa,jd�u�e�ɚt���	��鄄J��ĥ��O>����rEE[�b�;y�W}��m�{Wĕ�nSY�Fy�ẚ�8�;.I߈72��T���/���mS����L��v����"i�Whi$�T��w<�\#X�d9$ �d^Ds˲l�@���e.����������^��OA(���$[��T��H�Kڣ��
y��2����72T���H�bn��i����TR��rOje�U�R�d-�!%�������QE����	��%k76Z��_�km��*g�&c�M��-�#�Ps��@���Bʺ{��cR��V�5�l�g���fv�.�	����5:N�i���ƇqR���
y�b��u�na:�˄e�e���>�&U0��؄FF��v����I&...
[2025-08-04 09:46:25.300] [Info] 请求成功，检查响应内容
[2025-08-04 09:46:25.300] [Warning] JSON解析失败，但这可能是正常的HTML响应: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:46:25.300] [Info] Cookie验证成功 - 能够正常访问小红书页面
[2025-08-04 09:46:25.301] [Info] 小红书Cookie登录成功
[2025-08-04 09:46:25.301] [Info] 开始获取笔记信息: 688841b6000000002201ed12
[2025-08-04 09:46:25.301] [Info] 尝试API端点: https://www.xiaohongshu.com/api/sns/web/v1/feed?source_note_id=688841b6000000002201ed12
[2025-08-04 09:46:25.363] [Info] API响应状态: InternalServerError
[2025-08-04 09:46:25.363] [Info] API响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:46:25.363] [Warning] API请求失败: InternalServerError, 内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:46:25.363] [Info] 尝试API端点: https://www.xiaohongshu.com/api/sns/web/v1/note/688841b6000000002201ed12
[2025-08-04 09:46:25.423] [Info] API响应状态: InternalServerError
[2025-08-04 09:46:25.423] [Info] API响应内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:46:25.423] [Warning] API请求失败: InternalServerError, 内容: create invoker failed, service: jarvis-gateway-default
[2025-08-04 09:46:25.423] [Info] 尝试API端点: https://www.xiaohongshu.com/explore/688841b6000000002201ed12?xhsshare=CopyLink&appuid=&apptime=
[2025-08-04 09:46:25.722] [Info] API响应状态: OK
[2025-08-04 09:46:25.722] [Info] API响应内容: �      �Y[o���+2KѺ��pg�����"0����bB
�á���Eߊ>(P�݇�mї�E�L�������eeۇ��̜9��G[~��͔jc5�?*��?J����r�F�.S��	���*�E�L�n�l^1Y�nsW>b�2��P���T�]��'֭^W�H�h�]�x#���/�۹-�c�-q弣��T֮Q^f��f3jŎKR��╌�$����ċ�d{��px,8S榝 �a�H��ZI*�b�O)�V6Y	�*��ܲ,[8P���%A��.v�� �-k!��W��S
*%0ɖ64�4���h�aC� �L�����U�<�FҮ�[�y�/,$5ՅT�0�ܓZ�kճ2Y�gH	h~�)-kTQ�f"w��}��
��V*�Wz�Z��l�ʙ���a�it㈅.��*.�,ݷ��n��������e�-��l����wm��p���p�=�f��~�ԧ�~M���X�w���[��2aeٷ�����I���lB#�FC�����$��H�fF*މ�Z��X�J�y�3�F%}��
.�2��]Rv�R)����Κ��Rf��o��V�ס���\#���tຮIn����HY��U���h/Qf�RKj63��ey>Ix9^! K2#`�joM�+3����t�d7g� iX�Q~�|ns�xv|r��p�b��������x�������a�Uh��ŜE�*�P��}�hbr���>;=~�L�Lt�9��.�f�*y���tC3�i�z�.E	����ȵ��&��ܓ���b�p����������
<y����X�w%qDI�lw�}���}>Վ��7�k�R�̬��W� }���1vv�'���;��=Y�@���;�z���3�2��#i1N�=��G߇,��2��q�Kd�$q�N8	��)=����V'8ۊ��ļ���ȂMR3�BR���E&���H9�rț��ujd�~-���օO���L�&�H���	d^���x��йr�d.
sVJ.	��,�M#/B�Y"����P�6�B�N�P��...
[2025-08-04 09:46:25.723] [Warning] JSON解析失败: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 09:46:25.723] [Info] 创建模拟笔记信息: 688841b6000000002201ed12
[2025-08-04 09:46:25.723] [Warning] 所有API端点都失败了，返回模拟数据
[2025-08-04 09:46:25.724] [Info] 开始提取真实下载链接，视频数量: 1, 图片数量: 1
[2025-08-04 09:46:25.725] [Info] 验证链接: https://sns-video-bd.xhscdn.com/688841b6000000002201ed12/video.mp4
[2025-08-04 09:46:26.143] [Info] 链接验证结果: NotFound
[2025-08-04 09:46:26.144] [Warning] 链接验证失败: NotFound
[2025-08-04 09:46:26.144] [Info] 验证链接: https://sns-img-bd.xhscdn.com/688841b6000000002201ed12/image1.jpg
[2025-08-04 09:46:26.446] [Info] 链接验证结果: NotFound
[2025-08-04 09:46:26.446] [Warning] 链接验证失败: NotFound
[2025-08-04 09:46:26.446] [Info] 成功提取 0 个真实下载链接
[2025-08-04 09:46:26.446] [Info] API方法失败，尝试页面解析方法
[2025-08-04 09:46:26.447] [Info] 从页面获取下载链接: https://www.xiaohongshu.com/explore/688841b6000000002201ed12
[2025-08-04 09:46:26.916] [Info] 从HTML提取到 0 个媒体链接
[2025-08-04 09:46:26.916] [Info] 从页面提取到 0 个下载链接
[2025-08-04 09:46:26.916] [Info] 页面解析失败，尝试通用API方法
[2025-08-04 09:46:26.978] [Error] 获取小红书下载链接失败: 未找到可下载的内容。请确保：
1. 已正确登录小红书账号
2. 内容链接有效且可访问
3. 网络连接正常
[2025-08-04 09:57:55.820] [Info] 已加载 1 个账号
[2025-08-04 09:57:55.903] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 09:57:55.905] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 09:57:55.976] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 09:57:55.976] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 09:57:55.977] [Info] 已加载 7 个平台下载器
[2025-08-04 09:57:55.977] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 09:57:55.981] [Info] 程序初始化完成
[2025-08-04 09:59:13.612] [Info] 开始验证小红书Cookie...
[2025-08-04 09:59:13.613] [Warning] Cookie缺少必要字段，可能不是有效的小红书Cookie
[2025-08-04 09:59:13.613] [Warning] Cookie格式不正确
[2025-08-04 09:59:13.613] [Warning] 小红书Cookie无效或已过期
[2025-08-04 09:59:16.460] [Info] 开始验证小红书Cookie...
[2025-08-04 09:59:16.460] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:59:16.460] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/selfinfo
[2025-08-04 09:59:16.687] [Info] 响应状态码: InternalServerError
[2025-08-04 09:59:16.687] [Info] 响应内容长度: 54
[2025-08-04 09:59:16.687] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/homefeed
[2025-08-04 09:59:16.752] [Info] 响应状态码: InternalServerError
[2025-08-04 09:59:16.753] [Info] 响应内容长度: 54
[2025-08-04 09:59:16.753] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/settings
[2025-08-04 09:59:16.819] [Info] 响应状态码: InternalServerError
[2025-08-04 09:59:16.819] [Info] 响应内容长度: 54
[2025-08-04 09:59:16.820] [Info] 尝试通过用户资料验证Cookie
[2025-08-04 09:59:17.161] [Warning] 用户资料验证失败
[2025-08-04 09:59:17.161] [Warning] 小红书Cookie无效或已过期
[2025-08-04 09:59:21.075] [Info] 开始验证小红书Cookie...
[2025-08-04 09:59:21.075] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:59:21.075] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/selfinfo
[2025-08-04 09:59:21.142] [Info] 响应状态码: InternalServerError
[2025-08-04 09:59:21.143] [Info] 响应内容长度: 54
[2025-08-04 09:59:21.143] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/homefeed
[2025-08-04 09:59:21.253] [Info] 响应状态码: InternalServerError
[2025-08-04 09:59:21.253] [Info] 响应内容长度: 54
[2025-08-04 09:59:21.253] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/settings
[2025-08-04 09:59:21.323] [Info] 响应状态码: InternalServerError
[2025-08-04 09:59:21.323] [Info] 响应内容长度: 54
[2025-08-04 09:59:21.323] [Info] 尝试通过用户资料验证Cookie
[2025-08-04 09:59:21.699] [Warning] 用户资料验证失败
[2025-08-04 09:59:21.699] [Warning] 小红书Cookie无效或已过期
[2025-08-04 09:59:39.440] [Info] 开始验证小红书Cookie...
[2025-08-04 09:59:39.440] [Info] Cookie内容: abRequestId=a6089197-5cfa-5a60-9d2e-08e9e7813987; webBuild=4.75.0; a1=1987141a4bc4nuqt57i5a5ma6cpcnb...
[2025-08-04 09:59:39.440] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/selfinfo
[2025-08-04 09:59:39.615] [Info] 响应状态码: InternalServerError
[2025-08-04 09:59:39.615] [Info] 响应内容长度: 54
[2025-08-04 09:59:39.615] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/homefeed
[2025-08-04 09:59:39.677] [Info] 响应状态码: InternalServerError
[2025-08-04 09:59:39.677] [Info] 响应内容长度: 54
[2025-08-04 09:59:39.677] [Info] 尝试验证端点: https://www.xiaohongshu.com/api/sns/web/v1/user/settings
[2025-08-04 09:59:39.739] [Info] 响应状态码: InternalServerError
[2025-08-04 09:59:39.739] [Info] 响应内容长度: 54
[2025-08-04 09:59:39.739] [Info] 尝试通过用户资料验证Cookie
[2025-08-04 09:59:40.086] [Warning] 用户资料验证失败
[2025-08-04 09:59:40.086] [Warning] 小红书Cookie无效或已过期
[2025-08-04 10:09:10.723] [Info] 已加载 1 个账号
[2025-08-04 10:09:10.783] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:09:10.785] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:09:10.836] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:09:10.836] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:09:10.837] [Info] 已加载 7 个平台下载器
[2025-08-04 10:09:10.838] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 📖小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 10:09:10.840] [Info] 程序初始化完成
[2025-08-04 10:13:47.959] [Info] 已保存 1 个账号
[2025-08-04 10:13:50.382] [Info] 已保存 0 个账号
[2025-08-04 10:14:27.149] [Info] 检测到平台: 哔哩哔哩 📺
[2025-08-04 10:14:27.150] [Info] 解析B站视频: https://www.bilibili.com/video/BV11m4y1h7Bv/?spm_id_from=333.337.search-card.all.click&vd_source=65708e9424b5f3409807c8675f4da097
[2025-08-04 10:14:27.153] [Info] 开始解析视频链接: https://www.bilibili.com/video/BV11m4y1h7Bv/?spm_id_from=333.337.search-card.all.click&vd_source=65708e9424b5f3409807c8675f4da097
[2025-08-04 10:14:27.154] [Info] 开始解析URL: https://www.bilibili.com/video/BV11m4y1h7Bv/?spm_id_from=333.337.search-card.all.click&vd_source=65708e9424b5f3409807c8675f4da097
[2025-08-04 10:14:27.156] [Info] 提取到BV号: BV11m4y1h7Bv
[2025-08-04 10:14:27.156] [Info] 提取到视频ID: BV11m4y1h7Bv, 页面: 1
[2025-08-04 10:14:27.490] [Info] 获取视频基本信息成功: 【AI绘画】PS接入SD插件 支持ControlNET  Auto-Photoshop-SD-Plugin安装及演示 大幅度提高工作效率
[2025-08-04 10:14:27.556] [Info] 选择页面 1，CID: 1126523796
[2025-08-04 10:14:27.556] [Info] 请求播放信息URL: https://api.bilibili.com/x/player/playurl?bvid=BV11m4y1h7Bv&cid=1126523796&qn=120&fnval=4048&fourk=1&try_look=1
[2025-08-04 10:14:27.667] [Info] 播放信息API响应: code=0, message=0
[2025-08-04 10:14:27.668] [Info] 获取视频播放信息成功
[2025-08-04 10:14:27.673] [Info] 开始合并视频播放信息，API响应码: 0
[2025-08-04 10:14:27.674] [Info] 播放信息数据获取成功，开始解析清晰度
[2025-08-04 10:14:27.675] [Info] 检测到DASH格式，开始解析DASH清晰度
[2025-08-04 10:14:27.678] [Info] 开始解析DASH清晰度信息
[2025-08-04 10:14:27.680] [Info] 找到 12 个视频流
[2025-08-04 10:14:27.683] [Info] 解析视频流: ID=80, 名称=1080P
[2025-08-04 10:14:27.691] [Info] 为清晰度 1080P 找到音频流
[2025-08-04 10:14:27.692] [Info] 成功添加清晰度选项: 1080P
[2025-08-04 10:14:27.692] [Info] 为清晰度 1080P 找到音频流
[2025-08-04 10:14:27.692] [Info] 解析视频流: ID=64, 名称=720P
[2025-08-04 10:14:27.692] [Info] 成功添加清晰度选项: 1080P
[2025-08-04 10:14:27.693] [Info] 为清晰度 720P 找到音频流
[2025-08-04 10:14:27.692] [Info] 解析视频流: ID=80, 名称=1080P
[2025-08-04 10:14:27.692] [Info] 解析视频流: ID=80, 名称=1080P
[2025-08-04 10:14:27.692] [Info] 成功添加清晰度选项: 1080P
[2025-08-04 10:14:27.693] [Info] 成功添加清晰度选项: 720P
[2025-08-04 10:14:27.692] [Info] 为清晰度 720P 找到音频流
[2025-08-04 10:14:27.710] [Info] 解析视频流: ID=32, 名称=480P
[2025-08-04 10:14:27.693] [Info] 解析视频流: ID=64, 名称=720P
[2025-08-04 10:14:27.693] [Info] 解析视频流: ID=64, 名称=720P
[2025-08-04 10:14:27.710] [Info] 成功添加清晰度选项: 360P
[2025-08-04 10:14:27.711] [Info] 解析视频流: ID=16, 名称=360P
[2025-08-04 10:14:27.711] [Info] 为清晰度 360P 找到音频流
[2025-08-04 10:14:27.693] [Info] 成功添加清晰度选项: 480P
[2025-08-04 10:14:27.711] [Info] 解析视频流: ID=16, 名称=360P
[2025-08-04 10:14:27.711] [Info] 为清晰度 360P 找到音频流
[2025-08-04 10:14:27.711] [Info] 成功添加清晰度选项: 360P
[2025-08-04 10:14:27.711] [Info] 清晰度解析完成，共找到 12 个清晰度选项
[2025-08-04 10:14:27.711] [Info] 视频信息解析完成，可用清晰度: 12
[2025-08-04 10:14:27.710] [Info] 为清晰度 480P 找到音频流
[2025-08-04 10:14:27.710] [Info] 成功添加清晰度选项: 480P
[2025-08-04 10:14:27.710] [Info] 解析视频流: ID=16, 名称=360P
[2025-08-04 10:14:27.710] [Info] 为清晰度 360P 找到音频流
[2025-08-04 10:14:27.693] [Info] 成功添加清晰度选项: 720P
[2025-08-04 10:14:27.693] [Info] 为清晰度 720P 找到音频流
[2025-08-04 10:14:27.693] [Info] 成功添加清晰度选项: 720P
[2025-08-04 10:14:27.711] [Info] 成功添加清晰度选项: 360P
[2025-08-04 10:14:27.693] [Info] 解析视频流: ID=32, 名称=480P
[2025-08-04 10:14:27.693] [Info] 为清晰度 480P 找到音频流
[2025-08-04 10:14:27.694] [Info] 解析视频流: ID=32, 名称=480P
[2025-08-04 10:14:27.694] [Info] 成功添加清晰度选项: 480P
[2025-08-04 10:14:27.692] [Info] 为清晰度 1080P 找到音频流
[2025-08-04 10:14:27.694] [Info] 为清晰度 480P 找到音频流
[2025-08-04 10:14:27.785] [Info] 选择页面 1，CID: 1126523796
[2025-08-04 10:14:27.785] [Info] 请求播放信息URL: https://api.bilibili.com/x/player/playurl?bvid=BV11m4y1h7Bv&cid=1126523796&qn=120&fnval=4048&fourk=1&try_look=1
[2025-08-04 10:14:27.900] [Info] 播放信息API响应: code=0, message=0
[2025-08-04 10:14:41.552] [Info] 选择页面 1，CID: 1126523796
[2025-08-04 10:14:41.553] [Info] 请求播放信息URL: https://api.bilibili.com/x/player/playurl?bvid=BV11m4y1h7Bv&cid=1126523796&qn=120&fnval=4048&fourk=1&try_look=1
[2025-08-04 10:14:41.703] [Info] 播放信息API响应: code=0, message=0
[2025-08-04 10:14:41.708] [Info] 开始下载文件: https://upos-sz-estghw.bilivideo.com/upgcxcode/96/37/1126523796/1126523796_nb3-1-30080.m4s?e=ig8euxZM2rNcNbdlhoNvNC8BqJIzNbfqXBvEqxTEto8BTrNvN0GvT90W5JZMkX_YN0MvXg8gNEV4NC8xNEV4N03eN0B5tZlqNxTEto8BTrNvNeZVuJ10Kj_g2UB02J0mN0B5tZlqNCNEto8BTrNvNC7MTX502C8f2jmMQJ6mqF2fka1mqx6gqj0eN0B599M=&nbs=1&oi=612907262&os=upos&og=hw&platform=pc&trid=f5d88c5ada7641b39879cf38ac57801u&uipk=5&gen=playurlv3&mid=0&deadline=1754280880&upsig=64c1a3ab7b345ce35c270f8603884ce7&uparams=e,nbs,oi,os,og,platform,trid,uipk,gen,mid,deadline&bvc=vod&nettype=0&bw=161066&buvid=&build=0&dl=0&f=u_0_0&agrr=0&orderid=0,3 -> C:\Users\<USER>\Desktop\bilibli下载器\BilibiliDownloader\Downloads\【AI绘画】PS接入SD插件 支持ControlNET  Auto-Photoshop-SD-Plugin安装及演示 大幅度提高工作效率_BV11m4y1h7Bv\【AI绘画】PS接入SD插件 支持ControlNET  Auto-Photoshop-SD-Plugin安装及演示 大幅度提高工作效率.mp4
[2025-08-04 10:14:41.893] [Info] 下载响应状态: OK for https://upos-sz-estghw.bilivideo.com/upgcxcode/96/37/1126523796/1126523796_nb3-1-30080.m4s?e=ig8euxZM2rNcNbdlhoNvNC8BqJIzNbfqXBvEqxTEto8BTrNvN0GvT90W5JZMkX_YN0MvXg8gNEV4NC8xNEV4N03eN0B5tZlqNxTEto8BTrNvNeZVuJ10Kj_g2UB02J0mN0B5tZlqNCNEto8BTrNvNC7MTX502C8f2jmMQJ6mqF2fka1mqx6gqj0eN0B599M=&nbs=1&oi=612907262&os=upos&og=hw&platform=pc&trid=f5d88c5ada7641b39879cf38ac57801u&uipk=5&gen=playurlv3&mid=0&deadline=1754280880&upsig=64c1a3ab7b345ce35c270f8603884ce7&uparams=e,nbs,oi,os,og,platform,trid,uipk,gen,mid,deadline&bvc=vod&nettype=0&bw=161066&buvid=&build=0&dl=0&f=u_0_0&agrr=0&orderid=0,3
[2025-08-04 10:14:45.647] [Info] 文件下载完成，大小: 16.74 MB
[2025-08-04 10:14:53.225] [Info] 选择页面 1，CID: 1126523796
[2025-08-04 10:14:53.225] [Info] 请求播放信息URL: https://api.bilibili.com/x/player/playurl?bvid=BV11m4y1h7Bv&cid=1126523796&qn=120&fnval=4048&fourk=1&try_look=1
[2025-08-04 10:14:53.371] [Info] 播放信息API响应: code=0, message=0
[2025-08-04 10:14:53.371] [Info] 开始下载文件: https://upos-sz-estghw.bilivideo.com/upgcxcode/96/37/1126523796/1126523796_nb3-1-30080.m4s?e=ig8euxZM2rNcNbdlhoNvNC8BqJIzNbfqXBvEqxTEto8BTrNvN0GvT90W5JZMkX_YN0MvXg8gNEV4NC8xNEV4N03eN0B5tZlqNxTEto8BTrNvNeZVuJ10Kj_g2UB02J0mN0B5tZlqNCNEto8BTrNvNC7MTX502C8f2jmMQJ6mqF2fka1mqx6gqj0eN0B599M=&uipk=5&oi=612907262&platform=pc&deadline=1754280892&gen=playurlv3&os=upos&trid=c0555f65b288431bbe3e26bcb6f3921u&mid=0&nbs=1&og=hw&upsig=1f831b4e001b9562e95cee7c910d22fb&uparams=e,uipk,oi,platform,deadline,gen,os,trid,mid,nbs,og&bvc=vod&nettype=0&bw=161066&f=u_0_0&agrr=0&buvid=&build=0&dl=0&orderid=0,3 -> C:\Users\<USER>\Desktop\bilibli下载器\BilibiliDownloader\Downloads\【AI绘画】PS接入SD插件 支持ControlNET  Auto-Photoshop-SD-Plugin安装及演示 大幅度提高工作效率_BV11m4y1h7Bv\【AI绘画】PS接入SD插件 支持ControlNET  Auto-Photoshop-SD-Plugin安装及演示 大幅度提高工作效率.mp4
[2025-08-04 10:14:53.412] [Info] 下载响应状态: OK for https://upos-sz-estghw.bilivideo.com/upgcxcode/96/37/1126523796/1126523796_nb3-1-30080.m4s?e=ig8euxZM2rNcNbdlhoNvNC8BqJIzNbfqXBvEqxTEto8BTrNvN0GvT90W5JZMkX_YN0MvXg8gNEV4NC8xNEV4N03eN0B5tZlqNxTEto8BTrNvNeZVuJ10Kj_g2UB02J0mN0B5tZlqNCNEto8BTrNvNC7MTX502C8f2jmMQJ6mqF2fka1mqx6gqj0eN0B599M=&uipk=5&oi=612907262&platform=pc&deadline=1754280892&gen=playurlv3&os=upos&trid=c0555f65b288431bbe3e26bcb6f3921u&mid=0&nbs=1&og=hw&upsig=1f831b4e001b9562e95cee7c910d22fb&uparams=e,uipk,oi,platform,deadline,gen,os,trid,mid,nbs,og&bvc=vod&nettype=0&bw=161066&f=u_0_0&agrr=0&buvid=&build=0&dl=0&orderid=0,3
[2025-08-04 10:14:54.422] [Info] 文件下载完成，大小: 16.74 MB
[2025-08-04 10:15:16.487] [Info] 检测到平台: 哔哩哔哩 📺
[2025-08-04 10:15:16.487] [Info] 解析B站视频: https://www.bilibili.com/video/BV1eL411176f?spm_id_from=333.788.videopod.sections&vd_source=65708e9424b5f3409807c8675f4da097
[2025-08-04 10:15:16.487] [Info] 开始解析URL: https://www.bilibili.com/video/BV1eL411176f?spm_id_from=333.788.videopod.sections&vd_source=65708e9424b5f3409807c8675f4da097
[2025-08-04 10:15:16.488] [Info] 提取到视频ID: BV1eL411176f, 页面: 1
[2025-08-04 10:15:16.487] [Info] 提取到BV号: BV1eL411176f
[2025-08-04 10:15:16.487] [Info] 开始解析视频链接: https://www.bilibili.com/video/BV1eL411176f?spm_id_from=333.788.videopod.sections&vd_source=65708e9424b5f3409807c8675f4da097
[2025-08-04 10:15:16.754] [Info] 获取视频基本信息成功: 【AI绘画入门教程】AI绘画入门  模型详解  保姆教程 Stable Diffusion模型到哪下载？
[2025-08-04 10:15:16.844] [Info] 选择页面 1，CID: 1040393284
[2025-08-04 10:15:16.845] [Info] 请求播放信息URL: https://api.bilibili.com/x/player/playurl?bvid=BV1eL411176f&cid=1040393284&qn=120&fnval=4048&fourk=1&try_look=1
[2025-08-04 10:15:16.953] [Info] 播放信息API响应: code=0, message=0
[2025-08-04 10:15:16.953] [Info] 获取视频播放信息成功
[2025-08-04 10:15:16.953] [Info] 开始合并视频播放信息，API响应码: 0
[2025-08-04 10:15:16.954] [Info] 播放信息数据获取成功，开始解析清晰度
[2025-08-04 10:15:16.954] [Info] 检测到DASH格式，开始解析DASH清晰度
[2025-08-04 10:15:16.954] [Info] 开始解析DASH清晰度信息
[2025-08-04 10:15:16.954] [Info] 找到 12 个视频流
[2025-08-04 10:15:16.954] [Info] 解析视频流: ID=80, 名称=1080P
[2025-08-04 10:15:16.955] [Info] 为清晰度 1080P 找到音频流
[2025-08-04 10:15:16.955] [Info] 成功添加清晰度选项: 1080P
[2025-08-04 10:15:16.955] [Info] 为清晰度 1080P 找到音频流
[2025-08-04 10:15:16.955] [Info] 解析视频流: ID=80, 名称=1080P
[2025-08-04 10:15:16.955] [Info] 成功添加清晰度选项: 1080P
[2025-08-04 10:15:16.955] [Info] 解析视频流: ID=80, 名称=1080P
[2025-08-04 10:15:16.956] [Info] 为清晰度 1080P 找到音频流
[2025-08-04 10:15:16.956] [Info] 成功添加清晰度选项: 1080P
[2025-08-04 10:15:16.956] [Info] 解析视频流: ID=64, 名称=720P
[2025-08-04 10:15:16.956] [Info] 为清晰度 720P 找到音频流
[2025-08-04 10:15:16.956] [Info] 成功添加清晰度选项: 720P
[2025-08-04 10:15:16.956] [Info] 解析视频流: ID=64, 名称=720P
[2025-08-04 10:15:16.956] [Info] 为清晰度 720P 找到音频流
[2025-08-04 10:15:16.957] [Info] 成功添加清晰度选项: 720P
[2025-08-04 10:15:16.957] [Info] 解析视频流: ID=64, 名称=720P
[2025-08-04 10:15:16.957] [Info] 为清晰度 720P 找到音频流
[2025-08-04 10:15:16.958] [Info] 解析视频流: ID=32, 名称=480P
[2025-08-04 10:15:16.957] [Info] 解析视频流: ID=32, 名称=480P
[2025-08-04 10:15:16.957] [Info] 为清晰度 480P 找到音频流
[2025-08-04 10:15:16.957] [Info] 成功添加清晰度选项: 480P
[2025-08-04 10:15:16.958] [Info] 解析视频流: ID=32, 名称=480P
[2025-08-04 10:15:16.958] [Info] 为清晰度 480P 找到音频流
[2025-08-04 10:15:16.958] [Info] 成功添加清晰度选项: 480P
[2025-08-04 10:15:16.957] [Info] 成功添加清晰度选项: 720P
[2025-08-04 10:15:16.958] [Info] 为清晰度 480P 找到音频流
[2025-08-04 10:15:16.958] [Info] 成功添加清晰度选项: 480P
[2025-08-04 10:15:16.958] [Info] 解析视频流: ID=16, 名称=360P
[2025-08-04 10:15:16.959] [Info] 为清晰度 360P 找到音频流
[2025-08-04 10:15:16.959] [Info] 成功添加清晰度选项: 360P
[2025-08-04 10:15:16.959] [Info] 解析视频流: ID=16, 名称=360P
[2025-08-04 10:15:16.959] [Info] 为清晰度 360P 找到音频流
[2025-08-04 10:15:16.959] [Info] 成功添加清晰度选项: 360P
[2025-08-04 10:15:16.959] [Info] 解析视频流: ID=16, 名称=360P
[2025-08-04 10:15:16.959] [Info] 为清晰度 360P 找到音频流
[2025-08-04 10:15:16.960] [Info] 成功添加清晰度选项: 360P
[2025-08-04 10:15:16.960] [Info] 清晰度解析完成，共找到 12 个清晰度选项
[2025-08-04 10:15:16.960] [Info] 视频信息解析完成，可用清晰度: 12
[2025-08-04 10:15:17.036] [Info] 选择页面 1，CID: 1040393284
[2025-08-04 10:15:17.037] [Info] 请求播放信息URL: https://api.bilibili.com/x/player/playurl?bvid=BV1eL411176f&cid=1040393284&qn=120&fnval=4048&fourk=1&try_look=1
[2025-08-04 10:15:17.140] [Info] 播放信息API响应: code=0, message=0
[2025-08-04 10:15:19.961] [Info] 选择页面 1，CID: 1040393284
[2025-08-04 10:15:19.961] [Info] 请求播放信息URL: https://api.bilibili.com/x/player/playurl?bvid=BV1eL411176f&cid=1040393284&qn=120&fnval=4048&fourk=1&try_look=1
[2025-08-04 10:15:20.069] [Info] 播放信息API响应: code=0, message=0
[2025-08-04 10:15:20.070] [Info] 开始下载文件: https://upos-sz-estghw.bilivideo.com/upgcxcode/84/32/1040393284/1040393284_nb3-1-30080.m4s?e=ig8euxZM2rNcNbdlhoNvNC8BqJIzNbfqXBvEqxTEto8BTrNvN0GvT90W5JZMkX_YN0MvXg8gNEV4NC8xNEV4N03eN0B5tZlqNxTEto8BTrNvNeZVuJ10Kj_g2UB02J0mN0B5tZlqNCNEto8BTrNvNC7MTX502C8f2jmMQJ6mqF2fka1mqx6gqj0eN0B599M=&deadline=1754280919&mid=0&os=upos&og=hw&nbs=1&uipk=5&oi=612907262&platform=pc&trid=6793ad7dfb9441539ed3a53c76fbc9bu&gen=playurlv3&upsig=1a39deb12523c5ad9f9a3f801ac497eb&uparams=e,deadline,mid,os,og,nbs,uipk,oi,platform,trid,gen&bvc=vod&nettype=0&bw=194431&build=0&dl=0&f=u_0_0&agrr=0&buvid=&orderid=0,3 -> C:\Users\<USER>\Desktop\bilibli下载器\BilibiliDownloader\Downloads\【AI绘画入门教程】AI绘画入门  模型详解  保姆教程 Stable Diffusion模型到哪下载？_BV1eL411176f\【AI绘画入门教程】AI绘画入门  模型详解  保姆教程 Stable Diffusion模型到哪下载？.mp4
[2025-08-04 10:15:20.215] [Info] 下载响应状态: OK for https://upos-sz-estghw.bilivideo.com/upgcxcode/84/32/1040393284/1040393284_nb3-1-30080.m4s?e=ig8euxZM2rNcNbdlhoNvNC8BqJIzNbfqXBvEqxTEto8BTrNvN0GvT90W5JZMkX_YN0MvXg8gNEV4NC8xNEV4N03eN0B5tZlqNxTEto8BTrNvNeZVuJ10Kj_g2UB02J0mN0B5tZlqNCNEto8BTrNvNC7MTX502C8f2jmMQJ6mqF2fka1mqx6gqj0eN0B599M=&deadline=1754280919&mid=0&os=upos&og=hw&nbs=1&uipk=5&oi=612907262&platform=pc&trid=6793ad7dfb9441539ed3a53c76fbc9bu&gen=playurlv3&upsig=1a39deb12523c5ad9f9a3f801ac497eb&uparams=e,deadline,mid,os,og,nbs,uipk,oi,platform,trid,gen&bvc=vod&nettype=0&bw=194431&build=0&dl=0&f=u_0_0&agrr=0&buvid=&orderid=0,3
[2025-08-04 10:15:23.319] [Info] 文件下载完成，大小: 15.02 MB
[2025-08-04 10:15:47.661] [Info] 检测到平台: 小红书 📖
[2025-08-04 10:16:18.352] [Info] 已保存 1 个账号
[2025-08-04 10:16:20.860] [Info] 已保存 1 个账号
[2025-08-04 10:16:23.760] [Info] 检测到平台: 小红书 📖
[2025-08-04 10:16:23.761] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688841b6000000002201ed12?xsec_token=ABsc82Of7ufb2V33AsOShP8EcF5c2_in9Hz0ebfFsIA0Q=&xsec_source=pc_user
[2025-08-04 10:16:23.763] [Info] 开始验证小红书Cookie...
[2025-08-04 10:16:23.763] [Warning] Cookie缺少必要字段: web_session
[2025-08-04 10:16:23.764] [Warning] Cookie格式不正确
[2025-08-04 10:16:23.764] [Warning] 小红书Cookie无效或已过期
[2025-08-04 10:16:23.764] [Warning] 小红书Cookie无效或已过期，请重新登录
[2025-08-04 10:16:23.764] [Error] 小红书解析失败: 小红书登录失败，请检查Cookie或重新登录
[2025-08-04 10:16:29.028] [Info] 已保存 1 个账号
[2025-08-04 10:25:27.151] [Info] 已加载 1 个账号
[2025-08-04 10:25:27.210] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:25:27.211] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:25:27.259] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:25:27.259] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:25:27.260] [Info] 已加载 7 个平台下载器
[2025-08-04 10:25:27.261] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🌹小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 10:25:27.263] [Info] 程序初始化完成
[2025-08-04 10:25:39.043] [Info] 已保存 0 个账号
[2025-08-04 10:26:38.048] [Info] 检测到平台: 小红书 🌹
[2025-08-04 10:26:54.387] [Info] 已保存 1 个账号
[2025-08-04 10:26:57.984] [Info] 检测到平台: 小红书 🌹
[2025-08-04 10:27:00.748] [Info] 已保存 1 个账号
[2025-08-04 10:27:02.730] [Info] 检测到平台: 小红书 🌹
[2025-08-04 10:27:02.731] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688841b6000000002201ed12?xsec_token=ABsc82Of7ufb2V33AsOShP8EcF5c2_in9Hz0ebfFsIA0Q=&xsec_source=pc_user
[2025-08-04 10:27:02.731] [Info] 开始验证小红书Cookie...
[2025-08-04 10:27:03.265] [Warning] 小红书Cookie验证失败
[2025-08-04 10:27:03.266] [Error] 解析小红书内容失败: 小红书Cookie无效，请在账号管理中更新Cookie
[2025-08-04 10:27:10.125] [Info] 检测到平台: 小红书 🌹
[2025-08-04 10:27:10.126] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688841b6000000002201ed12?xsec_token=ABsc82Of7ufb2V33AsOShP8EcF5c2_in9Hz0ebfFsIA0Q=&xsec_source=pc_user
[2025-08-04 10:27:10.126] [Info] 开始验证小红书Cookie...
[2025-08-04 10:27:10.489] [Warning] 小红书Cookie验证失败
[2025-08-04 10:27:10.489] [Error] 解析小红书内容失败: 小红书Cookie无效，请在账号管理中更新Cookie
[2025-08-04 10:33:06.723] [Info] 已加载 1 个账号
[2025-08-04 10:33:07.188] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:33:07.189] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:33:07.772] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:33:07.773] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:33:07.774] [Info] 已加载 7 个平台下载器
[2025-08-04 10:33:07.774] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🌹小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 10:33:07.777] [Info] 程序初始化完成
[2025-08-04 10:35:56.399] [Info] 检测到平台: 小红书 🌹
[2025-08-04 10:35:56.400] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688841b6000000002201ed12?xsec_token=ABsc82Of7ufb2V33AsOShP8EcF5c2_in9Hz0ebfFsIA0Q=&xsec_source=pc_user
[2025-08-04 10:35:56.923] [Warning] 未找到__INITIAL_STATE__数据
[2025-08-04 10:35:56.923] [Error] 解析小红书内容失败: 解析笔记数据失败
[2025-08-04 10:37:29.973] [Info] 已加载 1 个账号
[2025-08-04 10:37:30.032] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:37:30.034] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:37:30.080] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:37:30.080] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:37:30.082] [Info] 已加载 7 个平台下载器
[2025-08-04 10:37:30.082] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🌹小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 10:37:30.084] [Info] 程序初始化完成
[2025-08-04 10:37:43.591] [Info] 检测到平台: 小红书 🌹
[2025-08-04 10:37:43.592] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688841b6000000002201ed12?xsec_token=ABsc82Of7ufb2V33AsOShP8EcF5c2_in9Hz0ebfFsIA0Q=&xsec_source=pc_user
[2025-08-04 10:37:44.167] [Warning] 未找到__INITIAL_STATE__数据
[2025-08-04 10:37:44.167] [Error] 解析小红书内容失败: 解析笔记数据失败
[2025-08-04 10:38:32.189] [Info] 已加载 1 个账号
[2025-08-04 10:38:32.248] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:38:32.250] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:38:32.300] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:38:32.300] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:38:32.301] [Info] 已加载 7 个平台下载器
[2025-08-04 10:38:32.302] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🌹小红书, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 10:38:32.304] [Info] 程序初始化完成
[2025-08-04 10:38:38.632] [Info] 检测到平台: 小红书 🌹
[2025-08-04 10:38:38.633] [Info] 解析小红书内容: https://www.xiaohongshu.com/explore/688841b6000000002201ed12?xsec_token=ABsc82Of7ufb2V33AsOShP8EcF5c2_in9Hz0ebfFsIA0Q=&xsec_source=pc_user
[2025-08-04 10:38:39.180] [Warning] 未找到__INITIAL_STATE__数据
[2025-08-04 10:38:39.181] [Error] 解析小红书内容失败: 解析笔记数据失败
[2025-08-04 10:42:20.916] [Info] 已加载 1 个账号
[2025-08-04 10:42:20.975] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:42:20.976] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:42:21.029] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:42:21.029] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:42:21.030] [Info] 已加载 6 个平台下载器
[2025-08-04 10:42:21.030] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 10:42:21.033] [Info] 程序初始化完成
[2025-08-04 10:42:33.918] [Info] 已保存 0 个账号
[2025-08-04 10:42:53.504] [Info] 已加载 0 个账号
[2025-08-04 10:42:53.571] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:42:53.573] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:42:53.629] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:42:53.629] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:42:53.629] [Info] 已加载 6 个平台下载器
[2025-08-04 10:42:53.630] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 10:42:53.633] [Info] 程序初始化完成
[2025-08-04 10:42:58.786] [Warning] 未找到支持的平台: https://www.xiaohongshu.com/explore/688c9cd6000000000403fef3?xsec_token=ABx9u-0ZCAvSiYihqcnzoadgJShKZ-zOjBGDQWbYjD9oM=&xsec_source=pc_feed
[2025-08-04 10:43:06.307] [Warning] 未找到支持的平台: https://www.xiaohongshu.com/explore/688c9cd6000000000403fef3?xsec_token=ABx9u-0ZCAvSiYihqcnzoadgJShKZ-zOjBGDQWbYjD9oM=&xsec_source=pc_feed
[2025-08-04 10:43:29.105] [Info] 已加载 0 个账号
[2025-08-04 10:43:29.164] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:43:29.166] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:43:29.216] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:43:29.216] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:43:29.216] [Info] 已加载 6 个平台下载器
[2025-08-04 10:43:29.217] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺
[2025-08-04 10:43:29.220] [Info] 程序初始化完成
[2025-08-04 10:43:32.465] [Warning] 未找到支持的平台: https://www.xiaohongshu.com/explore/688c9cd6000000000403fef3?xsec_token=ABx9u-0ZCAvSiYihqcnzoadgJShKZ-zOjBGDQWbYjD9oM=&xsec_source=pc_feed
[2025-08-04 10:43:57.440] [Warning] 未找到支持的平台: https://www.xiaohongshu.com/explore/688c9cd6000000000403fef3?xsec_token=ABx9u-0ZCAvSiYihqcnzoadgJShKZ-zOjBGDQWbYjD9oM=&xsec_source=pc_feed
[2025-08-04 10:55:19.953] [Info] 已加载 0 个账号
[2025-08-04 10:55:20.013] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:55:20.014] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:55:20.063] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:55:20.063] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:55:20.064] [Info] 已加载 7 个平台下载器
[2025-08-04 10:55:20.065] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 10:55:20.068] [Info] 程序初始化完成
[2025-08-04 10:56:44.899] [Info] 已加载 0 个账号
[2025-08-04 10:56:44.958] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:56:44.960] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:56:45.008] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 10:56:45.008] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 10:56:45.010] [Info] 已加载 7 个平台下载器
[2025-08-04 10:56:45.010] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 10:56:45.013] [Info] 程序初始化完成
[2025-08-04 11:05:16.698] [Info] 已加载 0 个账号
[2025-08-04 11:05:16.757] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:05:16.759] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:05:16.807] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:05:16.807] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:05:16.809] [Info] 已加载 7 个平台下载器
[2025-08-04 11:05:16.809] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 11:05:16.812] [Info] 程序初始化完成
[2025-08-04 11:05:31.500] [Info] 检测到平台: 小红书 🌹
[2025-08-04 11:05:31.500] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c9cd6000000000403fef3?xsec_token=ABx9u-0ZCAvSiYihqcnzoadgJShKZ-zOjBGDQWbYjD9oM=&xsec_source=pc_feed
[2025-08-04 11:05:31.501] [Info] 提取到作品ID: 688c9cd6000000000403fef3
[2025-08-04 11:05:31.501] [Info] 开始获取小红书作品详情: 688c9cd6000000000403fef3
[2025-08-04 11:05:32.042] [Warning] 未找到页面初始数据
[2025-08-04 11:05:32.317] [Warning] API请求失败: NotAcceptable
[2025-08-04 11:05:32.317] [Error] 所有方法都无法获取作品详情
[2025-08-04 11:05:32.318] [Error] 获取作品详细信息失败
[2025-08-04 11:05:48.342] [Info] 检测到平台: 小红书 🌹
[2025-08-04 11:05:48.342] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c51db000000002501751f?xsec_token=ABx9u-0ZCAvSiYihqcnzoadk1MfSpN5QappVndnwiSy2k=&xsec_source=pc_feed
[2025-08-04 11:05:48.342] [Info] 提取到作品ID: 688c51db000000002501751f
[2025-08-04 11:05:48.342] [Info] 开始获取小红书作品详情: 688c51db000000002501751f
[2025-08-04 11:05:48.735] [Warning] 未找到页面初始数据
[2025-08-04 11:05:48.809] [Warning] API请求失败: NotAcceptable
[2025-08-04 11:05:48.811] [Error] 所有方法都无法获取作品详情
[2025-08-04 11:05:48.813] [Error] 获取作品详细信息失败
[2025-08-04 11:23:32.106] [Info] 已加载 0 个账号
[2025-08-04 11:23:32.165] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:23:32.166] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:23:32.214] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:23:32.214] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:23:32.215] [Info] 已加载 7 个平台下载器
[2025-08-04 11:23:32.216] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 11:23:32.219] [Info] 程序初始化完成
[2025-08-04 11:23:50.098] [Info] 检测到平台: 小红书 🌹
[2025-08-04 11:23:50.099] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 11:23:50.100] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 11:23:50.100] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 11:23:50.101] [Info] 访问网页版: https://www.xiaohongshu.com/explore/688c586300000000030254b9
[2025-08-04 11:23:50.101] [Warning] 未提供Cookie，尝试无登录访问
[2025-08-04 11:23:53.317] [Info] 网页请求响应状态: OK
[2025-08-04 11:23:53.320] [Info] 成功获取页面内容，长度: 70864
[2025-08-04 11:23:53.321] [Info] 开始解析HTML页面数据
[2025-08-04 11:23:53.321] [Warning] 未找到页面初始数据，尝试其他解析方法
[2025-08-04 11:23:53.322] [Info] 尝试从HTML中直接解析作品信息
[2025-08-04 11:23:53.323] [Warning] 无法从HTML中提取有效信息
[2025-08-04 11:23:53.567] [Warning] API请求失败: NotAcceptable
[2025-08-04 11:23:53.567] [Error] 所有方法都无法获取作品详情
[2025-08-04 11:23:53.567] [Error] 获取作品详细信息失败
[2025-08-04 11:25:16.221] [Info] 已加载 0 个账号
[2025-08-04 11:25:16.282] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:25:16.283] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:25:16.333] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:25:16.333] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:25:16.334] [Info] 已加载 7 个平台下载器
[2025-08-04 11:25:16.335] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 11:25:16.337] [Info] 程序初始化完成
[2025-08-04 11:26:01.156] [Info] 检测到平台: 小红书 🌹
[2025-08-04 11:26:01.157] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 11:26:01.157] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 11:26:01.158] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 11:26:01.159] [Info] 访问网页版: https://www.xiaohongshu.com/explore/688c586300000000030254b9
[2025-08-04 11:26:01.159] [Warning] 未提供Cookie，尝试无登录访问
[2025-08-04 11:26:03.803] [Info] 网页请求响应状态: OK
[2025-08-04 11:26:03.804] [Error] 页面不存在或已被删除
[2025-08-04 11:26:04.063] [Warning] API请求失败: NotAcceptable
[2025-08-04 11:26:04.063] [Error] 所有方法都无法获取作品详情
[2025-08-04 11:26:04.063] [Error] 获取作品详细信息失败
[2025-08-04 11:28:35.980] [Info] 已加载 0 个账号
[2025-08-04 11:28:36.043] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:28:36.044] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:28:36.101] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:28:36.101] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:28:36.103] [Info] 已加载 7 个平台下载器
[2025-08-04 11:28:36.104] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 11:28:36.107] [Info] 程序初始化完成
[2025-08-04 11:28:48.229] [Info] 检测到平台: 小红书 🌹
[2025-08-04 11:28:48.230] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 11:28:48.231] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 11:28:48.231] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 11:28:48.232] [Info] 访问网页版: https://www.xiaohongshu.com/explore/688c586300000000030254b9
[2025-08-04 11:28:48.232] [Warning] 未提供Cookie，尝试无登录访问
[2025-08-04 11:28:49.854] [Info] 网页请求响应状态: OK
[2025-08-04 11:28:49.855] [Info] 获取到页面内容，长度: 275621
[2025-08-04 11:28:49.855] [Warning] 检测到可能的压缩内容，尝试手动解压缩
[2025-08-04 11:28:49.861] [Error] Gzip解压缩失败: The archive entry was compressed using an unsupported compression method.
[2025-08-04 11:28:49.861] [Error] 手动解压缩失败: The archive entry was compressed using an unsupported compression method.
[2025-08-04 11:28:49.861] [Error] 页面不存在或已被删除
[2025-08-04 11:28:50.113] [Warning] API请求失败: NotAcceptable
[2025-08-04 11:28:50.114] [Error] 所有方法都无法获取作品详情
[2025-08-04 11:28:50.114] [Error] 获取作品详细信息失败
[2025-08-04 11:30:18.674] [Info] 检测到平台: 小红书 🌹
[2025-08-04 11:30:18.675] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 11:30:18.675] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 11:30:18.675] [Info] 访问网页版: https://www.xiaohongshu.com/explore/688c586300000000030254b9
[2025-08-04 11:30:18.675] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 11:30:18.675] [Warning] 未提供Cookie，尝试无登录访问
[2025-08-04 11:32:36.649] [Info] 已加载 0 个账号
[2025-08-04 11:32:36.709] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:32:36.710] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:32:36.758] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:32:36.758] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:32:36.759] [Info] 已加载 7 个平台下载器
[2025-08-04 11:32:36.760] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 11:32:36.762] [Info] 程序初始化完成
[2025-08-04 11:32:43.851] [Info] 检测到平台: 小红书 🌹
[2025-08-04 11:32:43.852] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 11:32:43.853] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 11:32:43.853] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 11:32:43.854] [Info] 访问网页版: https://www.xiaohongshu.com/explore/688c586300000000030254b9
[2025-08-04 11:32:43.854] [Warning] 未提供Cookie，尝试无登录访问
[2025-08-04 11:32:47.081] [Info] 网页请求响应状态: OK
[2025-08-04 11:32:47.082] [Info] 获取到页面内容，长度: 275621
[2025-08-04 11:32:47.082] [Warning] 检测到可能的压缩内容，尝试手动解压缩
[2025-08-04 11:32:47.083] [Error] Gzip解压缩失败: The archive entry was compressed using an unsupported compression method.
[2025-08-04 11:32:47.083] [Error] 手动解压缩失败: The archive entry was compressed using an unsupported compression method.
[2025-08-04 11:32:47.083] [Error] 页面不存在或已被删除
[2025-08-04 11:32:47.348] [Warning] API请求失败: NotAcceptable
[2025-08-04 11:32:47.349] [Error] 所有方法都无法获取作品详情
[2025-08-04 11:32:47.349] [Error] 获取作品详细信息失败
[2025-08-04 11:46:10.714] [Info] 已加载 0 个账号
[2025-08-04 11:46:10.779] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:46:10.780] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:46:10.833] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:46:10.833] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:46:10.835] [Info] 已加载 7 个平台下载器
[2025-08-04 11:46:10.835] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 11:46:10.839] [Info] 程序初始化完成
[2025-08-04 11:46:49.114] [Info] 检测到平台: 小红书 🌹
[2025-08-04 11:46:49.115] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 11:46:49.116] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 11:46:49.117] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 11:46:49.117] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 11:46:50.652] [Error] 解析HTML数据失败: 'u' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 2913.
[2025-08-04 11:46:50.653] [Error] 提取作品详情失败
[2025-08-04 11:46:50.653] [Error] 获取作品详细信息失败
[2025-08-04 11:48:22.401] [Info] 已加载 0 个账号
[2025-08-04 11:48:22.467] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:48:22.469] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:48:22.523] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:48:22.523] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:48:22.525] [Info] 已加载 7 个平台下载器
[2025-08-04 11:48:22.525] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 11:48:22.528] [Info] 程序初始化完成
[2025-08-04 11:49:24.112] [Info] 已加载 0 个账号
[2025-08-04 11:49:24.169] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:49:24.170] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:49:24.219] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:49:24.219] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:49:24.220] [Info] 已加载 7 个平台下载器
[2025-08-04 11:49:24.221] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 11:49:24.223] [Info] 程序初始化完成
[2025-08-04 11:51:25.419] [Info] 检测到平台: 小红书 🌹
[2025-08-04 11:51:25.420] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 11:51:25.421] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 11:51:25.422] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 11:51:25.422] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 11:51:27.040] [Error] 解析HTML数据失败: 'u' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 2913.
[2025-08-04 11:51:27.040] [Error] 提取作品详情失败
[2025-08-04 11:51:27.041] [Error] 获取作品详细信息失败
[2025-08-04 11:53:05.156] [Info] 已加载 0 个账号
[2025-08-04 11:53:05.215] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:53:05.216] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:53:05.266] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 11:53:05.266] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 11:53:05.267] [Info] 已加载 7 个平台下载器
[2025-08-04 11:53:05.268] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 11:53:05.270] [Info] 程序初始化完成
[2025-08-04 12:01:25.012] [Info] 已加载 0 个账号
[2025-08-04 12:01:25.097] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 12:01:25.098] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 12:01:25.169] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 12:01:25.169] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 12:01:25.171] [Info] 已加载 7 个平台下载器
[2025-08-04 12:01:25.171] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 12:01:25.175] [Info] 程序初始化完成
[2025-08-04 12:11:14.521] [Info] 检测到平台: 小红书 🌹
[2025-08-04 12:11:14.522] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 12:11:14.523] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 12:11:14.524] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 12:11:14.524] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 12:11:16.134] [Info] 提取的JSON数据长度: 8015
[2025-08-04 12:11:16.134] [Info] JSON数据开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 12:11:16.136] [Error] JSON解析失败: 'u' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 2913.
[2025-08-04 12:11:16.136] [Error] 错误位置: Line 0, Position 2913
[2025-08-04 12:11:16.137] [Error] 提取作品详情失败
[2025-08-04 12:11:16.137] [Error] 获取作品详细信息失败
[2025-08-04 12:11:36.296] [Info] 检测到平台: 小红书 🌹
[2025-08-04 12:11:36.296] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 12:11:36.296] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 12:11:36.296] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 12:11:36.296] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 12:11:36.297] [Warning] 请求失败 (重试 1/3): This instance has already started one or more requests. Properties can only be modified before sending the first request.
[2025-08-04 12:11:38.305] [Warning] 请求失败 (重试 2/3): This instance has already started one or more requests. Properties can only be modified before sending the first request.
[2025-08-04 12:11:42.314] [Warning] 请求失败 (重试 3/3): This instance has already started one or more requests. Properties can only be modified before sending the first request.
[2025-08-04 12:11:42.314] [Error] 获取作品详情失败: This instance has already started one or more requests. Properties can only be modified before sending the first request.
[2025-08-04 12:11:42.314] [Error] 获取作品详细信息失败
[2025-08-04 12:12:54.482] [Info] 已加载 0 个账号
[2025-08-04 12:12:54.541] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 12:12:54.542] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 12:12:54.591] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 12:12:54.591] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 12:12:54.592] [Info] 已加载 7 个平台下载器
[2025-08-04 12:12:54.592] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 12:12:54.595] [Info] 程序初始化完成
[2025-08-04 12:12:57.906] [Info] 检测到平台: 小红书 🌹
[2025-08-04 12:12:57.907] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 12:12:57.908] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 12:12:57.909] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 12:12:57.909] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 12:12:59.468] [Info] 提取的JSON数据长度: 8015
[2025-08-04 12:12:59.468] [Info] JSON数据开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 12:12:59.470] [Error] JSON解析失败: 'u' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 2913.
[2025-08-04 12:12:59.470] [Error] 错误位置: Line 0, Position 2913
[2025-08-04 12:12:59.470] [Error] 提取作品详情失败
[2025-08-04 12:12:59.471] [Error] 获取作品详细信息失败
[2025-08-04 12:15:00.099] [Info] 已加载 0 个账号
[2025-08-04 12:15:00.158] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 12:15:00.159] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 12:15:00.209] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 12:15:00.209] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 12:15:00.211] [Info] 已加载 7 个平台下载器
[2025-08-04 12:15:00.211] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 12:15:00.214] [Info] 程序初始化完成
[2025-08-04 12:15:15.441] [Info] 检测到平台: 小红书 🌹
[2025-08-04 12:15:15.442] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 12:15:15.443] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 12:15:15.444] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 12:15:15.444] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 12:15:16.977] [Info] 开始解析HTML页面数据
[2025-08-04 12:15:16.993] [Info] 提取的JSON数据长度: 8015
[2025-08-04 12:15:16.994] [Info] 清理后的JSON数据长度: 8014
[2025-08-04 12:15:17.000] [Error] JSON解析失败: ':' is invalid after a single JSON value. Expected end of data. LineNumber: 0 | BytePositionInLine: 8.
[2025-08-04 12:15:17.000] [Error] 错误位置: Line 0, Position 8
[2025-08-04 12:15:17.000] [Error] 错误位置附近内容: ..."global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259...
[2025-08-04 12:15:17.000] [Error] 提取作品详情失败
[2025-08-04 12:15:17.000] [Error] 获取作品详细信息失败
[2025-08-04 12:23:15.748] [Info] 已加载 0 个账号
[2025-08-04 12:23:15.810] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 12:23:15.812] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 12:23:15.861] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 12:23:15.861] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 12:23:15.862] [Info] 已加载 7 个平台下载器
[2025-08-04 12:23:15.863] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 12:23:15.866] [Info] 程序初始化完成
[2025-08-04 12:23:32.687] [Info] 检测到平台: 小红书 🌹
[2025-08-04 12:23:32.688] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 12:23:32.689] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 12:23:32.689] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 12:23:32.690] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 12:23:34.206] [Info] 开始解析HTML页面数据
[2025-08-04 12:23:34.225] [Info] 提取的JSON数据长度: 8015
[2025-08-04 12:23:34.225] [Info] JSON数据开头50字符: {"global":{"appSettings":{"notificationInterval":3
[2025-08-04 12:23:34.226] [Info] 移除了JSON开头的 9 个字符
[2025-08-04 12:23:34.226] [Info] 清理后的JSON数据长度: 8005
[2025-08-04 12:23:34.228] [Error] JSON解析失败: 'u' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 2903.
[2025-08-04 12:23:34.228] [Error] 错误位置: Line 0, Position 2903
[2025-08-04 12:23:34.228] [Error] 错误位置附近内容: ...":[],"userPageData":{},"activeTab":{"key":0,"index":0,"query":"note","label":"笔记","lock":false,"subT...
[2025-08-04 12:23:34.228] [Error] 提取作品详情失败
[2025-08-04 12:23:34.229] [Error] 获取作品详细信息失败
[2025-08-04 14:55:02.103] [Info] 已加载 0 个账号
[2025-08-04 14:55:02.161] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 14:55:02.163] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 14:55:02.208] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 14:55:02.208] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 14:55:02.209] [Info] 已加载 7 个平台下载器
[2025-08-04 14:55:02.209] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 14:55:02.212] [Info] 程序初始化完成
[2025-08-04 14:55:10.451] [Info] 检测到平台: 小红书 🌹
[2025-08-04 14:55:10.452] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 14:55:10.453] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 14:55:10.454] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 14:55:10.454] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 14:55:12.108] [Info] 开始解析HTML页面数据
[2025-08-04 14:55:12.124] [Info] 提取的JSON数据长度: 8015
[2025-08-04 14:55:12.125] [Info] JSON数据开头50字符: {"global":{"appSettings":{"notificationInterval":3
[2025-08-04 14:55:12.126] [Info] 移除了JSON开头的 9 个字符
[2025-08-04 14:55:12.126] [Info] 清理后的JSON数据长度: 8005
[2025-08-04 14:55:12.129] [Info] 已修复JSON数据中的常见问题
[2025-08-04 14:55:12.129] [Error] JSON解析失败: ',' is invalid after a single JSON value. Expected end of data. LineNumber: 0 | BytePositionInLine: 3240.
[2025-08-04 14:55:12.130] [Error] 错误位置: Line 0, Position 3240
[2025-08-04 14:55:12.130] [Error] 错误位置附近内容: ...ason":"","api":""},"firstFetchNote":true,"noteQueries":[{"num":30,"cursor":"","userId":"","hasMore":...
[2025-08-04 14:55:12.130] [Error] 提取作品详情失败
[2025-08-04 14:55:12.130] [Error] 获取作品详细信息失败
[2025-08-04 15:02:07.234] [Info] 已加载 0 个账号
[2025-08-04 15:02:07.296] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 15:02:07.297] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 15:02:07.348] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 15:02:07.348] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 15:02:07.350] [Info] 已加载 7 个平台下载器
[2025-08-04 15:02:07.350] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 15:02:07.353] [Info] 程序初始化完成
[2025-08-04 15:08:37.576] [Info] 已加载 0 个账号
[2025-08-04 15:08:37.635] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 15:08:37.636] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 15:08:37.679] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 15:08:37.679] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 15:08:37.680] [Info] 已加载 7 个平台下载器
[2025-08-04 15:08:37.681] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 15:08:37.683] [Info] 程序初始化完成
[2025-08-04 15:08:45.392] [Info] 检测到平台: 小红书 🌹
[2025-08-04 15:08:45.393] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 15:08:45.394] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 15:08:45.395] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 15:08:45.395] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 15:08:46.050] [Info] 获取到HTML内容，长度: 275573
[2025-08-04 15:08:46.050] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 15:08:47.055] [Info] 开始解析HTML页面数据
[2025-08-04 15:08:47.072] [Info] 找到目标脚本，长度: 8040
[2025-08-04 15:08:47.072] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 15:08:47.072] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:08:47.072] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 15:08:47.073] [Info] 使用正则表达式直接提取note数据
[2025-08-04 15:08:47.073] [Info] 方法1失败，尝试方法2
[2025-08-04 15:08:47.073] [Info] 方法2失败，尝试方法3
[2025-08-04 15:08:47.073] [Error] 所有正则表达式方法都失败了
[2025-08-04 15:08:47.073] [Info] 创建备用note详情
[2025-08-04 15:08:47.074] [Info] 成功提取作品详情
[2025-08-04 15:08:47.074] [Info] 成功解析作品: 小红书_沪ICP备
[2025-08-04 15:08:47.078] [Info] 开始获取小红书作品详情: unknown
[2025-08-04 15:08:47.078] [Warning] 请求失败 (重试 1/3): This instance has already started one or more requests. Properties can only be modified before sending the first request.
[2025-08-04 15:08:49.078] [Warning] 请求失败 (重试 2/3): This instance has already started one or more requests. Properties can only be modified before sending the first request.
[2025-08-04 15:08:53.082] [Warning] 请求失败 (重试 3/3): This instance has already started one or more requests. Properties can only be modified before sending the first request.
[2025-08-04 15:08:53.083] [Error] 获取作品详情失败: This instance has already started one or more requests. Properties can only be modified before sending the first request.
[2025-08-04 15:08:58.349] [Info] 开始获取小红书作品详情: unknown
[2025-08-04 15:08:58.349] [Warning] 请求失败 (重试 1/3): This instance has already started one or more requests. Properties can only be modified before sending the first request.
[2025-08-04 15:09:00.358] [Warning] 请求失败 (重试 2/3): This instance has already started one or more requests. Properties can only be modified before sending the first request.
[2025-08-04 15:09:04.371] [Warning] 请求失败 (重试 3/3): This instance has already started one or more requests. Properties can only be modified before sending the first request.
[2025-08-04 15:09:04.372] [Error] 获取作品详情失败: This instance has already started one or more requests. Properties can only be modified before sending the first request.
[2025-08-04 15:09:04.372] [Error] 获取作品详细信息失败
[2025-08-04 15:12:49.455] [Info] 已加载 0 个账号
[2025-08-04 15:12:49.512] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 15:12:49.513] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 15:12:49.557] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 15:12:49.557] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 15:12:49.559] [Info] 已加载 7 个平台下载器
[2025-08-04 15:12:49.559] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 15:12:49.562] [Info] 程序初始化完成
[2025-08-04 15:12:57.037] [Info] 检测到平台: 小红书 🌹
[2025-08-04 15:12:57.038] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 15:12:57.039] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 15:12:57.040] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 15:12:57.040] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 15:12:57.572] [Info] 获取到HTML内容，长度: 275572
[2025-08-04 15:12:57.572] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 15:12:58.579] [Info] 开始解析HTML页面数据
[2025-08-04 15:12:58.595] [Info] 找到目标脚本，长度: 8040
[2025-08-04 15:12:58.595] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 15:12:58.595] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:12:58.595] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 15:12:58.596] [Info] 使用正则表达式直接提取note数据
[2025-08-04 15:12:58.596] [Info] JSON数据长度: 8015
[2025-08-04 15:12:58.596] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:12:58.605] [Warning] 完整JSON解析失败: 'u' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 2913.
[2025-08-04 15:12:58.605] [Info] 使用正则表达式备用方法
[2025-08-04 15:12:58.606] [Info] 正则方法1失败，尝试方法2
[2025-08-04 15:12:58.606] [Info] 正则方法2失败，尝试方法3
[2025-08-04 15:12:58.606] [Error] 所有正则表达式方法都失败了
[2025-08-04 15:12:58.606] [Info] 创建备用note详情
[2025-08-04 15:12:58.606] [Info] 成功提取作品详情
[2025-08-04 15:12:58.607] [Info] 成功解析作品: 小红书_沪ICP备
[2025-08-04 15:12:58.610] [Info] 开始获取小红书作品详情: unknown
[2025-08-04 15:12:59.238] [Info] 获取到HTML内容，长度: 276122
[2025-08-04 15:12:59.238] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 15:13:00.250] [Info] 开始解析HTML页面数据
[2025-08-04 15:13:00.252] [Info] 找到目标脚本，长度: 8040
[2025-08-04 15:13:00.252] [Info] 使用正则表达式直接提取note数据
[2025-08-04 15:13:00.252] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:13:00.253] [Info] 正则方法1失败，尝试方法2
[2025-08-04 15:13:00.253] [Info] 正则方法2失败，尝试方法3
[2025-08-04 15:13:00.252] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:13:00.252] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 15:13:00.252] [Info] JSON数据长度: 8015
[2025-08-04 15:13:00.252] [Warning] 完整JSON解析失败: 'u' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 2913.
[2025-08-04 15:13:00.252] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 15:13:00.252] [Info] 使用正则表达式备用方法
[2025-08-04 15:13:00.253] [Error] 所有正则表达式方法都失败了
[2025-08-04 15:13:00.253] [Info] 创建备用note详情
[2025-08-04 15:13:00.253] [Info] 成功提取作品详情
[2025-08-04 15:13:03.585] [Info] 开始获取小红书作品详情: unknown
[2025-08-04 15:13:04.254] [Info] 获取到HTML内容，长度: 276122
[2025-08-04 15:13:04.254] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 15:13:05.267] [Info] 开始解析HTML页面数据
[2025-08-04 15:13:05.269] [Info] 找到目标脚本，长度: 8040
[2025-08-04 15:13:05.269] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 15:13:05.269] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 15:13:05.270] [Info] 获取到 0 个下载链接
[2025-08-04 15:13:05.269] [Info] 使用正则表达式直接提取note数据
[2025-08-04 15:13:05.269] [Info] JSON数据长度: 8015
[2025-08-04 15:13:05.269] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:13:05.270] [Warning] 完整JSON解析失败: 'u' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 2913.
[2025-08-04 15:13:05.270] [Info] 使用正则表达式备用方法
[2025-08-04 15:13:05.270] [Info] 正则方法1失败，尝试方法2
[2025-08-04 15:13:05.270] [Info] 正则方法2失败，尝试方法3
[2025-08-04 15:13:05.270] [Info] 创建备用note详情
[2025-08-04 15:13:05.270] [Error] 所有正则表达式方法都失败了
[2025-08-04 15:13:05.269] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:13:05.270] [Info] 成功提取作品详情
[2025-08-04 15:16:15.767] [Info] 已加载 0 个账号
[2025-08-04 15:16:15.824] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 15:16:15.825] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 15:16:15.871] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 15:16:15.871] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 15:16:15.873] [Info] 已加载 7 个平台下载器
[2025-08-04 15:16:15.873] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 15:16:15.876] [Info] 程序初始化完成
[2025-08-04 15:16:21.780] [Info] 检测到平台: 小红书 🌹
[2025-08-04 15:16:21.780] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 15:16:21.782] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 15:16:21.783] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 15:16:21.783] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 15:16:22.271] [Info] 获取到HTML内容，长度: 275572
[2025-08-04 15:16:22.271] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 15:16:23.276] [Info] 开始解析HTML页面数据
[2025-08-04 15:16:23.293] [Info] 找到目标脚本，长度: 8040
[2025-08-04 15:16:23.293] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 15:16:23.293] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 15:16:23.293] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:16:23.294] [Info] 使用正则表达式直接提取note数据
[2025-08-04 15:16:23.294] [Info] JSON数据长度: 8015
[2025-08-04 15:16:23.294] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:16:23.294] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 15:16:23.296] [Info] 修复后JSON长度: 7895
[2025-08-04 15:16:23.296] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 15:16:23.296] [Info] 修复后JSON长度: 7895
[2025-08-04 15:16:23.304] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 15:16:23.304] [Info] 找到note.noteDetailMap路径
[2025-08-04 15:16:23.304] [Info] 找到note数据: null
[2025-08-04 15:16:23.305] [Info] 成功提取作品详情
[2025-08-04 15:16:23.305] [Info] 成功解析作品: 
[2025-08-04 15:16:23.309] [Info] 开始获取小红书作品详情: 
[2025-08-04 15:16:23.762] [Info] 获取到HTML内容，长度: 348480
[2025-08-04 15:16:23.763] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 15:16:24.772] [Info] 开始解析HTML页面数据
[2025-08-04 15:16:24.797] [Info] 找到目标脚本，长度: 48779
[2025-08-04 15:16:24.797] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 15:16:24.797] [Info] JavaScript对象字符串长度: 48754
[2025-08-04 15:16:24.797] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:16:24.797] [Info] JSON数据长度: 48754
[2025-08-04 15:16:24.797] [Info] 使用正则表达式直接提取note数据
[2025-08-04 15:16:24.797] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 15:16:24.799] [Info] 修复后JSON长度: 48644
[2025-08-04 15:16:24.798] [Info] 修复后JSON长度: 48644
[2025-08-04 15:16:24.798] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 15:16:24.797] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:16:24.800] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 15:16:24.800] [Info] 找到note.noteDetailMap路径
[2025-08-04 15:16:24.800] [Info] 找到note数据: null
[2025-08-04 15:16:24.800] [Info] 成功提取作品详情
[2025-08-04 15:16:27.560] [Info] 开始获取小红书作品详情: 
[2025-08-04 15:16:27.958] [Info] 获取到HTML内容，长度: 340527
[2025-08-04 15:16:27.958] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 15:16:28.960] [Info] 开始解析HTML页面数据
[2025-08-04 15:16:28.963] [Info] 找到目标脚本，长度: 45938
[2025-08-04 15:16:28.964] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 15:16:28.964] [Info] JavaScript对象字符串长度: 45913
[2025-08-04 15:16:28.964] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:16:28.964] [Info] 使用正则表达式直接提取note数据
[2025-08-04 15:16:28.964] [Info] JSON数据长度: 45913
[2025-08-04 15:16:28.963] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 15:16:28.965] [Info] 找到note.noteDetailMap路径
[2025-08-04 15:16:28.964] [Info] 修复后JSON长度: 45803
[2025-08-04 15:16:28.964] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 15:16:28.964] [Info] 修复后JSON长度: 45803
[2025-08-04 15:16:28.965] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 15:16:28.964] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:16:28.965] [Info] 找到note数据: null
[2025-08-04 15:16:28.965] [Info] 成功提取作品详情
[2025-08-04 15:16:28.965] [Info] 获取到 0 个下载链接
[2025-08-04 15:18:47.948] [Info] 已加载 0 个账号
[2025-08-04 15:18:48.005] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 15:18:48.006] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 15:18:48.053] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 15:18:48.054] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 15:18:48.055] [Info] 已加载 7 个平台下载器
[2025-08-04 15:18:48.055] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 15:18:48.058] [Info] 程序初始化完成
[2025-08-04 15:18:53.849] [Info] 检测到平台: 小红书 🌹
[2025-08-04 15:18:53.849] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 15:18:53.851] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 15:18:53.851] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 15:18:53.852] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 15:18:54.485] [Info] 获取到HTML内容，长度: 275572
[2025-08-04 15:18:54.485] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 15:18:55.493] [Info] 开始解析HTML页面数据
[2025-08-04 15:18:55.510] [Info] 找到目标脚本，长度: 8040
[2025-08-04 15:18:55.510] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:18:55.510] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 15:18:55.510] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 15:18:55.511] [Info] 使用正则表达式直接提取note数据
[2025-08-04 15:18:55.512] [Info] JSON数据长度: 8015
[2025-08-04 15:18:55.512] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:18:55.512] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 15:18:55.513] [Info] 修复后JSON长度: 7895
[2025-08-04 15:18:55.513] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 15:18:55.513] [Info] 修复后JSON长度: 7895
[2025-08-04 15:18:55.522] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 15:18:55.522] [Info] 找到note元素
[2025-08-04 15:18:55.522] [Info] 找到note.noteDetailMap路径
[2025-08-04 15:18:55.522] [Info] noteDetailMap类型: Object
[2025-08-04 15:18:55.523] [Info] noteDetailMap属性 1: null, 值类型: Object
[2025-08-04 15:18:55.523] [Info] 找到note数据: null
[2025-08-04 15:18:55.523] [Info] 成功提取作品详情
[2025-08-04 15:18:55.524] [Info] 成功解析作品: 
[2025-08-04 15:18:55.528] [Info] 开始获取小红书作品详情: 
[2025-08-04 15:18:55.894] [Info] 获取到HTML内容，长度: 342132
[2025-08-04 15:18:55.894] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 15:18:56.908] [Info] 开始解析HTML页面数据
[2025-08-04 15:18:56.912] [Info] 找到目标脚本，长度: 46168
[2025-08-04 15:18:56.923] [Info] JavaScript对象字符串长度: 46143
[2025-08-04 15:18:56.923] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:18:56.923] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:18:56.923] [Info] 使用正则表达式直接提取note数据
[2025-08-04 15:18:56.912] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 15:18:56.923] [Info] JSON数据长度: 46143
[2025-08-04 15:18:56.923] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 15:18:56.924] [Info] 修复后JSON长度: 46033
[2025-08-04 15:18:56.924] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 15:18:56.924] [Info] 修复后JSON长度: 46033
[2025-08-04 15:18:56.925] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 15:18:56.925] [Info] 找到note数据: null
[2025-08-04 15:18:56.925] [Info] 找到note元素
[2025-08-04 15:18:56.925] [Info] 找到note.noteDetailMap路径
[2025-08-04 15:18:56.925] [Info] noteDetailMap类型: Object
[2025-08-04 15:18:56.925] [Info] noteDetailMap属性 1: null, 值类型: Object
[2025-08-04 15:18:56.925] [Info] 成功提取作品详情
[2025-08-04 15:18:59.083] [Info] 开始获取小红书作品详情: 
[2025-08-04 15:18:59.645] [Info] 获取到HTML内容，长度: 340256
[2025-08-04 15:18:59.645] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 15:19:00.653] [Info] 开始解析HTML页面数据
[2025-08-04 15:19:00.656] [Info] 找到目标脚本，长度: 45848
[2025-08-04 15:19:00.656] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 15:19:00.656] [Info] JavaScript对象字符串长度: 45823
[2025-08-04 15:19:00.656] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:19:00.656] [Info] 使用正则表达式直接提取note数据
[2025-08-04 15:19:00.656] [Info] JSON数据长度: 45823
[2025-08-04 15:19:00.656] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 15:19:00.656] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:19:00.657] [Info] 修复后JSON长度: 45713
[2025-08-04 15:19:00.658] [Info] 找到note.noteDetailMap路径
[2025-08-04 15:19:00.657] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 15:19:00.657] [Info] 修复后JSON长度: 45713
[2025-08-04 15:19:00.658] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 15:19:00.658] [Info] 找到note元素
[2025-08-04 15:19:00.658] [Info] noteDetailMap类型: Object
[2025-08-04 15:19:00.658] [Info] noteDetailMap属性 1: null, 值类型: Object
[2025-08-04 15:19:00.658] [Info] 找到note数据: null
[2025-08-04 15:19:00.658] [Info] 成功提取作品详情
[2025-08-04 15:19:00.658] [Info] 获取到 0 个下载链接
[2025-08-04 15:20:22.044] [Info] 已加载 0 个账号
[2025-08-04 15:20:22.106] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 15:20:22.107] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 15:20:22.158] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 15:20:22.158] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 15:20:22.159] [Info] 已加载 7 个平台下载器
[2025-08-04 15:20:22.159] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 15:20:22.162] [Info] 程序初始化完成
[2025-08-04 15:20:39.345] [Info] 检测到平台: 小红书 🌹
[2025-08-04 15:20:39.345] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 15:20:39.347] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 15:20:39.347] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 15:20:39.347] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 15:20:39.900] [Info] 获取到HTML内容，长度: 275572
[2025-08-04 15:20:39.900] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 15:20:40.911] [Info] 开始解析HTML页面数据
[2025-08-04 15:20:40.927] [Info] 找到目标脚本，长度: 8040
[2025-08-04 15:20:40.928] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 15:20:40.928] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:20:40.928] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 15:20:40.929] [Info] 使用正则表达式直接提取note数据
[2025-08-04 15:20:40.929] [Info] JSON数据长度: 8015
[2025-08-04 15:20:40.929] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:20:40.929] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 15:20:40.931] [Info] 修复后JSON长度: 7895
[2025-08-04 15:20:40.931] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 15:20:40.931] [Info] 修复后JSON长度: 7895
[2025-08-04 15:20:40.939] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 15:20:40.939] [Info] 找到note元素
[2025-08-04 15:20:40.939] [Info] 找到note.noteDetailMap路径
[2025-08-04 15:20:40.940] [Info] noteDetailMap类型: Object
[2025-08-04 15:20:40.940] [Info] noteDetailMap属性 1: 'null', 值类型: Object
[2025-08-04 15:20:40.940] [Info] 在属性 'null' 中找到note数据
[2025-08-04 15:20:40.941] [Info] 成功提取作品详情
[2025-08-04 15:20:40.942] [Info] 成功解析作品: 
[2025-08-04 15:20:40.945] [Info] 开始获取小红书作品详情: 
[2025-08-04 15:20:41.453] [Info] 获取到HTML内容，长度: 326537
[2025-08-04 15:20:41.453] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 15:20:42.457] [Info] 开始解析HTML页面数据
[2025-08-04 15:20:42.461] [Info] 找到目标脚本，长度: 40367
[2025-08-04 15:20:42.461] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 15:20:42.461] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 15:20:42.461] [Info] 使用正则表达式直接提取note数据
[2025-08-04 15:20:42.461] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:20:42.461] [Info] JSON数据长度: 40342
[2025-08-04 15:20:42.461] [Info] JavaScript对象字符串长度: 40342
[2025-08-04 15:20:42.461] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 15:20:42.462] [Info] 修复后JSON长度: 40232
[2025-08-04 15:20:42.462] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 15:20:42.462] [Info] 修复后JSON长度: 40232
[2025-08-04 15:20:42.463] [Info] 找到note.noteDetailMap路径
[2025-08-04 15:20:42.463] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 15:20:42.463] [Info] 找到note元素
[2025-08-04 15:20:42.463] [Info] noteDetailMap类型: Object
[2025-08-04 15:20:42.463] [Info] noteDetailMap属性 1: 'null', 值类型: Object
[2025-08-04 15:20:42.463] [Info] 在属性 'null' 中找到note数据
[2025-08-04 15:20:42.463] [Info] 成功提取作品详情
[2025-08-04 15:20:43.231] [Info] 开始获取小红书作品详情: 
[2025-08-04 15:20:43.618] [Info] 获取到HTML内容，长度: 329517
[2025-08-04 15:20:43.618] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
