[2025-08-04 16:14:55.912] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 16:14:55.916] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 16:14:55.964] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 16:14:55.964] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 16:14:55.965] [Info] 已加载 7 个平台下载器
[2025-08-04 16:14:55.965] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 16:14:55.968] [Info] 程序初始化完成
[2025-08-04 16:16:55.927] [Info] 检测到平台: 小红书 🌹
[2025-08-04 16:16:55.927] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 16:16:55.929] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 16:16:55.929] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 16:16:55.930] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 16:16:56.545] [Info] 获取到HTML内容，长度: 275573
[2025-08-04 16:16:56.545] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 16:16:57.555] [Info] 开始解析HTML页面数据
[2025-08-04 16:16:57.574] [Info] 找到目标脚本，长度: 8040
[2025-08-04 16:16:57.574] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 16:16:57.574] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 16:16:57.574] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:16:57.575] [Info] 使用正则表达式直接提取note数据
[2025-08-04 16:16:57.575] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:16:57.575] [Info] JSON数据长度: 8015
[2025-08-04 16:16:57.576] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 16:16:57.577] [Info] 修复后JSON长度: 7895
[2025-08-04 16:16:57.577] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 16:16:57.577] [Info] 修复后JSON长度: 7895
[2025-08-04 16:16:57.585] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 16:16:57.585] [Info] 找到note元素
[2025-08-04 16:16:57.586] [Info] 找到note.noteDetailMap路径
[2025-08-04 16:16:57.586] [Info] noteDetailMap类型: Object
[2025-08-04 16:16:57.586] [Info] noteDetailMap属性 1: 'null', 值类型: Object
[2025-08-04 16:16:57.586] [Info] 在属性 'null' 中找到note数据
[2025-08-04 16:16:57.586] [Info] Note数据内容: {}
[2025-08-04 16:16:57.587] [Info] Note数据包含的属性:
[2025-08-04 16:16:57.587] [Info] 处理媒体内容，类型: 
[2025-08-04 16:16:57.587] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 16:16:57.588] [Info] 开始提取视频URL
[2025-08-04 16:16:57.588] [Warning] 未找到video属性
[2025-08-04 16:16:57.588] [Info] 提取到 0 个图片URL
[2025-08-04 16:16:57.588] [Info] 成功提取作品详情
[2025-08-04 16:16:57.589] [Info] 成功解析作品: 
[2025-08-04 16:16:57.592] [Info] 开始获取小红书作品详情: 
[2025-08-04 16:16:58.002] [Info] 获取到HTML内容，长度: 339871
[2025-08-04 16:16:58.002] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 16:16:59.007] [Info] 开始解析HTML页面数据
[2025-08-04 16:16:59.018] [Info] 找到目标脚本，长度: 45684
[2025-08-04 16:16:59.018] [Info] JSON数据长度: 45659
[2025-08-04 16:16:59.018] [Info] JavaScript对象字符串长度: 45659
[2025-08-04 16:16:59.018] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:16:59.018] [Info] 使用正则表达式直接提取note数据
[2025-08-04 16:16:59.018] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 16:16:59.018] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:16:59.018] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 16:16:59.019] [Info] 修复后JSON长度: 45549
[2025-08-04 16:16:59.019] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 16:16:59.019] [Info] 修复后JSON长度: 45549
[2025-08-04 16:16:59.020] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 16:16:59.020] [Info] 找到note元素
[2025-08-04 16:16:59.020] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 16:16:59.020] [Warning] 未找到video属性
[2025-08-04 16:16:59.020] [Info] 开始提取视频URL
[2025-08-04 16:16:59.020] [Info] noteDetailMap属性 1: 'null', 值类型: Object
[2025-08-04 16:16:59.020] [Info] 在属性 'null' 中找到note数据
[2025-08-04 16:16:59.020] [Info] Note数据内容: {}
[2025-08-04 16:16:59.020] [Info] Note数据包含的属性:
[2025-08-04 16:16:59.020] [Info] 找到note.noteDetailMap路径
[2025-08-04 16:16:59.020] [Info] 处理媒体内容，类型: 
[2025-08-04 16:16:59.020] [Info] noteDetailMap类型: Object
[2025-08-04 16:16:59.020] [Info] 提取到 0 个图片URL
[2025-08-04 16:16:59.020] [Info] 成功提取作品详情
[2025-08-04 16:17:00.443] [Info] 开始获取小红书作品详情: 
[2025-08-04 16:17:00.810] [Info] 获取到HTML内容，长度: 340258
[2025-08-04 16:17:00.810] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 16:17:01.820] [Info] 开始解析HTML页面数据
[2025-08-04 16:17:01.824] [Info] 找到目标脚本，长度: 45846
[2025-08-04 16:17:01.824] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 16:17:01.824] [Info] JavaScript对象字符串长度: 45821
[2025-08-04 16:17:01.824] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:17:01.824] [Info] 使用正则表达式直接提取note数据
[2025-08-04 16:17:01.824] [Info] JSON数据长度: 45821
[2025-08-04 16:17:01.824] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:17:01.824] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 16:17:01.824] [Info] 修复后JSON长度: 45711
[2025-08-04 16:17:01.825] [Info] noteDetailMap属性 1: 'null', 值类型: Object
[2025-08-04 16:17:01.825] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 16:17:01.825] [Warning] 未找到video属性
[2025-08-04 16:17:01.825] [Info] 成功提取作品详情
[2025-08-04 16:17:01.825] [Info] 找到note元素
[2025-08-04 16:17:01.825] [Info] 找到note.noteDetailMap路径
[2025-08-04 16:17:01.825] [Info] noteDetailMap类型: Object
[2025-08-04 16:17:01.825] [Info] 在属性 'null' 中找到note数据
[2025-08-04 16:17:01.825] [Info] Note数据内容: {}
[2025-08-04 16:17:01.825] [Info] Note数据包含的属性:
[2025-08-04 16:17:01.824] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 16:17:01.825] [Info] 处理媒体内容，类型: 
[2025-08-04 16:17:01.824] [Info] 修复后JSON长度: 45711
[2025-08-04 16:17:01.825] [Info] 开始提取视频URL
[2025-08-04 16:17:01.825] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 16:17:01.825] [Info] 提取到 0 个图片URL
[2025-08-04 16:17:01.825] [Info] 获取到 0 个下载链接
