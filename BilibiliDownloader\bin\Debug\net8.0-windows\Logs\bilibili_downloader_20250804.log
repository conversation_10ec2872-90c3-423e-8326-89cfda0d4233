[2025-08-04 19:49:53.437] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 19:49:53.440] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 19:49:53.488] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 19:49:53.488] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 19:49:53.489] [Info] 已加载 7 个平台下载器
[2025-08-04 19:49:53.489] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 19:49:53.492] [Info] 程序初始化完成
[2025-08-04 22:53:29.665] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 22:53:29.669] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 22:53:29.719] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 22:53:29.719] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 22:53:29.720] [Info] 已加载 7 个平台下载器
[2025-08-04 22:53:29.720] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 22:53:29.723] [Info] 程序初始化完成
[2025-08-04 22:54:23.433] [Info] 检测到平台: 哔哩哔哩 📺
[2025-08-04 22:54:23.434] [Info] 解析B站视频: https://www.bilibili.com/video/BV1b9hKzbEju/?spm_id_from=333.1007.tianma.1-3-3.click
[2025-08-04 22:54:23.437] [Info] 开始解析视频链接: https://www.bilibili.com/video/BV1b9hKzbEju/?spm_id_from=333.1007.tianma.1-3-3.click
[2025-08-04 22:54:23.438] [Info] 开始解析URL: https://www.bilibili.com/video/BV1b9hKzbEju/?spm_id_from=333.1007.tianma.1-3-3.click
[2025-08-04 22:54:23.440] [Info] 提取到BV号: BV1b9hKzbEju
[2025-08-04 22:54:23.440] [Info] 提取到视频ID: BV1b9hKzbEju, 页面: 1
[2025-08-04 22:54:23.719] [Info] 获取视频基本信息成功: 😚 🫧🐐
[2025-08-04 22:54:23.788] [Info] 选择页面 1，CID: 25835279272
[2025-08-04 22:54:23.789] [Info] 请求播放信息URL: https://api.bilibili.com/x/player/playurl?bvid=BV1b9hKzbEju&cid=25835279272&qn=120&fnval=4048&fourk=1&try_look=1
[2025-08-04 22:54:23.931] [Info] 播放信息API响应: code=0, message=0
[2025-08-04 22:54:23.931] [Info] 获取视频播放信息成功
[2025-08-04 22:54:23.933] [Info] 开始合并视频播放信息，API响应码: 0
[2025-08-04 22:54:23.933] [Info] 播放信息数据获取成功，开始解析清晰度
[2025-08-04 22:54:23.934] [Info] 检测到DASH格式，开始解析DASH清晰度
[2025-08-04 22:54:23.936] [Info] 开始解析DASH清晰度信息
[2025-08-04 22:54:23.937] [Info] 找到 12 个视频流
[2025-08-04 22:54:23.939] [Info] 解析视频流: ID=80, 名称=1080P
[2025-08-04 22:54:23.944] [Info] 为清晰度 1080P 找到音频流
[2025-08-04 22:54:23.945] [Info] 成功添加清晰度选项: 1080P
[2025-08-04 22:54:23.945] [Info] 为清晰度 1080P 找到音频流
[2025-08-04 22:54:23.945] [Info] 成功添加清晰度选项: 1080P
[2025-08-04 22:54:23.945] [Info] 解析视频流: ID=80, 名称=1080P
[2025-08-04 22:54:23.945] [Info] 解析视频流: ID=80, 名称=1080P
[2025-08-04 22:54:23.945] [Info] 成功添加清晰度选项: 720P
[2025-08-04 22:54:23.945] [Info] 解析视频流: ID=64, 名称=720P
[2025-08-04 22:54:23.945] [Info] 成功添加清晰度选项: 1080P
[2025-08-04 22:54:23.945] [Info] 为清晰度 720P 找到音频流
[2025-08-04 22:54:23.946] [Info] 成功添加清晰度选项: 480P
[2025-08-04 22:54:23.945] [Info] 成功添加清晰度选项: 720P
[2025-08-04 22:54:23.946] [Info] 为清晰度 360P 找到音频流
[2025-08-04 22:54:23.946] [Info] 成功添加清晰度选项: 360P
[2025-08-04 22:54:23.946] [Info] 解析视频流: ID=16, 名称=360P
[2025-08-04 22:54:23.946] [Info] 为清晰度 360P 找到音频流
[2025-08-04 22:54:23.946] [Info] 成功添加清晰度选项: 360P
[2025-08-04 22:54:23.946] [Info] 解析视频流: ID=16, 名称=360P
[2025-08-04 22:54:23.946] [Info] 为清晰度 360P 找到音频流
[2025-08-04 22:54:23.945] [Info] 为清晰度 480P 找到音频流
[2025-08-04 22:54:23.945] [Info] 解析视频流: ID=32, 名称=480P
[2025-08-04 22:54:23.946] [Info] 为清晰度 480P 找到音频流
[2025-08-04 22:54:23.946] [Info] 成功添加清晰度选项: 480P
[2025-08-04 22:54:23.946] [Info] 解析视频流: ID=32, 名称=480P
[2025-08-04 22:54:23.946] [Info] 为清晰度 480P 找到音频流
[2025-08-04 22:54:23.945] [Info] 解析视频流: ID=64, 名称=720P
[2025-08-04 22:54:23.946] [Info] 解析视频流: ID=16, 名称=360P
[2025-08-04 22:54:23.945] [Info] 为清晰度 720P 找到音频流
[2025-08-04 22:54:23.945] [Info] 解析视频流: ID=64, 名称=720P
[2025-08-04 22:54:23.945] [Info] 成功添加清晰度选项: 720P
[2025-08-04 22:54:23.945] [Info] 为清晰度 1080P 找到音频流
[2025-08-04 22:54:23.945] [Info] 为清晰度 720P 找到音频流
[2025-08-04 22:54:23.945] [Info] 解析视频流: ID=32, 名称=480P
[2025-08-04 22:54:23.945] [Info] 成功添加清晰度选项: 480P
[2025-08-04 22:54:23.946] [Info] 成功添加清晰度选项: 360P
[2025-08-04 22:54:23.946] [Info] 清晰度解析完成，共找到 12 个清晰度选项
[2025-08-04 22:54:23.946] [Info] 视频信息解析完成，可用清晰度: 12
[2025-08-04 22:54:24.014] [Info] 选择页面 1，CID: 25835279272
[2025-08-04 22:54:24.015] [Info] 请求播放信息URL: https://api.bilibili.com/x/player/playurl?bvid=BV1b9hKzbEju&cid=25835279272&qn=120&fnval=4048&fourk=1&try_look=1
[2025-08-04 22:54:24.115] [Info] 播放信息API响应: code=0, message=0
[2025-08-04 22:55:03.349] [Info] 检测到平台: 抖音 🎵
[2025-08-04 22:55:03.349] [Info] 解析抖音视频: https://www.douyin.com/video/7529010509569183034
[2025-08-04 22:56:06.691] [Warning] 未找到支持的平台: https://www.douyin.com/jingxuan/search/%E8%99%BE%E4%BB%81?aid=4b4a2d74-aae7-4e85-90b6-ebfc62269ec8&modal_id=7497094358984297737&type=general
[2025-08-04 22:57:56.577] [Warning] 未找到支持的平台: https://www.douyin.com/user/MS4wLjABAAAA34PGaVB6vpC_k_Jprah7_zNfiqstt1HnqAcEib58Bi1gI2FGSm0ZBMMoTIE5cIMW?from_tab_name=main&modal_id=7497094358984297737&vid=7497094358984297737
[2025-08-04 22:58:08.145] [Warning] 未找到支持的平台: https://www.douyin.com/user/vid=7497094358984297737
[2025-08-04 22:58:26.518] [Info] 检测到平台: 抖音 🎵
[2025-08-04 22:58:26.518] [Info] 解析抖音视频: 7.64 <EMAIL> Syg:/ 06/29 虾仁19小时 # 沙雕动画二次元 # 一口气看完系列 # 二次元   https://v.douyin.com/JKAdvxdxrx8/ 复制此链接，打开Dou音搜索，直接观看视频！
[2025-08-04 22:58:26.523] [Error] 抖音解析失败: 无法提取视频ID
[2025-08-04 22:58:35.936] [Info] 检测到平台: 抖音 🎵
[2025-08-04 22:58:35.936] [Info] 解析抖音视频: https://v.douyin.com/JKAdvxdxrx8/
[2025-08-04 22:58:36.830] [Error] 抖音解析失败: 无法提取视频ID
[2025-08-04 22:59:05.525] [Info] 检测到平台: 抖音 🎵
[2025-08-04 22:59:05.525] [Info] 解析抖音视频: https://www.douyin.com/video/7497094358984297737
[2025-08-04 22:59:11.815] [Info] 获取抖音下载链接: 7497094358984297737, 质量: 720p
[2025-08-04 22:59:12.071] [Error] 抖音API获取失败: The input does not contain any JSON tokens. Expected the input to start with a valid JSON token, when isFinalBlock is true. LineNumber: 0 | BytePositionInLine: 0.
[2025-08-04 22:59:12.071] [Warning] 无法获取抖音直接下载链接
[2025-08-04 22:59:12.072] [Error] 获取抖音下载链接失败: 抖音下载需要特殊处理。由于抖音的反爬虫机制，无法直接获取下载链接。
[2025-08-04 23:00:07.383] [Info] 检测到平台: 小红书 🌹
[2025-08-04 23:00:07.384] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688ff42a0000000004000ea7?xsec_token=ABThFRTuXqTQmEbN7l7mv4tDghM5gRdcaKHneUlG3ukPc=&xsec_source=pc_feed
[2025-08-04 23:00:07.384] [Info] 提取到笔记ID: 688ff42a0000000004000ea7
[2025-08-04 23:00:07.539] [Info] API响应: {"code":-1,"success":false}
[2025-08-04 23:00:07.540] [Error] 解析笔记响应失败: 响应数据格式错误
[2025-08-04 23:00:07.541] [Error] 获取笔记详情失败: 响应数据格式错误
[2025-08-04 23:00:07.541] [Error] 解析小红书视频信息失败: 响应数据格式错误
[2025-08-04 23:00:14.166] [Warning] 未找到支持的平台: https://www.xiaohongshu.com/explore
[2025-08-04 23:00:22.505] [Info] 检测到平台: 小红书 🌹
[2025-08-04 23:00:22.505] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/6890423c00000000020011e6?xsec_token=ABR4_JZR-WWtQrX8xFbRgaO7GgJ7Sed8Aa29TwEMGHlnI=&xsec_source=pc_feed
[2025-08-04 23:00:22.505] [Info] 提取到笔记ID: 6890423c00000000020011e6
[2025-08-04 23:00:22.557] [Info] API响应: {"code":-1,"success":false}
[2025-08-04 23:00:22.558] [Error] 解析笔记响应失败: 响应数据格式错误
[2025-08-04 23:00:22.558] [Error] 获取笔记详情失败: 响应数据格式错误
[2025-08-04 23:00:22.558] [Error] 解析小红书视频信息失败: 响应数据格式错误
[2025-08-04 23:01:40.857] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 23:01:40.861] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 23:01:40.908] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 23:01:40.908] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 23:01:40.909] [Info] 已加载 7 个平台下载器
[2025-08-04 23:01:40.910] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 23:01:40.913] [Info] 程序初始化完成
[2025-08-04 23:01:44.143] [Info] 检测到平台: 小红书 🌹
[2025-08-04 23:01:44.144] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/6890423c00000000020011e6?xsec_token=ABR4_JZR-WWtQrX8xFbRgaO7GgJ7Sed8Aa29TwEMGHlnI=&xsec_source=pc_feed
[2025-08-04 23:01:44.144] [Info] 提取到笔记ID: 6890423c00000000020011e6
[2025-08-04 23:01:44.360] [Info] API响应: {"code":-1,"success":false}
[2025-08-04 23:01:44.362] [Error] 解析笔记响应失败: 响应数据格式错误
[2025-08-04 23:01:44.362] [Error] 获取笔记详情失败: 响应数据格式错误
[2025-08-04 23:01:44.362] [Error] 解析小红书视频信息失败: 响应数据格式错误
[2025-08-04 23:08:08.287] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 23:08:08.291] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 23:08:08.337] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 23:08:08.337] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 23:08:08.338] [Info] 已加载 7 个平台下载器
[2025-08-04 23:08:08.338] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 23:08:08.341] [Info] 程序初始化完成
[2025-08-04 23:11:15.686] [Info] 检测到平台: 小红书 🌹
[2025-08-04 23:11:15.687] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/687774c800000000170319b6?xsec_token=ABGjbklRmnjgvHhAGtr5T53FiZr8VdCRd1GBC8mOJqWm4=&xsec_source=pc_cfeed
[2025-08-04 23:11:15.687] [Info] 提取到笔记ID: 687774c800000000170319b6
[2025-08-04 23:11:15.688] [Info] 访问网页版链接: https://www.xiaohongshu.com/explore/687774c800000000170319b6
[2025-08-04 23:11:16.141] [Info] 网页响应长度: 71385
[2025-08-04 23:11:16.142] [Error] 解析HTML内容失败: 无法从HTML中提取数据
[2025-08-04 23:12:00.229] [Info] 检测到平台: 小红书 🌹
[2025-08-04 23:12:00.229] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688428b300000000170329ef?xsec_token=ABq5AKzwce9osfDUoR_vThq4dMhwYkfiw_S-vJAWnUXII=&xsec_source=pc_cfeed
[2025-08-04 23:12:00.229] [Info] 访问网页版链接: https://www.xiaohongshu.com/explore/688428b300000000170329ef
[2025-08-04 23:12:00.229] [Info] 提取到笔记ID: 688428b300000000170329ef
[2025-08-04 23:12:00.549] [Info] 网页响应长度: 71414
[2025-08-04 23:12:00.549] [Error] 解析HTML内容失败: 无法从HTML中提取数据
[2025-08-04 23:17:07.233] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 23:17:07.236] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 23:17:07.287] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 23:17:07.287] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 23:17:07.288] [Info] 已加载 7 个平台下载器
[2025-08-04 23:17:07.288] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 23:17:07.291] [Info] 程序初始化完成
[2025-08-04 23:25:02.015] [Info] 检测到平台: 小红书 🌹
[2025-08-04 23:25:02.016] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/6874793c0000000024009daa?xsec_token=ABhXpYcFAQV84Y2eb8am0kpvKbExEy6eSWrojVj8mskIE=&xsec_source=pc_cfeed
[2025-08-04 23:25:02.017] [Info] 提取到笔记ID: 6874793c0000000024009daa
[2025-08-04 23:25:02.017] [Info] 访问网页版链接: https://www.xiaohongshu.com/explore/6874793c0000000024009daa
[2025-08-04 23:25:02.521] [Info] 网页响应长度: 71395
[2025-08-04 23:25:02.522] [Error] 解析HTML内容失败: 无法从HTML中提取数据
