[2025-08-04 16:14:55.912] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 16:14:55.916] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 16:14:55.964] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 16:14:55.964] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 16:14:55.965] [Info] 已加载 7 个平台下载器
[2025-08-04 16:14:55.965] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 16:14:55.968] [Info] 程序初始化完成
[2025-08-04 16:16:55.927] [Info] 检测到平台: 小红书 🌹
[2025-08-04 16:16:55.927] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 16:16:55.929] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 16:16:55.929] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 16:16:55.930] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 16:16:56.545] [Info] 获取到HTML内容，长度: 275573
[2025-08-04 16:16:56.545] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 16:16:57.555] [Info] 开始解析HTML页面数据
[2025-08-04 16:16:57.574] [Info] 找到目标脚本，长度: 8040
[2025-08-04 16:16:57.574] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 16:16:57.574] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 16:16:57.574] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:16:57.575] [Info] 使用正则表达式直接提取note数据
[2025-08-04 16:16:57.575] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:16:57.575] [Info] JSON数据长度: 8015
[2025-08-04 16:16:57.576] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 16:16:57.577] [Info] 修复后JSON长度: 7895
[2025-08-04 16:16:57.577] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 16:16:57.577] [Info] 修复后JSON长度: 7895
[2025-08-04 16:16:57.585] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 16:16:57.585] [Info] 找到note元素
[2025-08-04 16:16:57.586] [Info] 找到note.noteDetailMap路径
[2025-08-04 16:16:57.586] [Info] noteDetailMap类型: Object
[2025-08-04 16:16:57.586] [Info] noteDetailMap属性 1: 'null', 值类型: Object
[2025-08-04 16:16:57.586] [Info] 在属性 'null' 中找到note数据
[2025-08-04 16:16:57.586] [Info] Note数据内容: {}
[2025-08-04 16:16:57.587] [Info] Note数据包含的属性:
[2025-08-04 16:16:57.587] [Info] 处理媒体内容，类型: 
[2025-08-04 16:16:57.587] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 16:16:57.588] [Info] 开始提取视频URL
[2025-08-04 16:16:57.588] [Warning] 未找到video属性
[2025-08-04 16:16:57.588] [Info] 提取到 0 个图片URL
[2025-08-04 16:16:57.588] [Info] 成功提取作品详情
[2025-08-04 16:16:57.589] [Info] 成功解析作品: 
[2025-08-04 16:16:57.592] [Info] 开始获取小红书作品详情: 
[2025-08-04 16:16:58.002] [Info] 获取到HTML内容，长度: 339871
[2025-08-04 16:16:58.002] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 16:16:59.007] [Info] 开始解析HTML页面数据
[2025-08-04 16:16:59.018] [Info] 找到目标脚本，长度: 45684
[2025-08-04 16:16:59.018] [Info] JSON数据长度: 45659
[2025-08-04 16:16:59.018] [Info] JavaScript对象字符串长度: 45659
[2025-08-04 16:16:59.018] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:16:59.018] [Info] 使用正则表达式直接提取note数据
[2025-08-04 16:16:59.018] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 16:16:59.018] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:16:59.018] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 16:16:59.019] [Info] 修复后JSON长度: 45549
[2025-08-04 16:16:59.019] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 16:16:59.019] [Info] 修复后JSON长度: 45549
[2025-08-04 16:16:59.020] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 16:16:59.020] [Info] 找到note元素
[2025-08-04 16:16:59.020] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 16:16:59.020] [Warning] 未找到video属性
[2025-08-04 16:16:59.020] [Info] 开始提取视频URL
[2025-08-04 16:16:59.020] [Info] noteDetailMap属性 1: 'null', 值类型: Object
[2025-08-04 16:16:59.020] [Info] 在属性 'null' 中找到note数据
[2025-08-04 16:16:59.020] [Info] Note数据内容: {}
[2025-08-04 16:16:59.020] [Info] Note数据包含的属性:
[2025-08-04 16:16:59.020] [Info] 找到note.noteDetailMap路径
[2025-08-04 16:16:59.020] [Info] 处理媒体内容，类型: 
[2025-08-04 16:16:59.020] [Info] noteDetailMap类型: Object
[2025-08-04 16:16:59.020] [Info] 提取到 0 个图片URL
[2025-08-04 16:16:59.020] [Info] 成功提取作品详情
[2025-08-04 16:17:00.443] [Info] 开始获取小红书作品详情: 
[2025-08-04 16:17:00.810] [Info] 获取到HTML内容，长度: 340258
[2025-08-04 16:17:00.810] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 16:17:01.820] [Info] 开始解析HTML页面数据
[2025-08-04 16:17:01.824] [Info] 找到目标脚本，长度: 45846
[2025-08-04 16:17:01.824] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 16:17:01.824] [Info] JavaScript对象字符串长度: 45821
[2025-08-04 16:17:01.824] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:17:01.824] [Info] 使用正则表达式直接提取note数据
[2025-08-04 16:17:01.824] [Info] JSON数据长度: 45821
[2025-08-04 16:17:01.824] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 16:17:01.824] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 16:17:01.824] [Info] 修复后JSON长度: 45711
[2025-08-04 16:17:01.825] [Info] noteDetailMap属性 1: 'null', 值类型: Object
[2025-08-04 16:17:01.825] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 16:17:01.825] [Warning] 未找到video属性
[2025-08-04 16:17:01.825] [Info] 成功提取作品详情
[2025-08-04 16:17:01.825] [Info] 找到note元素
[2025-08-04 16:17:01.825] [Info] 找到note.noteDetailMap路径
[2025-08-04 16:17:01.825] [Info] noteDetailMap类型: Object
[2025-08-04 16:17:01.825] [Info] 在属性 'null' 中找到note数据
[2025-08-04 16:17:01.825] [Info] Note数据内容: {}
[2025-08-04 16:17:01.825] [Info] Note数据包含的属性:
[2025-08-04 16:17:01.824] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 16:17:01.825] [Info] 处理媒体内容，类型: 
[2025-08-04 16:17:01.824] [Info] 修复后JSON长度: 45711
[2025-08-04 16:17:01.825] [Info] 开始提取视频URL
[2025-08-04 16:17:01.825] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 16:17:01.825] [Info] 提取到 0 个图片URL
[2025-08-04 16:17:01.825] [Info] 获取到 0 个下载链接
[2025-08-04 16:30:22.194] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 16:30:22.198] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 16:30:22.249] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 16:30:22.250] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 16:30:22.251] [Info] 已加载 7 个平台下载器
[2025-08-04 16:30:22.252] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 16:30:22.254] [Info] 程序初始化完成
[2025-08-04 16:32:44.530] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 16:32:44.536] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 16:32:44.589] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 16:32:44.589] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 16:32:44.591] [Info] 已加载 7 个平台下载器
[2025-08-04 16:32:44.591] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 16:32:44.594] [Info] 程序初始化完成
[2025-08-04 19:05:42.017] [Info] 检测到平台: 小红书 🌹
[2025-08-04 19:05:42.018] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 19:05:42.019] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 19:05:42.020] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 19:05:42.020] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 19:05:42.647] [Info] 获取到HTML内容，长度: 275572
[2025-08-04 19:05:42.648] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:05:43.651] [Info] 开始解析HTML页面数据
[2025-08-04 19:05:43.666] [Info] 找到目标脚本，长度: 8040
[2025-08-04 19:05:43.666] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:05:43.666] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:05:43.666] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 19:05:43.667] [Info] 使用正则表达式直接提取note数据
[2025-08-04 19:05:43.668] [Info] JSON数据长度: 8015
[2025-08-04 19:05:43.668] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:05:43.668] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:05:43.668] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:05:43.669] [Info] 修复后JSON长度: 7895
[2025-08-04 19:05:43.669] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:05:43.669] [Info] 修复后JSON长度: 7895
[2025-08-04 19:05:43.669] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:05:43.669] [Info] 修复后JSON长度: 7895
[2025-08-04 19:05:43.678] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:05:43.678] [Info] 根JSON对象属性:
[2025-08-04 19:05:43.679] [Info]   - global: Object
[2025-08-04 19:05:43.679] [Info]   - search: Object
[2025-08-04 19:05:43.679] [Info]   - layout: Object
[2025-08-04 19:05:43.679] [Info]   - board: Object
[2025-08-04 19:05:43.679] [Info]   - login: Object
[2025-08-04 19:05:43.680] [Info] 查找属性: note
[2025-08-04 19:05:43.679] [Info]   - user: Object
[2025-08-04 19:05:43.680] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:05:43.679] [Info]   - note: Object
[2025-08-04 19:05:43.679] [Info]   - notification: Object
[2025-08-04 19:05:43.679] [Info]   - nioStore: Object
[2025-08-04 19:05:43.679] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:05:43.680] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:05:43.679] [Info]   - feed: Object
[2025-08-04 19:05:43.680] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:05:43.680] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:05:43.680] [Info] 查找属性: noteDetailMap
[2025-08-04 19:05:43.680] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:05:43.680] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:05:43.679] [Info]   - activity: Object
[2025-08-04 19:05:43.680] [Info] 处理数组索引: -1
[2025-08-04 19:05:43.682] [Info] 对象包含 1 个属性
[2025-08-04 19:05:43.682] [Info] 使用最后一个索引: 0
[2025-08-04 19:05:43.682] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:05:43.682] [Info] 索引后数据类型: Object
[2025-08-04 19:05:43.682] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:05:43.682] [Info] 查找属性: note
[2025-08-04 19:05:43.682] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:05:43.682] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:05:43.682] [Info] 使用标准路径找到note数据
[2025-08-04 19:05:43.683] [Info] Note数据包含的属性:
[2025-08-04 19:05:43.683] [Info] 处理媒体内容，类型: 
[2025-08-04 19:05:43.683] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:05:43.683] [Info] 开始提取视频URL
[2025-08-04 19:05:43.683] [Warning] 未找到video属性
[2025-08-04 19:05:43.684] [Info] 提取到 0 个图片URL
[2025-08-04 19:05:43.684] [Info] 成功提取作品详情
[2025-08-04 19:05:43.685] [Info] 成功解析作品: 
[2025-08-04 19:05:43.688] [Info] 开始获取小红书作品详情: 
[2025-08-04 19:05:44.083] [Info] 获取到HTML内容，长度: 330207
[2025-08-04 19:05:44.084] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:05:45.099] [Info] 开始解析HTML页面数据
[2025-08-04 19:05:45.102] [Info] 找到目标脚本，长度: 41829
[2025-08-04 19:05:45.102] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:05:45.102] [Info] JavaScript对象字符串长度: 41804
[2025-08-04 19:05:45.102] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:05:45.102] [Info] 使用正则表达式直接提取note数据
[2025-08-04 19:05:45.102] [Info] JSON数据长度: 41804
[2025-08-04 19:05:45.102] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:05:45.102] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:05:45.102] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:05:45.103] [Info] 修复后JSON长度: 41694
[2025-08-04 19:05:45.111] [Info]   - feed: Object
[2025-08-04 19:05:45.111] [Info]   - activity: Object
[2025-08-04 19:05:45.111] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:05:45.103] [Info] 修复后JSON长度: 41694
[2025-08-04 19:05:45.111] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:05:45.111] [Info] 查找属性: note
[2025-08-04 19:05:45.111] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:05:45.111] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:05:45.111] [Info] 查找属性: noteDetailMap
[2025-08-04 19:05:45.111] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:05:45.111] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:05:45.111] [Info] 处理数组索引: -1
[2025-08-04 19:05:45.111] [Info] 对象包含 1 个属性
[2025-08-04 19:05:45.111] [Info] 使用最后一个索引: 0
[2025-08-04 19:05:45.111] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:05:45.111] [Info] 索引后数据类型: Object
[2025-08-04 19:05:45.111] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:05:45.111] [Info] 查找属性: note
[2025-08-04 19:05:45.111] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:05:45.111] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:05:45.112] [Info] Note数据包含的属性:
[2025-08-04 19:05:45.112] [Info] 处理媒体内容，类型: 
[2025-08-04 19:05:45.112] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:05:45.112] [Info] 开始提取视频URL
[2025-08-04 19:05:45.112] [Warning] 未找到video属性
[2025-08-04 19:05:45.112] [Info] 提取到 0 个图片URL
[2025-08-04 19:05:45.112] [Info] 成功提取作品详情
[2025-08-04 19:05:45.111] [Info]   - search: Object
[2025-08-04 19:05:45.111] [Info]   - note: Object
[2025-08-04 19:05:45.103] [Info] 修复后JSON长度: 41694
[2025-08-04 19:05:45.111] [Info]   - nioStore: Object
[2025-08-04 19:05:45.111] [Info]   - notification: Object
[2025-08-04 19:05:45.103] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:05:45.112] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:05:45.112] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:05:45.112] [Info] 使用标准路径找到note数据
[2025-08-04 19:05:45.111] [Info] 根JSON对象属性:
[2025-08-04 19:05:45.111] [Info]   - global: Object
[2025-08-04 19:05:45.111] [Info]   - user: Object
[2025-08-04 19:05:45.111] [Info]   - board: Object
[2025-08-04 19:05:45.111] [Info]   - login: Object
[2025-08-04 19:05:45.103] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:05:45.111] [Info]   - layout: Object
[2025-08-04 19:06:01.497] [Info] 开始获取小红书作品详情: 
[2025-08-04 19:06:01.888] [Info] 获取到HTML内容，长度: 351737
[2025-08-04 19:06:01.889] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:06:02.891] [Info] 开始解析HTML页面数据
[2025-08-04 19:06:02.895] [Info] 找到目标脚本，长度: 50057
[2025-08-04 19:06:02.895] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:06:02.895] [Info] JavaScript对象字符串长度: 50032
[2025-08-04 19:06:02.895] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:06:02.896] [Info] 使用正则表达式直接提取note数据
[2025-08-04 19:06:02.896] [Info] JSON数据长度: 50032
[2025-08-04 19:06:02.896] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:06:02.896] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:06:02.897] [Info] 修复后JSON长度: 49922
[2025-08-04 19:06:02.896] [Info] 修复后JSON长度: 49922
[2025-08-04 19:06:02.897] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:06:02.896] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:06:02.897] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:06:02.897] [Info] 修复后JSON长度: 49922
[2025-08-04 19:06:02.898] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:06:02.898] [Info] 根JSON对象属性:
[2025-08-04 19:06:02.898] [Info]   - global: Object
[2025-08-04 19:06:02.898] [Info]   - board: Object
[2025-08-04 19:06:02.898] [Info]   - feed: Object
[2025-08-04 19:06:02.898] [Info]   - user: Object
[2025-08-04 19:06:02.898] [Info]   - login: Object
[2025-08-04 19:06:02.899] [Info]   - layout: Object
[2025-08-04 19:06:02.899] [Info]   - search: Object
[2025-08-04 19:06:02.899] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:06:02.899] [Info]   - note: Object
[2025-08-04 19:06:02.899] [Info]   - nioStore: Object
[2025-08-04 19:06:02.899] [Info]   - notification: Object
[2025-08-04 19:06:02.899] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:06:02.899] [Info]   - activity: Object
[2025-08-04 19:06:02.899] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:06:02.899] [Info] 查找属性: note
[2025-08-04 19:06:02.900] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:06:02.900] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:06:02.900] [Info] 查找属性: noteDetailMap
[2025-08-04 19:06:02.900] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:06:02.901] [Info] 查找属性: note
[2025-08-04 19:06:02.900] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:06:02.900] [Info] 处理数组索引: -1
[2025-08-04 19:06:02.900] [Info] 对象包含 1 个属性
[2025-08-04 19:06:02.900] [Info] 使用最后一个索引: 0
[2025-08-04 19:06:02.901] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:06:02.901] [Info] 索引后数据类型: Object
[2025-08-04 19:06:02.901] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:06:02.901] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:06:02.901] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:06:02.901] [Info] 使用标准路径找到note数据
[2025-08-04 19:06:02.901] [Info] Note数据包含的属性:
[2025-08-04 19:06:02.902] [Info] 获取到 0 个下载链接
[2025-08-04 19:06:02.902] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:06:02.902] [Info] 开始提取视频URL
[2025-08-04 19:06:02.902] [Warning] 未找到video属性
[2025-08-04 19:06:02.902] [Info] 提取到 0 个图片URL
[2025-08-04 19:06:02.902] [Info] 成功提取作品详情
[2025-08-04 19:06:02.902] [Info] 处理媒体内容，类型: 
[2025-08-04 19:15:39.702] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 19:15:39.706] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 19:15:39.758] [Warning] 未找到yt-dlp，备用解析功能将受限
[2025-08-04 19:15:39.758] [Info] 备用解析器状态: ✗ yt-dlp (未找到)
[2025-08-04 19:15:39.760] [Info] 已加载 7 个平台下载器
[2025-08-04 19:15:39.760] [Info] 支持的平台: 📺哔哩哔哩, 🎬YouTube, 🎵TikTok, 🎵抖音, 🅰️AcFun, 🎭爱奇艺, 🌹小红书
[2025-08-04 19:15:39.763] [Info] 程序初始化完成
[2025-08-04 19:18:38.301] [Info] 检测到平台: 小红书 🌹
[2025-08-04 19:18:38.302] [Info] 开始解析小红书链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 19:18:38.304] [Info] 处理链接: https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed
[2025-08-04 19:18:38.304] [Info] 提取到作品ID: 688c586300000000030254b9
[2025-08-04 19:18:38.304] [Info] 开始获取小红书作品详情: 688c586300000000030254b9
[2025-08-04 19:18:38.849] [Info] 获取到HTML内容，长度: 275572
[2025-08-04 19:18:38.849] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:18:39.864] [Info] 开始解析HTML页面数据
[2025-08-04 19:18:39.880] [Info] 找到目标脚本，长度: 8040
[2025-08-04 19:18:39.880] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:18:39.880] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:18:39.880] [Info] JavaScript对象字符串长度: 8015
[2025-08-04 19:18:39.881] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:18:39.881] [Info] JSON数据长度: 8015
[2025-08-04 19:18:39.881] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:18:39.881] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:18:39.881] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:18:39.883] [Info] 修复后JSON长度: 7895
[2025-08-04 19:18:39.883] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:18:39.883] [Info] 修复后JSON长度: 7895
[2025-08-04 19:18:39.883] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:18:39.883] [Info] 修复后JSON长度: 7895
[2025-08-04 19:18:39.892] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:18:39.892] [Info] 根JSON对象属性:
[2025-08-04 19:18:39.893] [Info]   - global: Object
[2025-08-04 19:18:39.893] [Info]   - user: Object
[2025-08-04 19:18:39.893] [Info]   - activity: Object
[2025-08-04 19:18:39.893] [Info]   - login: Object
[2025-08-04 19:18:39.893] [Info]   - feed: Object
[2025-08-04 19:18:39.893] [Info]   - layout: Object
[2025-08-04 19:18:39.893] [Info]   - board: Object
[2025-08-04 19:18:39.893] [Info]   - search: Object
[2025-08-04 19:18:39.893] [Info]   - note: Object
[2025-08-04 19:18:39.893] [Info]   - notification: Object
[2025-08-04 19:18:39.893] [Info]   - nioStore: Object
[2025-08-04 19:18:39.893] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:18:39.894] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:18:39.894] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:18:39.894] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:18:39.894] [Info] 查找属性: note
[2025-08-04 19:18:39.894] [Info] 查找属性: noteDetailMap
[2025-08-04 19:18:39.896] [Info] 索引后数据类型: Object
[2025-08-04 19:18:39.894] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:18:39.894] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:18:39.894] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:18:39.894] [Info] 处理数组索引: -1
[2025-08-04 19:18:39.896] [Info] 对象包含 1 个属性
[2025-08-04 19:18:39.896] [Info] 使用最后一个索引: 0
[2025-08-04 19:18:39.897] [Info] Note数据包含的属性:
[2025-08-04 19:18:39.896] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:18:39.896] [Info] 查找属性: note
[2025-08-04 19:18:39.896] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:18:39.896] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:18:39.896] [Info] 使用标准路径找到note数据
[2025-08-04 19:18:39.896] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:18:39.898] [Info] 成功提取作品详情
[2025-08-04 19:18:39.897] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:18:39.897] [Info] 开始提取视频URL
[2025-08-04 19:18:39.898] [Warning] 未找到video属性
[2025-08-04 19:18:39.898] [Info] 提取到 0 个图片URL
[2025-08-04 19:18:39.897] [Info] 处理媒体内容，类型: 
[2025-08-04 19:18:39.899] [Info] 成功解析作品: 
[2025-08-04 19:18:39.902] [Info] 开始获取小红书作品详情: 
[2025-08-04 19:18:40.306] [Info] 获取到HTML内容，长度: 347930
[2025-08-04 19:18:40.307] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:18:41.317] [Info] 开始解析HTML页面数据
[2025-08-04 19:18:41.331] [Info] 找到目标脚本，长度: 48613
[2025-08-04 19:18:41.332] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:18:41.332] [Info] JavaScript对象字符串长度: 48588
[2025-08-04 19:18:41.332] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:18:41.332] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:18:41.332] [Info] JSON数据长度: 48588
[2025-08-04 19:18:41.332] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:18:41.332] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:18:41.332] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:18:41.333] [Info] 修复后JSON长度: 48478
[2025-08-04 19:18:41.333] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:18:41.334] [Info]   - search: Object
[2025-08-04 19:18:41.334] [Info]   - notification: Object
[2025-08-04 19:18:41.333] [Info] 修复后JSON长度: 48478
[2025-08-04 19:18:41.334] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:18:41.334] [Info] 查找属性: note
[2025-08-04 19:18:41.334] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:18:41.334] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:18:41.334] [Info] 查找属性: noteDetailMap
[2025-08-04 19:18:41.334] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:18:41.334] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:18:41.334] [Info] 处理数组索引: -1
[2025-08-04 19:18:41.334] [Info] 对象包含 1 个属性
[2025-08-04 19:18:41.334] [Info] 使用最后一个索引: 0
[2025-08-04 19:18:41.334] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:18:41.334] [Info] 索引后数据类型: Object
[2025-08-04 19:18:41.334] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:18:41.334] [Info] 查找属性: note
[2025-08-04 19:18:41.334] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:18:41.334] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:18:41.334] [Info] 使用标准路径找到note数据
[2025-08-04 19:18:41.334] [Info] Note数据包含的属性:
[2025-08-04 19:18:41.334] [Info] 处理媒体内容，类型: 
[2025-08-04 19:18:41.334] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:18:41.334] [Info] 开始提取视频URL
[2025-08-04 19:18:41.334] [Warning] 未找到video属性
[2025-08-04 19:18:41.334] [Info] 提取到 0 个图片URL
[2025-08-04 19:18:41.334] [Info] 成功提取作品详情
[2025-08-04 19:18:41.334] [Info]   - activity: Object
[2025-08-04 19:18:41.334] [Info]   - note: Object
[2025-08-04 19:18:41.333] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:18:41.334] [Info]   - nioStore: Object
[2025-08-04 19:18:41.334] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:18:41.334] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:18:41.333] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:18:41.333] [Info] 根JSON对象属性:
[2025-08-04 19:18:41.333] [Info]   - global: Object
[2025-08-04 19:18:41.333] [Info]   - user: Object
[2025-08-04 19:18:41.333] [Info]   - board: Object
[2025-08-04 19:18:41.333] [Info]   - login: Object
[2025-08-04 19:18:41.334] [Info]   - feed: Object
[2025-08-04 19:18:41.334] [Info]   - layout: Object
[2025-08-04 19:18:41.333] [Info] 修复后JSON长度: 48478
[2025-08-04 19:18:48.914] [Info] 开始获取小红书作品详情: 
[2025-08-04 19:18:49.324] [Info] 获取到HTML内容，长度: 330549
[2025-08-04 19:18:49.324] [Info] HTML开头: <!doctype html><html><head><script formula-runtime >function e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},n=Object.keys(t);"function"==typeof Object.getOwnProperty
[2025-08-04 19:18:50.338] [Info] 开始解析HTML页面数据
[2025-08-04 19:18:50.341] [Info] 找到目标脚本，长度: 41853
[2025-08-04 19:18:50.341] [Info] 脚本开头: window.__INITIAL_STATE__={"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,
[2025-08-04 19:18:50.341] [Info] 使用JSON方法解析JavaScript对象
[2025-08-04 19:18:50.341] [Info] JavaScript对象字符串长度: 41828
[2025-08-04 19:18:50.341] [Info] JS对象开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:18:50.342] [Info] JSON数据长度: 41828
[2025-08-04 19:18:50.342] [Info] JSON数据开头100字符: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":25
[2025-08-04 19:18:50.342] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:18:50.342] [Info] 开始修复JavaScript对象为JSON
[2025-08-04 19:18:50.342] [Info] 修复后JSON长度: 41718
[2025-08-04 19:18:50.343] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:18:50.343] [Info] 修复后JSON长度: 41718
[2025-08-04 19:18:50.343] [Info] 修复后JSON开头: {"global":{"appSettings":{"notificationInterval":30,"prefetchTimeout":3001,"prefetchRedisExpires":259200000,"searchFilterGuideConfig":{"maxDailyShow":1,"maxTotalShow":3,"showInterval":1,"validDays":15
[2025-08-04 19:18:50.343] [Info] 修复后JSON长度: 41718
[2025-08-04 19:18:50.344] [Info] 成功解析完整JSON，开始查找note数据
[2025-08-04 19:18:50.344] [Info] 根JSON对象属性:
[2025-08-04 19:18:50.344] [Info]   - user: Object
[2025-08-04 19:18:50.344] [Info]   - global: Object
[2025-08-04 19:18:50.344] [Info]   - board: Object
[2025-08-04 19:18:50.344] [Info]   - login: Object
[2025-08-04 19:18:50.344] [Info]   - feed: Object
[2025-08-04 19:18:50.345] [Info] 尝试使用标准路径: note.noteDetailMap.[-1].note
[2025-08-04 19:18:50.345] [Info] 处理键 [0]: note, 当前数据类型: Object
[2025-08-04 19:18:50.345] [Info]   - search: Object
[2025-08-04 19:18:50.346] [Info] 处理键 [1]: noteDetailMap, 当前数据类型: Object
[2025-08-04 19:18:50.346] [Info] 查找属性: noteDetailMap
[2025-08-04 19:18:50.345] [Info]   - note: Object
[2025-08-04 19:18:50.345] [Info]   - nioStore: Object
[2025-08-04 19:18:50.346] [Info] 处理数组索引: -1
[2025-08-04 19:18:50.346] [Info] 对象包含 1 个属性
[2025-08-04 19:18:50.347] [Info] 使用最后一个索引: 0
[2025-08-04 19:18:50.345] [Info] 查找属性: note
[2025-08-04 19:18:50.347] [Info] 索引后数据类型: Object
[2025-08-04 19:18:50.345] [Info]   - activity: Object
[2025-08-04 19:18:50.346] [Info] 找到属性 noteDetailMap，数据类型: Object
[2025-08-04 19:18:50.346] [Info] 处理键 [2]: [-1], 当前数据类型: Object
[2025-08-04 19:18:50.345] [Info]   - notification: Object
[2025-08-04 19:18:50.348] [Info] 使用标准路径找到note数据
[2025-08-04 19:18:50.345] [Info]   - layout: Object
[2025-08-04 19:18:50.347] [Info] 选择属性: null, 类型: Object
[2025-08-04 19:18:50.346] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:18:50.347] [Info] 处理键 [3]: note, 当前数据类型: Object
[2025-08-04 19:18:50.347] [Info] 查找属性: note
[2025-08-04 19:18:50.347] [Info] 找到属性 note，数据类型: Object
[2025-08-04 19:18:50.347] [Info] 深度获取完成，最终数据类型: Object
[2025-08-04 19:18:50.345] [Info] 开始深度获取，初始数据类型: Object
[2025-08-04 19:18:50.348] [Info] Note数据包含的属性:
[2025-08-04 19:18:50.348] [Info] 处理媒体内容，类型: 
[2025-08-04 19:18:50.348] [Warning] 未知的媒体类型: ，尝试同时提取视频和图片
[2025-08-04 19:18:50.348] [Info] 开始提取视频URL
[2025-08-04 19:18:50.348] [Warning] 未找到video属性
[2025-08-04 19:18:50.349] [Info] 提取到 0 个图片URL
[2025-08-04 19:18:50.349] [Info] 成功提取作品详情
[2025-08-04 19:18:50.349] [Info] 获取到 0 个下载链接
