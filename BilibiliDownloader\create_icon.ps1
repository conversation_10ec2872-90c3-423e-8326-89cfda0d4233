# 创建像素喵图标
Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

# 创建位图
$bitmap = New-Object System.Drawing.Bitmap(256, 256)
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)

# 设置高质量渲染
$graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
$graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAlias

# 背景渐变
$brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush(
    (New-Object System.Drawing.Point(0, 0)),
    (New-Object System.Drawing.Point(256, 256)),
    [System.Drawing.Color]::FromArgb(255, 74, 144, 226),
    [System.Drawing.Color]::FromArgb(255, 126, 87, 194)
)
$graphics.FillRectangle($brush, 0, 0, 256, 256)

# 绘制圆角矩形背景
$rect = New-Object System.Drawing.Rectangle(20, 20, 216, 216)
$path = New-Object System.Drawing.Drawing2D.GraphicsPath
$radius = 30
$path.AddArc($rect.X, $rect.Y, $radius, $radius, 180, 90)
$path.AddArc($rect.Right - $radius, $rect.Y, $radius, $radius, 270, 90)
$path.AddArc($rect.Right - $radius, $rect.Bottom - $radius, $radius, $radius, 0, 90)
$path.AddArc($rect.X, $rect.Bottom - $radius, $radius, $radius, 90, 90)
$path.CloseFigure()

$whiteBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(240, 255, 255, 255))
$graphics.FillPath($whiteBrush, $path)

# 绘制猫咪图案 (简化的像素风格)
$catBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 88, 88, 88))

# 猫头
$graphics.FillEllipse($catBrush, 90, 70, 76, 70)

# 猫耳朵
$earPoints1 = @(
    (New-Object System.Drawing.Point(95, 75)),
    (New-Object System.Drawing.Point(105, 50)),
    (New-Object System.Drawing.Point(115, 75))
)
$earPoints2 = @(
    (New-Object System.Drawing.Point(141, 75)),
    (New-Object System.Drawing.Point(151, 50)),
    (New-Object System.Drawing.Point(161, 75))
)
$graphics.FillPolygon($catBrush, $earPoints1)
$graphics.FillPolygon($catBrush, $earPoints2)

# 猫眼睛
$eyeBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 34, 139, 34))
$graphics.FillEllipse($eyeBrush, 105, 90, 12, 15)
$graphics.FillEllipse($eyeBrush, 139, 90, 12, 15)

# 瞳孔
$pupilBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::Black)
$graphics.FillEllipse($pupilBrush, 109, 94, 4, 8)
$graphics.FillEllipse($pupilBrush, 143, 94, 4, 8)

# 鼻子
$noseBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 255, 105, 180))
$nosePoints = @(
    (New-Object System.Drawing.Point(128, 110)),
    (New-Object System.Drawing.Point(123, 118)),
    (New-Object System.Drawing.Point(133, 118))
)
$graphics.FillPolygon($noseBrush, $nosePoints)

# 嘴巴
$pen = New-Object System.Drawing.Pen([System.Drawing.Color]::Black, 2)
$graphics.DrawArc($pen, 118, 118, 20, 15, 0, 180)

# 胡须
$graphics.DrawLine($pen, 80, 105, 95, 108)
$graphics.DrawLine($pen, 80, 115, 95, 115)
$graphics.DrawLine($pen, 161, 108, 176, 105)
$graphics.DrawLine($pen, 161, 115, 176, 115)

# 绘制下载箭头
$arrowBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 0, 150, 255))
$arrowPoints = @(
    (New-Object System.Drawing.Point(128, 160)),
    (New-Object System.Drawing.Point(118, 150)),
    (New-Object System.Drawing.Point(124, 150)),
    (New-Object System.Drawing.Point(124, 140)),
    (New-Object System.Drawing.Point(132, 140)),
    (New-Object System.Drawing.Point(132, 150)),
    (New-Object System.Drawing.Point(138, 150))
)
$graphics.FillPolygon($arrowBrush, $arrowPoints)

# 绘制文字
$font = New-Object System.Drawing.Font('Microsoft YaHei', 16, [System.Drawing.FontStyle]::Bold)
$textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 64, 64, 64))
$text = "像素喵"
$textSize = $graphics.MeasureString($text, $font)
$textX = (256 - $textSize.Width) / 2
$graphics.DrawString($text, $font, $textBrush, $textX, 180)

$smallFont = New-Object System.Drawing.Font('Microsoft YaHei', 10, [System.Drawing.FontStyle]::Regular)
$subText = "网络资源下载器"
$subTextSize = $graphics.MeasureString($subText, $smallFont)
$subTextX = (256 - $subTextSize.Width) / 2
$graphics.DrawString($subText, $smallFont, $textBrush, $subTextX, 205)

# 保存为PNG
$bitmap.Save("icon.png", [System.Drawing.Imaging.ImageFormat]::Png)

# 清理资源
$graphics.Dispose()
$bitmap.Dispose()
$brush.Dispose()
$whiteBrush.Dispose()
$catBrush.Dispose()
$eyeBrush.Dispose()
$pupilBrush.Dispose()
$noseBrush.Dispose()
$pen.Dispose()
$arrowBrush.Dispose()
$font.Dispose()
$textBrush.Dispose()
$smallFont.Dispose()

Write-Host "图标已创建: icon.png" -ForegroundColor Green
