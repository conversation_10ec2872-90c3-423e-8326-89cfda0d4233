using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using BilibiliDownloader.Models;
using BilibiliDownloader.Core;

namespace BilibiliDownloader.Utils
{
    public class AccountManager
    {
        private static readonly string AccountFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "accounts.json");
        private List<AccountInfo> _accounts;
        private Dictionary<string, AccountInfo?> _currentAccounts; // 每个平台的当前账号

        public event EventHandler<AccountInfo?> AccountChanged;

        public AccountManager()
        {
            _accounts = new List<AccountInfo>();
            _currentAccounts = new Dictionary<string, AccountInfo?>();
            LoadAccounts();
        }
        
        /// <summary>
        /// 获取所有账号
        /// </summary>
        public List<AccountInfo> GetAllAccounts()
        {
            return _accounts?.ToList() ?? new List<AccountInfo>();
        }

        /// <summary>
        /// 根据平台获取账号列表
        /// </summary>
        public List<AccountInfo> GetAccountsByPlatform(string platform)
        {
            return _accounts?.Where(a => a.Platform.Equals(platform, StringComparison.OrdinalIgnoreCase)).ToList()
                   ?? new List<AccountInfo>();
        }

        /// <summary>
        /// 获取当前账号（兼容旧版本，返回B站账号）
        /// </summary>
        public AccountInfo? GetCurrentAccount()
        {
            return GetCurrentAccount("哔哩哔哩");
        }

        /// <summary>
        /// 获取指定平台的当前账号
        /// </summary>
        public AccountInfo? GetCurrentAccount(string platform)
        {
            _currentAccounts.TryGetValue(platform, out var account);
            return account;
        }

        /// <summary>
        /// 设置指定平台的当前账号
        /// </summary>
        public void SetCurrentAccount(string platform, AccountInfo? account)
        {
            // 清除该平台之前的活跃账号
            var oldAccount = GetCurrentAccount(platform);
            if (oldAccount != null)
            {
                oldAccount.IsActive = false;
            }

            // 设置新的活跃账号
            _currentAccounts[platform] = account;
            if (account != null)
            {
                account.IsActive = true;
                account.LastLoginTime = DateTime.Now;
            }

            SaveAccounts();
            AccountChanged?.Invoke(this, account);
        }
        
        /// <summary>
        /// 添加账号
        /// </summary>
        public void AddAccount(AccountInfo account)
        {
            if (_accounts == null)
                _accounts = new List<AccountInfo>();

            // 检查是否已存在相同平台和用户名的账号
            var existingAccount = _accounts.FirstOrDefault(a =>
                a.Platform.Equals(account.Platform, StringComparison.OrdinalIgnoreCase) &&
                a.Username.Equals(account.Username, StringComparison.OrdinalIgnoreCase));

            if (existingAccount != null)
            {
                // 更新现有账号
                existingAccount.Password = account.Password;
                existingAccount.Cookie = account.Cookie;
                existingAccount.LastLoginTime = DateTime.Now;
                existingAccount.Nickname = account.Nickname;
                existingAccount.Avatar = account.Avatar;
                existingAccount.Level = account.Level;
                existingAccount.IsVip = account.IsVip;
                existingAccount.Email = account.Email;
                existingAccount.Phone = account.Phone;
                existingAccount.Notes = account.Notes;
                existingAccount.LastValidatedTime = DateTime.Now;
                existingAccount.IsValid = true;
            }
            else
            {
                account.CreatedTime = DateTime.Now;
                account.LastValidatedTime = DateTime.Now;
                _accounts.Add(account);
            }

            SaveAccounts();
        }

        /// <summary>
        /// 删除账号
        /// </summary>
        public void RemoveAccount(AccountInfo account)
        {
            if (_accounts != null && account != null)
            {
                _accounts.Remove(account);

                // 如果删除的是当前活跃账号，清除当前账号
                if (_currentAccounts.ContainsValue(account))
                {
                    var platformToRemove = _currentAccounts.FirstOrDefault(kvp => kvp.Value == account).Key;
                    if (!string.IsNullOrEmpty(platformToRemove))
                    {
                        _currentAccounts[platformToRemove] = null;
                        AccountChanged?.Invoke(this, null);
                    }
                }

                SaveAccounts();
            }
        }

        /// <summary>
        /// 根据ID删除账号
        /// </summary>
        public void RemoveAccount(string accountId)
        {
            var account = _accounts?.FirstOrDefault(a => a.Id == accountId);
            if (account != null)
            {
                RemoveAccount(account);
            }
        }

        /// <summary>
        /// 验证账号有效性
        /// </summary>
        public async Task<bool> ValidateAccountAsync(AccountInfo account)
        {
            try
            {
                var platform = PlatformManager.Instance.GetPlatform(account.Platform);
                if (platform != null)
                {
                    var isValid = await platform.ValidateAccountAsync(account.Cookie);
                    account.IsValid = isValid;
                    account.LastValidatedTime = DateTime.Now;
                    SaveAccounts();
                    return isValid;
                }
                return false;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"验证账号失败: {ex.Message}");
                account.IsValid = false;
                SaveAccounts();
                return false;
            }
        }
        
        /// <summary>
        /// 加载账号数据
        /// </summary>
        public void LoadAccounts()
        {
            try
            {
                if (File.Exists(AccountFilePath))
                {
                    var json = File.ReadAllText(AccountFilePath);
                    _accounts = JsonConvert.DeserializeObject<List<AccountInfo>>(json) ?? new List<AccountInfo>();

                    // 恢复各平台的当前活跃账号
                    _currentAccounts.Clear();
                    foreach (var account in _accounts.Where(a => a.IsActive))
                    {
                        _currentAccounts[account.Platform] = account;
                    }

                    // 兼容旧版本数据：如果没有平台信息，默认设为B站
                    foreach (var account in _accounts.Where(a => string.IsNullOrEmpty(a.Platform)))
                    {
                        account.Platform = "哔哩哔哩";
                        account.PlatformIcon = "📺";
                        account.Id = Guid.NewGuid().ToString();
                        account.CreatedTime = account.LastLoginTime;
                        account.LastValidatedTime = account.LastLoginTime;
                        account.IsValid = true;
                    }

                    Logger.Instance.Info($"已加载 {_accounts.Count} 个账号");
                }
                else
                {
                    _accounts = new List<AccountInfo>();
                    _currentAccounts = new Dictionary<string, AccountInfo?>();
                }
            }
            catch (Exception ex)
            {
                // 如果加载失败，创建新的账号列表
                _accounts = new List<AccountInfo>();
                _currentAccounts = new Dictionary<string, AccountInfo?>();
                Logger.Instance.Error($"加载账号文件失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 保存账号数据
        /// </summary>
        public void SaveAccounts()
        {
            try
            {
                var json = JsonConvert.SerializeObject(_accounts, Formatting.Indented);
                File.WriteAllText(AccountFilePath, json);
                Logger.Instance.Info($"已保存 {_accounts.Count} 个账号");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"保存账号文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取账号文件路径
        /// </summary>
        public string GetAccountFilePath()
        {
            return AccountFilePath;
        }

        /// <summary>
        /// 获取平台统计信息
        /// </summary>
        public Dictionary<string, int> GetPlatformStats()
        {
            var stats = new Dictionary<string, int>();

            foreach (var account in _accounts)
            {
                if (stats.ContainsKey(account.Platform))
                {
                    stats[account.Platform]++;
                }
                else
                {
                    stats[account.Platform] = 1;
                }
            }

            return stats;
        }

        /// <summary>
        /// 清理无效账号
        /// </summary>
        public async Task<int> CleanupInvalidAccountsAsync()
        {
            var invalidAccounts = new List<AccountInfo>();

            foreach (var account in _accounts)
            {
                if (!await ValidateAccountAsync(account))
                {
                    invalidAccounts.Add(account);
                }
            }

            foreach (var account in invalidAccounts)
            {
                RemoveAccount(account);
            }

            return invalidAccounts.Count;
        }
    }
}
