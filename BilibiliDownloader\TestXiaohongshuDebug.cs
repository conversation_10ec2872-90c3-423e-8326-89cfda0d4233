using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using BilibiliDownloader.Core;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader
{
    /// <summary>
    /// 调试小红书页面结构
    /// </summary>
    class TestXiaohongshuDebug
    {
        public static async Task DebugPageStructure()
        {
            Console.WriteLine("小红书页面结构调试");
            Console.WriteLine("====================");
            
            var testUrl = "https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed";
            
            try
            {
                Console.WriteLine($"正在获取页面: {testUrl}");
                
                using var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Add("User-Agent", 
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
                httpClient.DefaultRequestHeaders.Add("Accept", 
                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8");
                httpClient.DefaultRequestHeaders.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
                httpClient.DefaultRequestHeaders.Add("Cache-Control", "no-cache");
                
                var response = await httpClient.GetAsync(testUrl);
                response.EnsureSuccessStatusCode();
                
                var html = await response.Content.ReadAsStringAsync();
                
                Console.WriteLine($"页面内容长度: {html.Length}");
                
                // 保存页面内容到文件
                var fileName = "xiaohongshu_page.html";
                await File.WriteAllTextAsync(fileName, html);
                Console.WriteLine($"页面内容已保存到: {fileName}");
                
                // 查找script标签
                var scriptStart = html.IndexOf("window.__INITIAL_STATE__");
                if (scriptStart >= 0)
                {
                    Console.WriteLine($"找到 window.__INITIAL_STATE__ 位置: {scriptStart}");
                    
                    // 找到script标签的开始
                    var scriptTagStart = html.LastIndexOf("<script", scriptStart);
                    var scriptTagEnd = html.IndexOf("</script>", scriptStart);
                    
                    if (scriptTagStart >= 0 && scriptTagEnd >= 0)
                    {
                        var scriptContent = html.Substring(scriptTagStart, scriptTagEnd - scriptTagStart + 9);
                        
                        // 保存script内容
                        var scriptFileName = "xiaohongshu_script.js";
                        await File.WriteAllTextAsync(scriptFileName, scriptContent);
                        Console.WriteLine($"Script内容已保存到: {scriptFileName}");
                        
                        // 提取JSON部分
                        var jsonStart = scriptContent.IndexOf("window.__INITIAL_STATE__=") + "window.__INITIAL_STATE__=".Length;
                        var jsonContent = scriptContent.Substring(jsonStart);
                        
                        // 查找JSON结束位置
                        var jsonEnd = jsonContent.IndexOf(";</script>");
                        if (jsonEnd > 0)
                        {
                            jsonContent = jsonContent.Substring(0, jsonEnd);
                        }
                        
                        // 保存JSON内容
                        var jsonFileName = "xiaohongshu_data.json";
                        await File.WriteAllTextAsync(jsonFileName, jsonContent);
                        Console.WriteLine($"JSON数据已保存到: {jsonFileName}");
                        Console.WriteLine($"JSON数据长度: {jsonContent.Length}");
                        Console.WriteLine($"JSON开头: {jsonContent.Substring(0, Math.Min(200, jsonContent.Length))}");
                    }
                }
                else
                {
                    Console.WriteLine("未找到 window.__INITIAL_STATE__");
                    
                    // 查找其他可能的数据结构
                    var patterns = new[]
                    {
                        "window.__NUXT__",
                        "window.__APOLLO_STATE__",
                        "__NEXT_DATA__",
                        "window.initialState",
                        "window._INITIAL_STATE_"
                    };
                    
                    foreach (var pattern in patterns)
                    {
                        var index = html.IndexOf(pattern);
                        if (index >= 0)
                        {
                            Console.WriteLine($"找到 {pattern} 位置: {index}");
                        }
                    }
                }
                
                Console.WriteLine("\n调试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"调试过程中出现错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
        }
    }
}
