using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;
using BilibiliDownloader.Core;
using BilibiliDownloader.Core.Platforms;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader
{
    /// <summary>
    /// 调试小红书下载链接获取失败问题
    /// </summary>
    class TestXiaohongshuDebug
    {
        public static async Task DebugDownloadLinkIssue()
        {
            Console.WriteLine("小红书下载链接调试");
            Console.WriteLine("====================");

            var testUrl = "https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed";

            try
            {
                Console.WriteLine($"测试链接: {testUrl}");
                Console.WriteLine();

                // 创建小红书下载器
                using var downloader = new XiaohongshuDownloader();

                // 1. 解析视频信息
                Console.WriteLine("1. 解析视频信息...");
                var videoInfo = await downloader.ParseVideoInfoAsync(testUrl);

                if (videoInfo == null)
                {
                    Console.WriteLine("   ❌ 解析失败");
                    return;
                }

                Console.WriteLine($"   ✅ 解析成功");
                Console.WriteLine($"   标题: {videoInfo.Title}");
                Console.WriteLine($"   作者: {videoInfo.Author}");
                Console.WriteLine($"   时长: {videoInfo.Duration}");
                Console.WriteLine($"   平台: {videoInfo.Platform}");
                Console.WriteLine($"   VideoId: {videoInfo.VideoId}");
                Console.WriteLine();

                // 2. 获取可用清晰度
                Console.WriteLine("2. 获取可用清晰度...");
                var qualities = await downloader.GetAvailableQualitiesAsync(videoInfo);
                Console.WriteLine($"   找到 {qualities.Count} 个清晰度选项:");
                foreach (var quality in qualities)
                {
                    Console.WriteLine($"   - {quality.Quality}: {quality.Description} ({quality.Width}x{quality.Height}) {quality.Format}");
                }
                Console.WriteLine();

                // 3. 获取下载链接
                Console.WriteLine("3. 获取下载链接...");
                var selectedQuality = qualities.Count > 0 ? qualities[0].Quality : "默认质量";
                var downloadUrls = await downloader.GetDownloadUrlsAsync(videoInfo, selectedQuality);

                Console.WriteLine($"   获取到 {downloadUrls.Count} 个下载链接:");
                for (int i = 0; i < downloadUrls.Count; i++)
                {
                    var url = downloadUrls[i];
                    Console.WriteLine($"   [{i + 1}] {url.Quality} - {url.Format}");
                    Console.WriteLine($"       URL: {url.Url}");
                    Console.WriteLine($"       大小: {url.FileSize} bytes");
                    Console.WriteLine();
                }

                // 4. 如果没有获取到下载链接，进行深度调试
                if (downloadUrls.Count == 0)
                {
                    Console.WriteLine("4. 深度调试 - 分析note数据结构...");
                    await DebugNoteDataStructure(testUrl);
                }

                Console.WriteLine("调试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"调试过程中出现错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
        }

        private static async Task DebugNoteDataStructure(string testUrl)
        {
            try
            {
                // 直接使用XiaohongshuApi来获取详细的note数据
                using var httpClient = new HttpClient();
                var api = new XiaohongshuApi(httpClient);

                // 提取noteId
                var noteId = api.ExtractNoteId(testUrl);
                Console.WriteLine($"   提取到noteId: {noteId}");

                // 获取note详情
                var noteDetail = await api.GetNoteDetailAsync(noteId);
                if (noteDetail == null)
                {
                    Console.WriteLine("   ❌ 无法获取note详情");
                    return;
                }

                Console.WriteLine($"   ✅ 获取到note详情:");
                Console.WriteLine($"   - NoteId: {noteDetail.NoteId}");
                Console.WriteLine($"   - Title: {noteDetail.Title}");
                Console.WriteLine($"   - Type: {noteDetail.Type}");
                Console.WriteLine($"   - Description: {(noteDetail.Description?.Length > 100 ? noteDetail.Description.Substring(0, 100) + "..." : noteDetail.Description ?? "")}");
                Console.WriteLine($"   - CreateTime: {noteDetail.CreateTime}");
                Console.WriteLine($"   - ImageUrls Count: {noteDetail.ImageUrls?.Count ?? 0}");
                Console.WriteLine($"   - VideoUrls Count: {noteDetail.VideoUrls?.Count ?? 0}");

                // 详细输出图片链接
                if (noteDetail.ImageUrls?.Count > 0)
                {
                    Console.WriteLine($"   图片链接:");
                    for (int i = 0; i < noteDetail.ImageUrls.Count; i++)
                    {
                        Console.WriteLine($"     [{i + 1}] {noteDetail.ImageUrls[i]}");
                    }
                }

                // 详细输出视频链接
                if (noteDetail.VideoUrls?.Count > 0)
                {
                    Console.WriteLine($"   视频链接:");
                    for (int i = 0; i < noteDetail.VideoUrls.Count; i++)
                    {
                        var video = noteDetail.VideoUrls[i];
                        Console.WriteLine($"     [{i + 1}] {video.Quality} ({video.Width}x{video.Height})");
                        Console.WriteLine($"         URL: {video.Url}");
                        Console.WriteLine($"         Size: {video.Size}");
                    }
                }

                // 如果都没有，说明解析有问题
                if ((noteDetail.ImageUrls?.Count ?? 0) == 0 && (noteDetail.VideoUrls?.Count ?? 0) == 0)
                {
                    Console.WriteLine("   ⚠️ 警告: 没有找到任何媒体链接，可能是解析逻辑有问题");

                    // 保存原始HTML用于进一步分析
                    await SaveRawHtmlForAnalysis(testUrl);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   深度调试失败: {ex.Message}");
            }
        }

        private static async Task SaveRawHtmlForAnalysis(string testUrl)
        {
            try
            {
                Console.WriteLine("   保存原始HTML用于分析...");

                using var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Add("User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");

                var response = await httpClient.GetAsync(testUrl);
                var html = await response.Content.ReadAsStringAsync();

                var fileName = $"debug_xiaohongshu_{DateTime.Now:yyyyMMdd_HHmmss}.html";
                await File.WriteAllTextAsync(fileName, html);
                Console.WriteLine($"   HTML已保存到: {fileName}");

                // 提取并保存JSON数据
                var scriptStart = html.IndexOf("window.__INITIAL_STATE__");
                if (scriptStart >= 0)
                {
                    var jsonStart = scriptStart + "window.__INITIAL_STATE__=".Length;
                    var remaining = html.Substring(jsonStart);
                    var jsonEnd = remaining.IndexOf(";</script>");
                    if (jsonEnd > 0)
                    {
                        var jsonContent = remaining.Substring(0, jsonEnd);
                        var jsonFileName = $"debug_xiaohongshu_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                        await File.WriteAllTextAsync(jsonFileName, jsonContent);
                        Console.WriteLine($"   JSON数据已保存到: {jsonFileName}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   保存HTML失败: {ex.Message}");
            }
        }

        public static async Task DebugPageStructure()
        {
            await DebugDownloadLinkIssue();
        }
    }
}
