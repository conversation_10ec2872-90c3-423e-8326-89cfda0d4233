﻿#pragma checksum "..\..\..\..\UI\AccountManagerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "049DC68C0D7B6396D2CC340D80D6314451D363D4"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using BilibiliDownloader.UI;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace BilibiliDownloader.UI {
    
    
    /// <summary>
    /// AccountManagerWindow
    /// </summary>
    public partial class AccountManagerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 37 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox lbAccounts;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnDeleteAccount;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnUseAccount;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtUsername;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox txtPassword;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkSavePassword;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddAccount;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnTestLogin;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtCookie;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddCookie;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnOpenAccountFile;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClose;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/BilibiliDownloader;component/ui/accountmanagerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\UI\AccountManagerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.lbAccounts = ((System.Windows.Controls.ListBox)(target));
            
            #line 37 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.lbAccounts.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.LbAccounts_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.btnDeleteAccount = ((System.Windows.Controls.Button)(target));
            
            #line 49 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnDeleteAccount.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteAccount_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnUseAccount = ((System.Windows.Controls.Button)(target));
            
            #line 50 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnUseAccount.Click += new System.Windows.RoutedEventHandler(this.BtnUseAccount_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.txtUsername = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.txtPassword = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 6:
            this.chkSavePassword = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 7:
            this.btnAddAccount = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnAddAccount.Click += new System.Windows.RoutedEventHandler(this.BtnAddAccount_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.btnTestLogin = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnTestLogin.Click += new System.Windows.RoutedEventHandler(this.BtnTestLogin_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.txtCookie = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.btnAddCookie = ((System.Windows.Controls.Button)(target));
            
            #line 78 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnAddCookie.Click += new System.Windows.RoutedEventHandler(this.BtnAddCookie_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btnOpenAccountFile = ((System.Windows.Controls.Button)(target));
            
            #line 86 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnOpenAccountFile.Click += new System.Windows.RoutedEventHandler(this.BtnOpenAccountFile_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.btnClose = ((System.Windows.Controls.Button)(target));
            
            #line 87 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnClose.Click += new System.Windows.RoutedEventHandler(this.BtnClose_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

