using BilibiliDownloader.Core.Platforms;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core
{
    /// <summary>
    /// 平台管理器 - 统一管理所有支持的平台
    /// </summary>
    public class PlatformManager
    {
        private readonly List<IPlatformDownloader> _platforms;
        private static PlatformManager? _instance;
        private static readonly object _lock = new object();

        public static PlatformManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new PlatformManager();
                    }
                }
                return _instance;
            }
        }

        private PlatformManager()
        {
            _platforms = new List<IPlatformDownloader>();
            InitializePlatforms();
        }

        /// <summary>
        /// 初始化所有支持的平台
        /// </summary>
        private void InitializePlatforms()
        {
            try
            {
                // 添加所有支持的平台
                _platforms.Add(new BilibiliPlatformDownloader());
                _platforms.Add(new YouTubeDownloader());
                _platforms.Add(new TikTokDownloader());
                _platforms.Add(new DouyinDownloader());
                _platforms.Add(new AcFunDownloader());
                _platforms.Add(new IqiyiDownloader());
                _platforms.Add(new XiaohongshuDownloader());


                Logger.Instance.Info($"已加载 {_platforms.Count} 个平台下载器");

                // 输出支持的平台列表
                var platformList = string.Join(", ", _platforms.Select(p => $"{p.PlatformIcon}{p.PlatformName}"));
                Logger.Instance.Info($"支持的平台: {platformList}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"初始化平台失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取所有支持的平台
        /// </summary>
        public List<IPlatformDownloader> GetAllPlatforms()
        {
            return _platforms.ToList();
        }

        /// <summary>
        /// 根据URL自动检测平台
        /// </summary>
        public IPlatformDownloader? DetectPlatform(string url)
        {
            if (string.IsNullOrEmpty(url))
                return null;

            foreach (var platform in _platforms)
            {
                if (platform.IsUrlSupported(url))
                {
                    Logger.Instance.Info($"检测到平台: {platform.PlatformName} {platform.PlatformIcon}");
                    return platform;
                }
            }

            Logger.Instance.Warning($"未找到支持的平台: {url}");
            return null;
        }

        /// <summary>
        /// 根据平台名称获取下载器
        /// </summary>
        public IPlatformDownloader? GetPlatform(string platformName)
        {
            return _platforms.FirstOrDefault(p => 
                p.PlatformName.Equals(platformName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取平台统计信息
        /// </summary>
        public PlatformStats GetPlatformStats()
        {
            var stats = new PlatformStats
            {
                TotalPlatforms = _platforms.Count,
                PlatformsRequiringLogin = _platforms.Count(p => p.RequiresLogin),
                SupportedUrlPatterns = _platforms.SelectMany(p => p.SupportedUrlPatterns).Count()
            };

            return stats;
        }

        /// <summary>
        /// 验证URL是否被任何平台支持
        /// </summary>
        public bool IsUrlSupported(string url)
        {
            return DetectPlatform(url) != null;
        }

        /// <summary>
        /// 获取支持的URL示例
        /// </summary>
        public Dictionary<string, List<string>> GetSupportedUrlExamples()
        {
            var examples = new Dictionary<string, List<string>>();

            foreach (var platform in _platforms)
            {
                var platformExamples = new List<string>();
                
                switch (platform.PlatformName)
                {
                    case "哔哩哔哩":
                        platformExamples.AddRange(new[]
                        {
                            "https://www.bilibili.com/video/BV1xx411c7mD",
                            "https://www.bilibili.com/bangumi/play/ep123456",
                            "https://b23.tv/abc123"
                        });
                        break;
                    case "YouTube":
                        platformExamples.AddRange(new[]
                        {
                            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
                            "https://youtu.be/dQw4w9WgXcQ",
                            "https://www.youtube.com/shorts/abc123"
                        });
                        break;
                    case "TikTok":
                        platformExamples.AddRange(new[]
                        {
                            "https://www.tiktok.com/@user/video/1234567890",
                            "https://vm.tiktok.com/abc123",
                            "https://www.tiktok.com/t/abc123"
                        });
                        break;

                    case "抖音":
                        platformExamples.AddRange(new[]
                        {
                            "https://www.douyin.com/video/1234567890",
                            "https://v.douyin.com/abc123"
                        });
                        break;
                    case "AcFun":
                        platformExamples.AddRange(new[]
                        {
                            "https://www.acfun.cn/v/ac12345678",
                            "https://www.acfun.cn/bangumi/aa12345678"
                        });
                        break;
                    case "爱奇艺":
                        platformExamples.AddRange(new[]
                        {
                            "https://www.iqiyi.com/v_abc123.html",
                            "https://www.iqiyi.com/a_abc123.html"
                        });
                        break;
                    case "小红书":
                        platformExamples.AddRange(new[]
                        {
                            "https://www.xiaohongshu.com/explore/abc123",
                            "https://xhslink.com/abc123"
                        });
                        break;
                }

                if (platformExamples.Count > 0)
                {
                    examples[platform.PlatformName] = platformExamples;
                }
            }

            return examples;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            foreach (var platform in _platforms)
            {
                if (platform is IDisposable disposable)
                {
                    disposable.Dispose();
                }
            }
            _platforms.Clear();
        }
    }

    /// <summary>
    /// 平台统计信息
    /// </summary>
    public class PlatformStats
    {
        public int TotalPlatforms { get; set; }
        public int PlatformsRequiringLogin { get; set; }
        public int SupportedUrlPatterns { get; set; }
    }
}
