using System.Text.RegularExpressions;
using System.Text.Json;
using System.Net.Http;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core.Platforms
{
    public class AcFunDownloader : IPlatformDownloader
    {
        private readonly HttpClient _httpClient;

        public string PlatformName => "AcFun";
        public string PlatformIcon => "🅰️";
        public bool RequiresLogin => false;

        public string[] SupportedUrlPatterns => new[]
        {
            @"https?://(?:www\.)?acfun\.cn/v/ac\d+",
            @"https?://(?:www\.)?acfun\.cn/a/ac\d+",
            @"https?://m\.acfun\.cn/v/\?ac=\d+",
            @"https?://(?:www\.)?acfun\.cn/bangumi/aa\d+"
        };

        public AcFunDownloader()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        }

        public bool IsUrlSupported(string url)
        {
            return SupportedUrlPatterns.Any(pattern => Regex.IsMatch(url, pattern));
        }

        public async Task<VideoInfo?> ParseVideoInfoAsync(string url, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"解析AcFun视频: {url}");

                // 设置Cookie（如果提供）
                if (!string.IsNullOrEmpty(cookie))
                {
                    _httpClient.DefaultRequestHeaders.Remove("Cookie");
                    _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
                }

                var videoId = ExtractVideoId(url);
                if (string.IsNullOrEmpty(videoId))
                {
                    throw new Exception("无法提取视频ID");
                }

                // 获取视频信息
                var videoInfo = await GetAcFunVideoInfoAsync(videoId, url);
                if (videoInfo != null)
                {
                    videoInfo.Platform = PlatformName;
                    videoInfo.OriginalUrl = url;
                }

                return videoInfo;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"AcFun解析失败: {ex.Message}");
                return null;
            }
        }

        private async Task<VideoInfo?> GetAcFunVideoInfoAsync(string videoId, string url)
        {
            try
            {
                // 获取网页内容
                var pageContent = await _httpClient.GetStringAsync(url);

                // 从页面中提取视频信息
                var title = ExtractTitleFromPage(pageContent) ?? $"AcFun视频 {videoId}";
                var author = ExtractAuthorFromPage(pageContent) ?? "未知UP主";
                var description = ExtractDescriptionFromPage(pageContent) ?? "";
                var coverUrl = ExtractCoverFromPage(pageContent) ?? "";
                var duration = ExtractDurationFromPage(pageContent);
                var viewCount = ExtractViewCountFromPage(pageContent);

                return new VideoInfo
                {
                    VideoId = videoId,
                    Title = title,
                    Author = author,
                    Description = description,
                    CoverUrl = coverUrl,
                    Duration = duration,
                    ViewCount = viewCount,
                    PublishTime = DateTime.Now, // AcFun API限制
                    IsCollection = false,
                    IsPaymentRequired = false,
                    Pages = new List<VideoPage>
                    {
                        new VideoPage
                        {
                            Page = 1,
                            Cid = 0,
                            Part = title,
                            Duration = duration
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取AcFun视频信息失败: {ex.Message}");
                return null;
            }
        }

        public async Task<List<QualityOption>> GetAvailableQualitiesAsync(VideoInfo videoInfo, string? cookie = null)
        {
            var qualities = new List<QualityOption>
            {
                new() { Quality = "1080p", Description = "超清", Width = 1920, Height = 1080, Format = "mp4" },
                new() { Quality = "720p", Description = "高清", Width = 1280, Height = 720, Format = "mp4" },
                new() { Quality = "540p", Description = "标清", Width = 960, Height = 540, Format = "mp4" },
                new() { Quality = "360p", Description = "流畅", Width = 640, Height = 360, Format = "mp4" }
            };

            return qualities;
        }

        public async Task<List<DownloadUrl>> GetDownloadUrlsAsync(VideoInfo videoInfo, string quality, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"获取AcFun下载链接: {videoInfo.VideoId}, 质量: {quality}");

                // 设置Cookie（如果提供）
                if (!string.IsNullOrEmpty(cookie))
                {
                    _httpClient.DefaultRequestHeaders.Remove("Cookie");
                    _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
                }

                // 尝试获取实际的下载链接
                var downloadUrls = await GetAcFunDownloadUrlsAsync(videoInfo.VideoId, quality);
                
                if (downloadUrls.Count == 0)
                {
                    Logger.Instance.Warning("无法获取AcFun直接下载链接");
                    throw new Exception("AcFun下载需要特殊处理。由于AcFun的反爬虫机制，无法直接获取下载链接。");
                }

                return downloadUrls;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取AcFun下载链接失败: {ex.Message}");
                throw new Exception($"AcFun下载失败: {ex.Message}\n\n建议：\n1. 使用专业的下载工具\n2. 检查网络连接\n3. 确认视频是否可用");
            }
        }

        private async Task<List<DownloadUrl>> GetAcFunDownloadUrlsAsync(string videoId, string quality)
        {
            try
            {
                // 注意：由于AcFun的反爬虫机制，这里只是示例实现
                Logger.Instance.Warning("AcFun下载功能需要集成专业工具才能正常工作");
                return new List<DownloadUrl>();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"AcFun下载URL获取异常: {ex.Message}");
                return new List<DownloadUrl>();
            }
        }

        public async Task<bool> ValidateAccountAsync(string cookie)
        {
            try
            {
                // 简单验证Cookie是否有效
                _httpClient.DefaultRequestHeaders.Remove("Cookie");
                _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
                
                var response = await _httpClient.GetAsync("https://www.acfun.cn/");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private string? ExtractTitleFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取标题
                var titleMatch = Regex.Match(pageContent, @"<title>([^<]+)</title>");
                if (titleMatch.Success)
                {
                    return titleMatch.Groups[1].Value.Replace(" - AcFun弹幕视频网", "").Trim();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractAuthorFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取UP主信息
                var authorMatch = Regex.Match(pageContent, @"""username"":""([^""]+)""");
                if (authorMatch.Success)
                {
                    return authorMatch.Groups[1].Value;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractDescriptionFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取描述
                var descMatch = Regex.Match(pageContent, @"""description"":""([^""]+)""");
                if (descMatch.Success)
                {
                    return descMatch.Groups[1].Value.Replace("\\n", "\n").Replace("\\\"", "\"");
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractCoverFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取封面图
                var coverMatch = Regex.Match(pageContent, @"""coverUrl"":""([^""]+)""");
                if (coverMatch.Success)
                {
                    return coverMatch.Groups[1].Value.Replace("\\u002F", "/");
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private long ExtractDurationFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取时长
                var durationMatch = Regex.Match(pageContent, @"""durationMillis"":(\d+)");
                if (durationMatch.Success && long.TryParse(durationMatch.Groups[1].Value, out var duration))
                {
                    return duration / 1000; // 转换为秒
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private long ExtractViewCountFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取观看次数
                var viewMatch = Regex.Match(pageContent, @"""viewCount"":(\d+)");
                if (viewMatch.Success && long.TryParse(viewMatch.Groups[1].Value, out var viewCount))
                {
                    return viewCount;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private string ExtractVideoId(string url)
        {
            var patterns = new[]
            {
                @"acfun\.cn/v/ac(\d+)",
                @"acfun\.cn/a/ac(\d+)",
                @"ac=(\d+)",
                @"bangumi/aa(\d+)"
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(url, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            return "";
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
