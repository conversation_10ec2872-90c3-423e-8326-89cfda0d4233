﻿#pragma checksum "..\..\..\..\UI\XiaohongshuLoginGuideWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6FB8177D774CAF29B7555FDA5F54D8BB276A0484"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace BilibiliDownloader.UI {
    
    
    /// <summary>
    /// XiaohongshuLoginGuideWindow
    /// </summary>
    public partial class XiaohongshuLoginGuideWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 102 "..\..\..\..\UI\XiaohongshuLoginGuideWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenXiaohongshuButton;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\UI\XiaohongshuLoginGuideWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenAccountManagerButton;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\UI\XiaohongshuLoginGuideWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/BilibiliDownloader;V2.0.0.0;component/ui/xiaohongshuloginguidewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\UI\XiaohongshuLoginGuideWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.OpenXiaohongshuButton = ((System.Windows.Controls.Button)(target));
            
            #line 103 "..\..\..\..\UI\XiaohongshuLoginGuideWindow.xaml"
            this.OpenXiaohongshuButton.Click += new System.Windows.RoutedEventHandler(this.OpenXiaohongshuButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.OpenAccountManagerButton = ((System.Windows.Controls.Button)(target));
            
            #line 105 "..\..\..\..\UI\XiaohongshuLoginGuideWindow.xaml"
            this.OpenAccountManagerButton.Click += new System.Windows.RoutedEventHandler(this.OpenAccountManagerButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 114 "..\..\..\..\UI\XiaohongshuLoginGuideWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

