using System;
using System.Threading.Tasks;
using BilibiliDownloader.Core.Platforms;

namespace BilibiliDownloader
{
    /// <summary>
    /// 测试修复后的小红书下载功能
    /// </summary>
    class TestXiaohongshuFixed
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("测试修复后的小红书下载功能");
            Console.WriteLine("================================");
            
            var testUrl = "https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed";
            
            try
            {
                Console.WriteLine($"测试链接: {testUrl}");
                Console.WriteLine();
                
                // 创建小红书下载器
                using var downloader = new XiaohongshuDownloader();
                
                // 1. 解析视频信息
                Console.WriteLine("1. 解析视频信息...");
                var videoInfo = await downloader.ParseVideoInfoAsync(testUrl);
                
                if (videoInfo == null)
                {
                    Console.WriteLine("   ❌ 解析失败");
                    return;
                }
                
                Console.WriteLine($"   ✅ 解析成功");
                Console.WriteLine($"   标题: {videoInfo.Title}");
                Console.WriteLine($"   作者: {videoInfo.Author}");
                Console.WriteLine($"   时长: {videoInfo.Duration}");
                Console.WriteLine($"   平台: {videoInfo.Platform}");
                Console.WriteLine($"   VideoId: {videoInfo.VideoId}");
                Console.WriteLine();
                
                // 2. 获取可用清晰度
                Console.WriteLine("2. 获取可用清晰度...");
                var qualities = await downloader.GetAvailableQualitiesAsync(videoInfo);
                Console.WriteLine($"   找到 {qualities.Count} 个清晰度选项:");
                foreach (var quality in qualities)
                {
                    Console.WriteLine($"   - {quality.Quality}: {quality.Description} ({quality.Width}x{quality.Height}) {quality.Format}");
                }
                Console.WriteLine();
                
                // 3. 获取下载链接
                Console.WriteLine("3. 获取下载链接...");
                var selectedQuality = qualities.Count > 0 ? qualities[0].Quality : "默认质量";
                var downloadUrls = await downloader.GetDownloadUrlsAsync(videoInfo, selectedQuality);
                
                Console.WriteLine($"   获取到 {downloadUrls.Count} 个下载链接:");
                for (int i = 0; i < downloadUrls.Count; i++)
                {
                    var url = downloadUrls[i];
                    Console.WriteLine($"   [{i + 1}] {url.Quality} - {url.Format}");
                    Console.WriteLine($"       URL: {url.Url}");
                    Console.WriteLine($"       大小: {url.FileSize} bytes");
                    Console.WriteLine();
                }
                
                // 4. 测试结果
                if (downloadUrls.Count > 0)
                {
                    Console.WriteLine("🎉 测试成功！小红书下载功能已修复！");
                    Console.WriteLine($"成功获取到 {downloadUrls.Count} 个下载链接");
                }
                else
                {
                    Console.WriteLine("❌ 测试失败：仍然无法获取下载链接");
                }
                
                Console.WriteLine("\n测试完成！按任意键退出...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中出现错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }
    }
}
