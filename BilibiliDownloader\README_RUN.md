# Bilibili下载器 - 运行指南

## 🚀 使用 dotnet run 运行程序

### 前提条件
- 已安装 .NET 8.0 或更高版本 SDK
- 确保在项目根目录（包含 BilibiliDownloader.csproj 文件的目录）

### 🎯 最简单的运行方法

#### 方法1：双击运行脚本（推荐）
1. **双击 `quick_run.bat`** - 快速启动（推荐日常使用）
2. **双击 `run.bat`** - 完整启动（包含详细检查和错误处理）

#### 方法2：使用命令行
```bash
# 1. 进入项目目录
cd "c:\Users\<USER>\Desktop\bilibli下载器\BilibiliDownloader"

# 2. 快速运行
dotnet run

# 或者指定项目文件
dotnet run --project BilibiliDownloader.csproj
```

#### 方法3：完整编译流程
```bash
# 1. 进入项目目录
cd "c:\Users\<USER>\Desktop\bilibli下载器\BilibiliDownloader"

# 2. 清理项目（可选）
dotnet clean

# 3. 恢复依赖
dotnet restore

# 4. 编译并运行
dotnet run
```

#### 方法2：使用提供的脚本
双击运行以下任一脚本：
- `run.bat` - Windows批处理脚本
- `run.ps1` - PowerShell脚本

#### 方法3：一步运行
```bash
dotnet run --project "c:\Users\<USER>\Desktop\bilibli下载器\BilibiliDownloader\BilibiliDownloader.csproj"
```

### 编译选项

#### 调试模式（默认）
```bash
dotnet run
```

#### 发布模式
```bash
dotnet run --configuration Release
```

#### 详细输出
```bash
dotnet run --verbosity detailed
```

### 故障排除

#### 问题1：找不到项目文件
**错误信息**：`找不到要运行的项目`
**解决方案**：
1. 确保在正确的目录中（包含 .csproj 文件）
2. 使用 `dir *.csproj` 或 `ls *.csproj` 检查项目文件
3. 使用完整路径：`dotnet run --project BilibiliDownloader.csproj`

#### 问题2：编译错误
**解决方案**：
1. 运行 `dotnet restore` 恢复依赖
2. 运行 `dotnet clean` 清理旧文件
3. 检查 .NET SDK 版本：`dotnet --version`

#### 问题3：依赖问题
**解决方案**：
1. 删除 `bin` 和 `obj` 文件夹
2. 运行 `dotnet restore`
3. 重新编译

### 项目结构验证
确保以下文件存在：
- ✅ BilibiliDownloader.csproj
- ✅ MainWindow.xaml
- ✅ MainWindow.xaml.cs
- ✅ App.xaml
- ✅ App.xaml.cs

### 依赖文件
程序运行时需要以下文件（会自动复制到输出目录）：
- BBDown.exe（番剧下载工具）
- ffmpeg.exe（视频处理工具）

### 开发模式
如果要进行开发和调试：
```bash
# 监视文件变化并自动重新编译
dotnet watch run

# 启用热重载
dotnet watch run --hot-reload
```

## 📝 注意事项

1. **首次运行**可能需要较长时间来下载和恢复NuGet包
2. **防火墙提示**：程序需要网络访问权限来下载视频
3. **杀毒软件**：某些杀毒软件可能会误报，请添加到白名单
4. **管理员权限**：通常不需要，但如果遇到权限问题可以尝试

## 🔧 高级选项

### 自定义启动参数
```bash
# 指定配置文件
dotnet run -- --config custom.json

# 启用详细日志
dotnet run -- --verbose

# 指定下载目录
dotnet run -- --download-dir "D:\Downloads"
```

### 性能优化
```bash
# 使用发布配置（更快的启动时间）
dotnet run -c Release

# 启用ReadyToRun（更快的启动时间）
dotnet publish -c Release -r win-x64 --self-contained false
```

---

如果遇到任何问题，请检查：
1. .NET SDK 是否正确安装
2. 项目文件是否完整
3. 网络连接是否正常
4. 防火墙和杀毒软件设置
