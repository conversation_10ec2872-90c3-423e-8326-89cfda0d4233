# English translations for XHS-Downloader package.
# Copyright (C) 2024 THE XHS-Downloader'S COPYRIGHT HOLDER
# This file is distributed under the same license as the XHS-Downloader package.
# <AUTHOR> <EMAIL>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: XHS-Downloader 2.5\n"
"Report-Msgid-Bugs-To: <<EMAIL>>\n"
"POT-Creation-Date: 2025-04-20 19:05+0800\n"
"PO-Revision-Date: 2024-12-22 14:14+0800\n"
"Last-Translator: <<EMAIL>>\n"
"Language-Team: English\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:178
#, python-brace-format
msgid "作品 {0} 存在下载记录，跳过下载"
msgstr "works {0} has a download record, skip download"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:195
msgid "提取作品文件下载地址失败"
msgstr "Failed to extract the download address for the RedNote works files"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:224
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:251
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:549
msgid "提取小红书作品链接失败"
msgstr "Failed to extract the links for RedNote works"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:226
#, python-brace-format
msgid "共 {0} 个小红书作品待处理..."
msgstr "{0} works from RedNote are awaiting processing..."

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:296
#, python-brace-format
msgid "作品 {0} 存在下载记录，跳过处理"
msgstr "Works {0} has a download record, skip processing"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:299
#, python-brace-format
msgid "开始处理作品：{0}"
msgstr "Start processing the works: {0}"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:308
#, python-brace-format
msgid "{0} 获取数据失败"
msgstr "{0} failed to retrieve data"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:313
#, python-brace-format
msgid "{0} 提取数据失败"
msgstr "{0} failed to extract data"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:315
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:83
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\explore.py:13
msgid "视频"
msgstr "video"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:317
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:90
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\explore.py:13
msgid "图文"
msgstr "image"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:323
#, python-brace-format
msgid "作品处理完成：{0}"
msgstr "works processing completed: {0}"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:401
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\monitor.py:57
msgid ""
"程序会自动读取并提取剪贴板中的小红书作品链接，并自动下载链接对应的作品文件，"
"如需关闭，请点击关闭按钮，或者向剪贴板写入 “close” 文本！"
msgstr ""
"The program will automatically read and extract the link to RedNote works "
"from the clipboard, and automatically download the corresponding work file. "
"If you want to close it, please click the close button or write the "
"\"close\" text to the clipboard!"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:562
msgid "获取小红书作品数据成功"
msgstr "Successfully obtained data on RedNote works"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:564
msgid "获取小红书作品数据失败"
msgstr "Failed to obtain data on RedNote works"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:130
msgid "视频作品下载功能已关闭，跳过下载"
msgstr "The video download function has been turned off, skip download"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:147
msgid "图文作品下载功能已关闭，跳过下载"
msgstr "The image download function has been turned off, skip download"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:182
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:193
#, python-brace-format
msgid "{0} 文件已存在，跳过下载"
msgstr "{0} already exists, skipping download"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:239
#, python-brace-format
msgid "文件 {0} 缓存异常，重新下载"
msgstr "File {0} cache exception, download again"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:267
#, python-brace-format
msgid "文件 {0} 下载成功"
msgstr "file {0} download successful"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:273
#, python-brace-format
msgid "网络异常，{0} 下载失败，错误信息: {1}"
msgstr "Network error, {0} download failed, error message: {1}"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:350
#, python-brace-format
msgid "文件 {0} 格式判断失败，错误信息：{1}"
msgstr "Format recognition failed for file {0}, error message: {1}"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\explore.py:50
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\explore.py:58
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\explore.py:63
msgid "未知"
msgstr "unknown"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\request.py:80
#, python-brace-format
msgid "网络异常，{0} 请求失败: {1}"
msgstr "Network error, {0} request failed: {1}"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:124
msgid "小红书作品链接"
msgstr "Link to RedNote works"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:131
msgid "下载指定序号的图片文件，仅对图文作品生效；多个序号输入示例：\"1 3 5 7\""
msgstr ""
"Download image files with specified serial numbers, only effective for image "
"works; Example of multiple serial numbers input: \"1 3 5 7\""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:136
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:30
msgid "作品数据 / 文件保存根路径"
msgstr "Root path for saving works data / files"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:137
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:40
msgid "作品文件储存文件夹名称"
msgstr "Name of the folder for storing works files"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:138
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:49
msgid "作品文件名称格式"
msgstr "Format of works file name"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:140
msgid "小红书网页版 Cookie，无需登录"
msgstr "RedNote web version cookie, no need to log in"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:141
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:78
msgid "网络代理"
msgstr "Network proxy"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:142
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:88
msgid "请求数据超时限制，单位：秒"
msgstr "Network request timeout limit, in seconds"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:148
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:98
msgid "下载文件时，每次从服务器获取的数据块大小，单位：字节"
msgstr ""
"When downloading a file, the size of the data block obtained from the server "
"each time, in bytes"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:151
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:108
msgid "请求数据失败时，重试的最大次数"
msgstr "The maximum number of retries when data request fails"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:152
msgid "是否记录作品数据至文件"
msgstr "Record works data to file"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:157
msgid "图文作品文件下载格式，支持：PNG、WEBP"
msgstr "Image works file download format, supporting: PNG, WEBP"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:159
msgid "动态图片下载开关"
msgstr "LivePhoto download switch"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:160
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:149
msgid "作品下载记录开关"
msgstr "Download record switch"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:165
msgid "是否将每个作品的文件储存至单独的文件夹"
msgstr "Whether to save each work's files into separate folders"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:171
msgid "是否将每个作者的作品储存至单独的文件夹"
msgstr "Whether to save each author's works into separate folders"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:177
msgid "是否将作品文件的修改时间属性修改为作品的发布时间"
msgstr "Would you like to set the file's modified time attribute to match the work's publication time"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:179
msgid "设置程序语言，目前支持：zh_CN、en_US"
msgstr "Set the programming language, currently supports: zh_CN、en_US"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:180
msgid "读取指定配置文件"
msgstr "Read specified configuration file"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:187
#, python-brace-format
msgid "从指定的浏览器读取小红书网页版 Cookie，支持：{0}; 输入浏览器名称或序号"
msgstr ""
"Read RedNote web version cookies from the specified browser, supporting: "
"{0}; Enter browser name or serial number"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:200
msgid "是否更新配置文件"
msgstr "Do you need to update the configuration file"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:201
msgid "查看详细参数说明"
msgstr "View detailed parameter descriptions"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:202
msgid "查看 XHS-Downloader 版本"
msgstr "View XHS Downloader Version"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\expansion\browser.py:53
#, python-brace-format
msgid ""
"读取指定浏览器的 Cookie 并写入配置文件\n"
"Windows 系统需要以管理员身份运行程序才能读取 Chromium、Chrome、Edge 浏览器 "
"Cookie！\n"
"{options}\n"
"请输入浏览器名称或序号："
msgstr ""
"Read cookies from the specified browser and write them to the configuration "
"file\n"
"The Windows system requires running programs as an administrator to read "
"Chromium, Chrome, Edge browser cookies!\n"
"{options}\n"
"Please enter your browser name or serial number:"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\expansion\browser.py:63
msgid "未选择浏览器！"
msgstr "Browser not selected!"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\expansion\browser.py:74
msgid "浏览器名称或序号输入错误！"
msgstr "Browser name or serial number input error!"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\expansion\browser.py:80
msgid "获取 Cookie 失败，未找到 Cookie 数据！"
msgstr "Failed to retrieve cookie, no cookie data found!"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\expansion\browser.py:118
msgid "从浏览器读取 Cookie 功能不支持当前平台！"
msgstr ""
"The cookie reading function from the browser is not supported on the current "
"platform!"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\expansion\cleaner.py:45
msgid "不受支持的操作系统类型，可能无法正常去除非法字符！"
msgstr ""
"Unsupported operating system type, may not be able to remove illegal "
"characters properly!"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\manager.py:226
#, python-brace-format
msgid "代理 {0} 测试成功"
msgstr "Agent {0} tested successfully"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\manager.py:230
#, python-brace-format
msgid "代理 {0} 测试超时"
msgstr "Agent {0} test timeout"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\manager.py:238
#, python-brace-format
msgid "代理 {0} 测试失败：{1}"
msgstr "Agent {0} test failed: {1}"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\mapping.py:57
#, python-brace-format
msgid "{old_folder} 文件夹不存在，跳过处理"
msgstr "{old_folder} directory does not exist, skipping processing"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\mapping.py:86
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\mapping.py:108
msgid "文件夹"
msgstr "folder"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\mapping.py:91
#, python-brace-format
msgid "文件夹 {old_folder} 已重命名为 {new_folder}"
msgstr "The folder {old_folder} has been renamed to {new_folder}"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\mapping.py:113
#, python-brace-format
msgid "文件夹 {old_} 重命名为 {new_}"
msgstr "The folder {old_} has been renamed to {new_}"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\mapping.py:186
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\mapping.py:201
msgid "文件"
msgstr "file"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\mapping.py:191
#, python-brace-format
msgid "文件 {old_file} 重命名为 {new_file}"
msgstr "The file {old_file} has been renamed to {new_file}"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\mapping.py:210
#, python-brace-format
msgid "{type} {old}被占用，重命名失败: {error}"
msgstr "{type} {old} is occupied, renaming failed: {error}"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\mapping.py:219
#, python-brace-format
msgid "{type} {new}名称重复，重命名失败: {error}"
msgstr "{type} {new} already exists, renaming failed: {error}"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\mapping.py:228
#, python-brace-format
msgid "处理{type} {old}时发生预期之外的错误: {error}"
msgstr "An unexpected error occurred while processing {type} {old}: {error}"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\tools.py:31
msgid ""
"如需重新尝试处理该对象，请关闭所有正在访问该对象的窗口或程序，然后直接按下回"
"车键！\n"
"如需跳过处理该对象，请输入任意字符后按下回车键！"
msgstr ""
"If you want to retry processing this object, please close all windows or "
"programs currently accessing it, then press Enter directly!\n"
"If you want to skip processing this object, please enter any character and "
"then press Enter!"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:20
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:29
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\monitor.py:21
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:15
msgid "退出程序"
msgstr "Quit"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:21
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:30
msgid "检查更新"
msgstr "Update"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:22
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\record.py:35
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:16
msgid "返回首页"
msgstr "Return"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:35
msgid "如果 XHS-Downloader 对您有帮助，请考虑为它点个 Star，感谢您的支持！"
msgstr ""
"If XHS-Downloader is helpful to you, please consider giving it Star. Thank "
"you for your support!"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:42
msgid "Discord 社区"
msgstr "Discord Community"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:46
msgid "邀请链接："
msgstr "Invitation link: "

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:48
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:61
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:70
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:56
msgid "点击访问"
msgstr "Click to visit"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:51
msgid "作者的其他开源项目"
msgstr "Other open-source projects of the author"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\app.py:74
msgid ""
"配置文件 settings.json 缺少必要的参数，请删除该文件，然后重新运行程序，自动生"
"成默认配置文件！"
msgstr ""
"The configuration file settings.json is missing necessary parameters. Please "
"delete the file and run the program again to automatically generate the "
"default configuration file!"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:31
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:212
msgid "程序设置"
msgstr "Settings"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:32
msgid "下载记录"
msgstr "Record"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:33
msgid "开启监听"
msgstr "Monitor"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:34
msgid "关于项目"
msgstr "About"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:49
msgid "开源协议: "
msgstr "Open source protocol: "

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:52
msgid "项目地址: "
msgstr "Repository link: "

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:59
msgid "请输入小红书图文/视频作品链接"
msgstr "Please enter the link to the RedNote image or video works"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:62
msgid "多个链接之间使用空格分隔"
msgstr "Separate multiple links with spaces"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:64
msgid "下载无水印作品文件"
msgstr "Download images/video files"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:65
msgid "读取剪贴板"
msgstr "Read the clipboard"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:66
msgid "清空输入框"
msgstr "Clear the input box"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:81
msgid "免责声明\n"
msgstr ""
"Disclaimer about XHS-Downloader:\n"
"\n"
"1. The user decides on their own use of this project and assumes all risks. "
"The author is not responsible for any losses, liabilities, or risks incurred "
"by the user in using this project.\n"
"2. The code and features provided by the author of this project are "
"developed based on existing knowledge and technology. The author strives to "
"ensure the correctness and security of the code but does not guarantee that "
"the code is entirely free of errors or defects.\n"
"3. The user must strictly adhere to the requirements of the GNU General "
"Public License v3.0 when using this project and appropriately acknowledge "
"the use of code licensed under the GNU General Public License v3.0.\n"
"4. Under no circumstances may the user associate the author, contributors, "
"or other relevant parties of this project with the user's actions, nor "
"demand them to be held responsible for any losses or damages incurred by the "
"user in using this project.\n"
"5. The user must independently research relevant laws and regulations when "
"using the code and features of this project, ensuring that their use is "
"legal and compliant. Any legal responsibilities and risks arising from "
"violations of laws and regulations are the sole responsibility of the user.\n"
"6. The author of this project will not offer a paid version of the XHS-"
"Downloader project and will not provide any commercial services related to "
"the XHS-Downloader project.\n"
"7. Any secondary development, modification, or compilation of programs based "
"on this project is not associated with the original author. The original "
"author is not responsible for any consequences related to secondary "
"development actions or their results. The user is solely responsible for all "
"situations that may arise from secondary development.\n"
"\n"
"Before using the code and features of this project, please carefully "
"consider and accept the above disclaimers. If you have any questions or do "
"not agree with the statements above, please refrain from using the code and "
"features of this project. If you proceed to use the code and features of "
"this project, it will be considered that you fully understand and accept the "
"disclaimers mentioned above, and willingly assume all risks and consequences "
"associated with using this project.\n"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:94
msgid "未输入任何小红书作品链接"
msgstr "No RedNote works links provided"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:124
msgid "下载小红书作品文件失败"
msgstr "Failed to download the RedNote works files"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\loading.py:19
msgid "程序处理中..."
msgstr "Processing..."

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\monitor.py:22
msgid "关闭监听"
msgstr "Close"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\monitor.py:34
msgid "已启动监听剪贴板模式"
msgstr "Currently in monitoring clipboard mode"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\monitor.py:36
msgid "退出监听剪贴板模式"
msgstr "Exit monitoring clipboard mode"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\record.py:23
msgid "请输入待删除的小红书作品链接或作品 ID"
msgstr "Please enter the link or ID of the RedNote works to be deleted"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\record.py:26
msgid ""
"支持输入作品 ID 或包含作品 ID 的作品链接，多个链接或 ID 之间使用空格分隔"
msgstr ""
"Support input of works ID or links containing works ID, with multiple links "
"or IDs separated by spaces"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\record.py:32
msgid "删除指定作品 ID"
msgstr "Delete specified works ID"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\record.py:47
msgid "删除下载记录成功"
msgstr "Successfully deleted download record"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:35
msgid "程序根路径"
msgstr "Program root path"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:64
msgid "内置 Chrome User Agent"
msgstr "Chrome User Agent"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:69
msgid "小红书网页版 Cookie"
msgstr "RedNote Web Cookie"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:83
msgid "不使用代理"
msgstr "No proxy"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:120
msgid "记录作品详细数据"
msgstr "Record works data"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:125
msgid "作品归档保存模式"
msgstr "Works Archiving Mode"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:130
msgid "视频作品下载开关"
msgstr "Video download switch"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:135
msgid "图文作品下载开关"
msgstr "Image download switch"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:144
msgid "动图文件下载开关"
msgstr "LivePhoto download switch"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:154
msgid "作者归档保存模式"
msgstr "Author Archiving Mode"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:159
msgid "更新文件修改时间"
msgstr "Update File Modification Time"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:167
msgid "图片下载格式"
msgstr "Image download format"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:171
msgid "程序语言"
msgstr "Program language"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:194
msgid "保存配置"
msgstr "Save configuration"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:198
msgid "放弃更改"
msgstr "Discard changes"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:208
msgid "小红书网页版 Cookie，无需登录，参数已设置"
msgstr ""
"RedNote web version cookie, no login required, parameters have been set"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:209
msgid "小红书网页版 Cookie，无需登录，参数未设置"
msgstr "RedNote web version cookie, no login required, parameters not set"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\update.py:26
msgid "正在检查新版本，请稍等..."
msgstr "Checking for new version, please wait..."

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\update.py:46
#, python-brace-format
msgid "检测到新版本：{0}.{1}"
msgstr "Detected new version: {0} {1}"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\update.py:54
msgid "当前版本为开发版, 可更新至正式版"
msgstr "Detected a new official version"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\update.py:59
msgid "当前已是最新开发版"
msgstr "You are already using the latest development version"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\update.py:64
msgid "当前已是最新正式版"
msgstr "You are already using the latest official version"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\update.py:71
msgid "检测新版本失败"
msgstr "Failed to check for a new version"
