using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core.Platforms
{
    /// <summary>
    /// 小红书平台下载器 - 基于XHS-Downloader移植
    /// </summary>
    public class XiaohongshuDownloader : IPlatformDownloader, IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly XiaohongshuApi _api;

        public string PlatformName => "小红书";
        public string PlatformIcon => "🌹";
        public bool RequiresLogin => false;

        public string[] SupportedUrlPatterns => new[]
        {
            @"https?://www\.xiaohongshu\.com/explore/[a-zA-Z0-9]+",
            @"https?://www\.xiaohongshu\.com/discovery/item/[a-zA-Z0-9]+",
            @"https?://xhslink\.com/[a-zA-Z0-9]+",
            @"https?://www\.xiaohongshu\.com/user/profile/[a-zA-Z0-9]+/[a-zA-Z0-9]+",
            @"http://xhslink\.com/[a-zA-Z0-9]+"
        };

        public XiaohongshuDownloader()
        {
            // 创建支持自动解压缩的HttpClient
            var handler = new HttpClientHandler()
            {
                AutomaticDecompression = System.Net.DecompressionMethods.GZip | 
                                       System.Net.DecompressionMethods.Deflate | 
                                       System.Net.DecompressionMethods.Brotli
            };

            _httpClient = new HttpClient(handler);
            _api = new XiaohongshuApi(_httpClient);
            SetupHttpClient();
        }

        private void SetupHttpClient()
        {
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            _httpClient.DefaultRequestHeaders.Add("Accept",
                "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate, br");
            _httpClient.DefaultRequestHeaders.Add("Cache-Control", "no-cache");
            _httpClient.DefaultRequestHeaders.Add("Pragma", "no-cache");
            _httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Dest", "document");
            _httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Mode", "navigate");
            _httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Site", "none");
            _httpClient.DefaultRequestHeaders.Add("Sec-Fetch-User", "?1");
            _httpClient.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");
        }

        public bool IsUrlSupported(string url)
        {
            if (string.IsNullOrEmpty(url))
                return false;

            return SupportedUrlPatterns.Any(pattern => Regex.IsMatch(url, pattern, RegexOptions.IgnoreCase));
        }

        public async Task<VideoInfo?> ParseVideoInfoAsync(string url, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"开始解析小红书链接: {url}");

                // 提取链接
                var links = await _api.ExtractLinksAsync(url);
                if (links.Count == 0)
                {
                    Logger.Instance.Error("无法从输入中提取有效的小红书链接");
                    return null;
                }

                var targetUrl = links[0];
                Logger.Instance.Info($"处理链接: {targetUrl}");

                // 提取作品ID
                var noteId = _api.ExtractNoteId(targetUrl);
                if (string.IsNullOrEmpty(noteId))
                {
                    Logger.Instance.Error("无法从URL中提取作品ID");
                    return null;
                }

                Logger.Instance.Info($"提取到作品ID: {noteId}");

                // 获取作品详细信息
                var noteDetail = await _api.GetNoteDetailAsync(noteId, cookie);
                if (noteDetail == null)
                {
                    Logger.Instance.Error("获取作品详细信息失败");
                    return null;
                }

                // 转换为VideoInfo对象
                var videoInfo = ConvertToVideoInfo(noteDetail, targetUrl);
                Logger.Instance.Info($"成功解析作品: {videoInfo.Title}");

                return videoInfo;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"解析小红书链接失败: {ex.Message}");
                return null;
            }
        }

        public async Task<List<DownloadUrl>> GetDownloadUrlsAsync(VideoInfo videoInfo, string quality, string? cookie = null)
        {
            try
            {
                var downloadUrls = new List<DownloadUrl>();

                // 根据作品类型获取下载链接
                if (videoInfo.Platform == "小红书")
                {
                    var noteId = videoInfo.VideoId;
                    var noteDetail = await _api.GetNoteDetailAsync(noteId, cookie);
                    
                    if (noteDetail == null)
                    {
                        Logger.Instance.Error("获取作品详细信息失败");
                        return downloadUrls;
                    }

                    // 处理视频类型
                    if (noteDetail.Type == "video" && noteDetail.VideoUrls?.Count > 0)
                    {
                        var videoUrl = SelectBestVideoQuality(noteDetail.VideoUrls, quality);
                        if (videoUrl != null)
                        {
                            downloadUrls.Add(new DownloadUrl
                            {
                                Url = videoUrl.Url,
                                Quality = videoUrl.Quality,
                                Format = "mp4",
                                FileSize = videoUrl.Size,
                                Headers = GetDownloadHeaders()
                            });
                        }
                    }
                    // 处理图文类型
                    else if (noteDetail.Type == "normal" && noteDetail.ImageUrls?.Count > 0)
                    {
                        for (int i = 0; i < noteDetail.ImageUrls.Count; i++)
                        {
                            var imageUrl = noteDetail.ImageUrls[i];
                            downloadUrls.Add(new DownloadUrl
                            {
                                Url = imageUrl,
                                Quality = $"图片{i + 1}",
                                Format = "png",
                                FileSize = 0, // 图片大小需要实际下载时获取
                                Headers = GetDownloadHeaders()
                            });
                        }
                    }
                }

                Logger.Instance.Info($"获取到 {downloadUrls.Count} 个下载链接");
                return downloadUrls;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取下载链接失败: {ex.Message}");
                return new List<DownloadUrl>();
            }
        }

        public async Task<List<QualityOption>> GetAvailableQualitiesAsync(VideoInfo videoInfo, string? cookie = null)
        {
            var qualities = new List<QualityOption>();

            try
            {
                if (videoInfo.Platform == "小红书")
                {
                    var noteId = videoInfo.VideoId;
                    var noteDetail = await _api.GetNoteDetailAsync(noteId, cookie);
                    
                    if (noteDetail?.Type == "video" && noteDetail.VideoUrls?.Count > 0)
                    {
                        // 视频类型：提供不同清晰度选项
                        var uniqueQualities = noteDetail.VideoUrls
                            .GroupBy(v => v.Quality)
                            .Select(g => g.First())
                            .OrderByDescending(v => GetQualityPriority(v.Quality));

                        foreach (var video in uniqueQualities)
                        {
                            qualities.Add(new QualityOption
                            {
                                Quality = video.Quality,
                                Description = GetQualityDescription(video.Quality),
                                Width = video.Width,
                                Height = video.Height,
                                Format = "mp4"
                            });
                        }
                    }
                    else if (noteDetail?.Type == "normal")
                    {
                        // 图文类型：只有一个选项
                        qualities.Add(new QualityOption
                        {
                            Quality = "原图",
                            Description = "原始图片",
                            Width = 0,
                            Height = 0,
                            Format = "png"
                        });
                    }
                }

                if (qualities.Count == 0)
                {
                    // 默认选项
                    qualities.Add(new QualityOption
                    {
                        Quality = "默认",
                        Description = "默认质量",
                        Width = 0,
                        Height = 0,
                        Format = "mp4"
                    });
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取清晰度选项失败: {ex.Message}");
            }

            return qualities;
        }

        public async Task<bool> ValidateAccountAsync(string cookie)
        {
            try
            {
                // 小红书不需要登录即可获取大部分内容
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"验证账号失败: {ex.Message}");
                return false;
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 将小红书作品信息转换为VideoInfo对象
        /// </summary>
        private VideoInfo ConvertToVideoInfo(XiaohongshuNoteDetail noteDetail, string originalUrl)
        {
            var videoInfo = new VideoInfo
            {
                VideoId = noteDetail.NoteId,
                Title = noteDetail.Title ?? "无标题",
                Description = noteDetail.Description ?? "",
                Author = noteDetail.User?.Nickname ?? "未知作者",
                AuthorId = noteDetail.User?.UserId ?? "",
                PublishTime = noteDetail.CreateTime,
                Duration = noteDetail.Type == "video" ? noteDetail.Duration : 0,
                CoverUrl = noteDetail.CoverUrl ?? "",
                OriginalUrl = originalUrl,
                Platform = "小红书",
                ViewCount = noteDetail.ViewCount,
                Url = originalUrl,

                // 小红书特有字段
                ContentType = noteDetail.Type ?? "",
                LikeCount = noteDetail.LikeCount ?? "",
                CollectCount = noteDetail.CollectCount ?? "",
                CommentCount = noteDetail.CommentCount ?? "",
                ShareCount = noteDetail.ShareCount ?? ""
            };

            // 设置图片URL列表
            if (noteDetail.ImageUrls != null)
            {
                videoInfo.ImageUrls.AddRange(noteDetail.ImageUrls);
            }

            // 设置标签
            if (noteDetail.Tags != null)
            {
                videoInfo.Tags.AddRange(noteDetail.Tags);
            }

            // 设置作品类型相关信息
            if (noteDetail.Type == "video")
            {
                // 视频类型
                videoInfo.IsCollection = false;
            }
            else if (noteDetail.Type == "normal")
            {
                // 图文类型
                videoInfo.IsCollection = noteDetail.ImageUrls?.Count > 1;
            }

            return videoInfo;
        }

        /// <summary>
        /// 选择最佳视频质量
        /// </summary>
        private XiaohongshuVideoUrl? SelectBestVideoQuality(List<XiaohongshuVideoUrl> videoUrls, string requestedQuality)
        {
            if (videoUrls == null || videoUrls.Count == 0)
                return null;

            // 根据请求的质量选择
            var targetVideo = videoUrls.FirstOrDefault(v => v.Quality.Equals(requestedQuality, StringComparison.OrdinalIgnoreCase));
            if (targetVideo != null)
                return targetVideo;

            // 如果没有找到指定质量，选择最高质量
            return videoUrls.OrderByDescending(v => GetQualityPriority(v.Quality)).First();
        }

        /// <summary>
        /// 获取质量优先级（数值越大优先级越高）
        /// </summary>
        private int GetQualityPriority(string quality)
        {
            return quality?.ToLower() switch
            {
                "原画" or "1080p" or "hd" => 4,
                "720p" or "sd" => 3,
                "480p" => 2,
                "360p" => 1,
                _ => 0
            };
        }

        /// <summary>
        /// 获取质量描述
        /// </summary>
        private string GetQualityDescription(string quality)
        {
            return quality?.ToLower() switch
            {
                "原画" => "原画质量",
                "1080p" or "hd" => "高清 1080P",
                "720p" or "sd" => "标清 720P",
                "480p" => "流畅 480P",
                "360p" => "极速 360P",
                _ => quality ?? "未知质量"
            };
        }

        /// <summary>
        /// 获取下载请求头
        /// </summary>
        private Dictionary<string, string> GetDownloadHeaders()
        {
            return new Dictionary<string, string>
            {
                ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                ["Referer"] = "https://www.xiaohongshu.com/",
                ["Accept"] = "*/*",
                ["Accept-Language"] = "zh-CN,zh;q=0.9,en;q=0.8",
                ["Accept-Encoding"] = "gzip, deflate, br",
                ["Cache-Control"] = "no-cache",
                ["Pragma"] = "no-cache"
            };
        }

        #endregion

        public void Dispose()
        {
            _httpClient?.Dispose();
            _api?.Dispose();
        }
    }
}
