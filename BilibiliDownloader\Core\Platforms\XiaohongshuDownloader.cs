using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using BilibiliDownloader.Core;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core.Platforms
{
    /// <summary>
    /// 小红书下载器 - 2025年最新版本
    /// 基于最新的小红书API和技术方案
    /// </summary>
    public class XiaohongshuDownloader : IPlatformDownloader
    {
        private readonly HttpClient _httpClient;
        private readonly string _userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";

        // 2025年最新的小红书API端点
        private const string API_BASE = "https://edith.xiaohongshu.com";
        private const string WEB_BASE = "https://www.xiaohongshu.com";

        public XiaohongshuDownloader()
        {
            var handler = new HttpClientHandler()
            {
                UseCookies = false
            };

            _httpClient = new HttpClient(handler);
            _httpClient.DefaultRequestHeaders.Add("User-Agent", _userAgent);
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json, text/plain, */*");
            _httpClient.DefaultRequestHeaders.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate, br");
            _httpClient.DefaultRequestHeaders.Add("Origin", WEB_BASE);
            _httpClient.DefaultRequestHeaders.Add("Referer", WEB_BASE);
            _httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Dest", "empty");
            _httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Mode", "cors");
            _httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Site", "same-site");
        }

        public string PlatformName => "小红书";

        public string PlatformIcon => "🌹";

        public string[] SupportedUrlPatterns => new[]
        {
            @"xiaohongshu\.com/explore/[a-zA-Z0-9]+",
            @"xiaohongshu\.com/discovery/item/[a-zA-Z0-9]+",
            @"xhslink\.com/[a-zA-Z0-9]+",
            @"xiaohongshu\.com/user/profile/[a-zA-Z0-9]+",
            @"xiaohongshu\.com/.*note_id=([a-zA-Z0-9]+)"
        };

        public bool RequiresLogin => false;

        public bool IsUrlSupported(string url)
        {
            if (string.IsNullOrEmpty(url)) return false;

            return Array.Exists(SupportedUrlPatterns, pattern => Regex.IsMatch(url, pattern, RegexOptions.IgnoreCase));
        }

        public async Task<VideoInfo?> ParseVideoInfoAsync(string url, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"开始解析小红书链接: {url}");

                // 提取笔记ID
                string? noteId = ExtractNoteId(url);
                if (string.IsNullOrEmpty(noteId))
                {
                    throw new Exception("无法从链接中提取笔记ID");
                }

                Logger.Instance.Info($"提取到笔记ID: {noteId}");

                // 获取笔记详情
                var noteInfo = await GetNoteDetailAsync(noteId);

                return new VideoInfo
                {
                    Title = noteInfo.Title,
                    Author = noteInfo.Author,
                    Duration = 0, // 小红书可能是图片或短视频
                    Url = noteInfo.VideoUrl ?? "",
                    OriginalUrl = url,
                    CoverUrl = noteInfo.CoverUrl ?? "",
                    Description = noteInfo.Description,
                    PublishTime = noteInfo.PublishDate,
                    ViewCount = noteInfo.ViewCount,
                    LikeCount = noteInfo.LikeCount.ToString(),
                    Platform = "小红书",
                    ContentType = !string.IsNullOrEmpty(noteInfo.VideoUrl) ? "video" : "normal"
                };
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"解析小红书视频信息失败: {ex.Message}");
                return null;
            }
        }

        public async Task<List<DownloadUrl>> GetDownloadUrlsAsync(VideoInfo videoInfo, string quality, string? cookie = null)
        {
            var downloadUrls = new List<DownloadUrl>();

            try
            {
                // 如果有视频链接
                if (!string.IsNullOrEmpty(videoInfo.Url))
                {
                    downloadUrls.Add(new DownloadUrl
                    {
                        Url = videoInfo.Url,
                        Quality = quality,
                        Format = "mp4",
                        FileSize = 0, // 无法预先获取文件大小
                        Headers = new Dictionary<string, string>
                        {
                            ["User-Agent"] = _userAgent,
                            ["Referer"] = WEB_BASE
                        }
                    });
                }

                // 如果有封面图片
                if (!string.IsNullOrEmpty(videoInfo.CoverUrl))
                {
                    downloadUrls.Add(new DownloadUrl
                    {
                        Url = videoInfo.CoverUrl,
                        Quality = "原图",
                        Format = "jpg",
                        FileSize = 0,
                        Headers = new Dictionary<string, string>
                        {
                            ["User-Agent"] = _userAgent,
                            ["Referer"] = WEB_BASE
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取下载链接失败: {ex.Message}");
            }

            return downloadUrls;
        }

        public async Task<List<QualityOption>> GetAvailableQualitiesAsync(VideoInfo videoInfo, string? cookie = null)
        {
            var qualities = new List<QualityOption>();

            // 小红书通常只有一种质量
            if (!string.IsNullOrEmpty(videoInfo.Url))
            {
                qualities.Add(new QualityOption
                {
                    Quality = "高清",
                    Description = "小红书原始质量",
                    Width = 720,
                    Height = 1280,
                    Format = "mp4"
                });
            }

            return await Task.FromResult(qualities);
        }

        public async Task<bool> ValidateAccountAsync(string cookie)
        {
            // 小红书不需要登录，直接返回true
            return await Task.FromResult(true);
        }



        private string ExtractNoteId(string url)
        {
            // 处理各种小红书链接格式
            var patterns = new[]
            {
                @"explore/([a-zA-Z0-9]+)",
                @"discovery/item/([a-zA-Z0-9]+)", 
                @"xhslink\.com/([a-zA-Z0-9]+)",
                @"note_id=([a-zA-Z0-9]+)"
            };
            
            foreach (var pattern in patterns)
            {
                var match = Regex.Match(url, pattern, RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }
            
            return null;
        }

        private async Task<XhsNoteInfo> GetNoteDetailAsync(string noteId)
        {
            try
            {
                // 2025年最新方案：使用网页版接口，避免复杂的签名算法
                var webUrl = $"{WEB_BASE}/explore/{noteId}";

                Logger.Instance.Info($"访问网页版链接: {webUrl}");

                // 设置请求头模拟真实浏览器
                var request = new HttpRequestMessage(HttpMethod.Get, webUrl);
                request.Headers.Add("User-Agent", _userAgent);
                request.Headers.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
                request.Headers.Add("Accept-Language", "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2");
                request.Headers.Add("Accept-Encoding", "gzip, deflate, br");
                request.Headers.Add("DNT", "1");
                request.Headers.Add("Connection", "keep-alive");
                request.Headers.Add("Upgrade-Insecure-Requests", "1");
                request.Headers.Add("Sec-Fetch-Dest", "document");
                request.Headers.Add("Sec-Fetch-Mode", "navigate");
                request.Headers.Add("Sec-Fetch-Site", "none");
                request.Headers.Add("Cache-Control", "max-age=0");

                var response = await _httpClient.SendAsync(request);
                var html = await response.Content.ReadAsStringAsync();

                Logger.Instance.Info($"网页响应长度: {html.Length}");

                // 从HTML中提取数据
                return ParseHtmlContent(html, noteId);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取笔记详情失败: {ex.Message}");
                throw;
            }
        }

        private XhsNoteInfo ParseHtmlContent(string html, string noteId)
        {
            try
            {
                // 从HTML中提取JSON数据
                var jsonMatch = Regex.Match(html, @"window\.__INITIAL_STATE__\s*=\s*({.*?});", RegexOptions.Singleline);
                if (!jsonMatch.Success)
                {
                    // 尝试其他模式
                    jsonMatch = Regex.Match(html, @"window\.__INITIAL_SSR_STATE__\s*=\s*({.*?});", RegexOptions.Singleline);
                }

                if (!jsonMatch.Success)
                {
                    throw new Exception("无法从HTML中提取数据");
                }

                var jsonData = jsonMatch.Groups[1].Value;
                Logger.Instance.Info($"提取到JSON数据长度: {jsonData.Length}");

                using var document = JsonDocument.Parse(jsonData);
                var root = document.RootElement;

                // 查找笔记数据
                var noteData = FindNoteInJson(root, noteId);
                if (noteData == null)
                {
                    throw new Exception("未找到笔记数据");
                }

                return ParseNoteFromJson(noteData.Value);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"解析HTML内容失败: {ex.Message}");
                // 返回基础信息
                return new XhsNoteInfo
                {
                    Title = "解析失败",
                    Author = "未知",
                    Description = "无法解析笔记内容",
                    VideoUrl = null,
                    CoverUrl = null,
                    PublishDate = DateTime.Now,
                    ViewCount = 0,
                    LikeCount = 0
                };
            }
        }

        private JsonElement? FindNoteInJson(JsonElement root, string noteId)
        {
            try
            {
                // 在不同的路径中查找笔记数据
                var searchPaths = new[]
                {
                    "note.noteDetailMap",
                    "note.noteDetail",
                    "noteDetail",
                    "feed.noteDetailMap"
                };

                foreach (var path in searchPaths)
                {
                    var current = root;
                    var parts = path.Split('.');

                    foreach (var part in parts)
                    {
                        if (!current.TryGetProperty(part, out current))
                        {
                            break;
                        }
                    }

                    if (current.ValueKind == JsonValueKind.Object)
                    {
                        if (current.TryGetProperty(noteId, out var noteData))
                        {
                            return noteData;
                        }

                        // 如果是数组，遍历查找
                        foreach (var property in current.EnumerateObject())
                        {
                            if (property.Value.ValueKind == JsonValueKind.Object &&
                                property.Value.TryGetProperty("noteId", out var idProp) &&
                                idProp.GetString() == noteId)
                            {
                                return property.Value;
                            }
                        }
                    }
                }

                return null;
            }
            catch
            {
                return null;
            }
        }



        private XhsNoteInfo ParseNoteFromJson(JsonElement noteData)
        {
            try
            {
                var title = "未知标题";
                var author = "未知作者";
                var description = "";
                string? videoUrl = null;
                string? coverUrl = null;

                // 尝试不同的数据结构
                if (noteData.TryGetProperty("title", out var titleProp))
                {
                    title = titleProp.GetString() ?? title;
                }
                else if (noteData.TryGetProperty("display_title", out var displayTitleProp))
                {
                    title = displayTitleProp.GetString() ?? title;
                }

                if (noteData.TryGetProperty("user", out var userProp))
                {
                    if (userProp.TryGetProperty("nickname", out var nicknameProp))
                    {
                        author = nicknameProp.GetString() ?? author;
                    }
                    else if (userProp.TryGetProperty("name", out var nameProp))
                    {
                        author = nameProp.GetString() ?? author;
                    }
                }

                if (noteData.TryGetProperty("desc", out var descProp))
                {
                    description = descProp.GetString() ?? "";
                }
                else if (noteData.TryGetProperty("description", out var descriptionProp))
                {
                    description = descriptionProp.GetString() ?? "";
                }

                // 查找视频URL
                if (noteData.TryGetProperty("video", out var videoProp))
                {
                    if (videoProp.TryGetProperty("media", out var mediaProp) &&
                        mediaProp.TryGetProperty("stream", out var streamProp) &&
                        streamProp.TryGetProperty("h264", out var h264Prop) &&
                        h264Prop.GetArrayLength() > 0)
                    {
                        var videoInfo = h264Prop[0];
                        if (videoInfo.TryGetProperty("master_url", out var masterUrlProp))
                        {
                            videoUrl = masterUrlProp.GetString();
                        }
                        else if (videoInfo.TryGetProperty("backup_urls", out var backupUrlsProp) &&
                                backupUrlsProp.GetArrayLength() > 0)
                        {
                            videoUrl = backupUrlsProp[0].GetString();
                        }
                    }
                }

                // 查找封面图片
                if (noteData.TryGetProperty("image_list", out var imageListProp) &&
                    imageListProp.GetArrayLength() > 0)
                {
                    var firstImage = imageListProp[0];
                    if (firstImage.TryGetProperty("url_default", out var urlDefaultProp))
                    {
                        coverUrl = urlDefaultProp.GetString();
                    }
                    else if (firstImage.TryGetProperty("url", out var urlProp))
                    {
                        coverUrl = urlProp.GetString();
                    }
                }
                else if (noteData.TryGetProperty("cover", out var coverProp))
                {
                    if (coverProp.TryGetProperty("url", out var coverUrlProp))
                    {
                        coverUrl = coverUrlProp.GetString();
                    }
                }

                return new XhsNoteInfo
                {
                    Title = title,
                    Author = author,
                    Description = description,
                    VideoUrl = videoUrl,
                    CoverUrl = coverUrl,
                    PublishDate = DateTime.Now,
                    ViewCount = 0,
                    LikeCount = 0
                };
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"解析笔记JSON失败: {ex.Message}");
                return new XhsNoteInfo
                {
                    Title = "解析失败",
                    Author = "未知",
                    Description = "无法解析笔记内容",
                    VideoUrl = null,
                    CoverUrl = null,
                    PublishDate = DateTime.Now,
                    ViewCount = 0,
                    LikeCount = 0
                };
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// 小红书笔记信息模型
    /// </summary>
    public class XhsNoteInfo
    {
        public string Title { get; set; } = "";
        public string Author { get; set; } = "";
        public string Description { get; set; } = "";
        public string? VideoUrl { get; set; }
        public string? CoverUrl { get; set; }
        public DateTime PublishDate { get; set; }
        public long ViewCount { get; set; }
        public long LikeCount { get; set; }
    }
}
