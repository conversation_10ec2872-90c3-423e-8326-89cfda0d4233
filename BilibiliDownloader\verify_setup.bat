@echo off
chcp 65001 >nul
echo ========================================
echo Bilibili下载器 - 环境验证
echo ========================================
echo.

echo 1. 检查 .NET SDK...
dotnet --version
if errorlevel 1 (
    echo ❌ .NET SDK 未安装或不在PATH中
    echo 请从 https://dotnet.microsoft.com/download 下载安装
    pause
    exit /b 1
) else (
    echo ✅ .NET SDK 已安装
)
echo.

echo 2. 检查当前目录...
echo 当前目录: %CD%
if exist "BilibiliDownloader.csproj" (
    echo ✅ 找到项目文件: BilibiliDownloader.csproj
) else (
    echo ❌ 未找到项目文件
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)
echo.

echo 3. 检查主要源文件...
set "files=MainWindow.xaml MainWindow.xaml.cs App.xaml App.xaml.cs"
for %%f in (%files%) do (
    if exist "%%f" (
        echo ✅ %%f
    ) else (
        echo ❌ %%f
    )
)
echo.

echo 4. 检查依赖工具...
if exist "BBDown.exe" (
    echo ✅ BBDown.exe
) else (
    echo ⚠️  BBDown.exe 未找到（程序仍可运行，但番剧下载功能受限）
)

if exist "ffmpeg.exe" (
    echo ✅ ffmpeg.exe
) else (
    echo ⚠️  ffmpeg.exe 未找到（程序仍可运行，但视频处理功能受限）
)
echo.

echo 5. 测试项目编译...
echo 正在编译项目...
dotnet build --verbosity quiet
if errorlevel 1 (
    echo ❌ 编译失败
    echo 详细错误信息：
    dotnet build
    pause
    exit /b 1
) else (
    echo ✅ 编译成功
)
echo.

echo ========================================
echo 🎉 环境验证完成！
echo ========================================
echo.
echo 现在您可以使用以下命令运行程序：
echo.
echo   dotnet run
echo.
echo 或者双击 run.bat 脚本
echo.
echo 如果要在后台运行：
echo   start dotnet run
echo.
pause
