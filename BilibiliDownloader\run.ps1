# Bilibili下载器启动脚本
Write-Host "正在启动Bilibili下载器..." -ForegroundColor Green
Write-Host ""

Write-Host "1. 清理项目..." -ForegroundColor Yellow
dotnet clean
if ($LASTEXITCODE -ne 0) {
    Write-Host "清理失败!" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit
}

Write-Host ""
Write-Host "2. 恢复依赖..." -ForegroundColor Yellow
dotnet restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "依赖恢复失败!" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit
}

Write-Host ""
Write-Host "3. 编译项目..." -ForegroundColor Yellow
dotnet build
if ($LASTEXITCODE -ne 0) {
    Write-Host "编译失败!" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit
}

Write-Host ""
Write-Host "4. 运行程序..." -ForegroundColor Green
dotnet run

Write-Host ""
Write-Host "程序已退出" -ForegroundColor Blue
Read-Host "按任意键退出"
