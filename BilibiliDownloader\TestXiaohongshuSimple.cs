using System;
using System.IO;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace BilibiliDownloader
{
    /// <summary>
    /// 简单的小红书测试程序
    /// </summary>
    class TestXiaohongshuSimple
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("小红书简单测试");
            Console.WriteLine("====================");
            
            var testUrl = "https://www.xiaohongshu.com/explore/688c586300000000030254b9?xsec_token=ABx9u-0ZCAvSiYihqcnzoadhTRPT-LLfK4UKYaBjofpL4=&xsec_source=pc_cfeed";
            
            try
            {
                Console.WriteLine($"正在获取页面: {testUrl}");
                
                using var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Add("User-Agent", 
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
                httpClient.DefaultRequestHeaders.Add("Accept", 
                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8");
                httpClient.DefaultRequestHeaders.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
                httpClient.DefaultRequestHeaders.Add("Cache-Control", "no-cache");
                
                var response = await httpClient.GetAsync(testUrl);
                response.EnsureSuccessStatusCode();
                
                var html = await response.Content.ReadAsStringAsync();
                
                Console.WriteLine($"页面内容长度: {html.Length}");
                
                // 查找script标签
                var scriptStart = html.IndexOf("window.__INITIAL_STATE__");
                if (scriptStart >= 0)
                {
                    Console.WriteLine($"找到 window.__INITIAL_STATE__ 位置: {scriptStart}");
                    
                    // 提取JSON部分
                    var jsonStart = html.IndexOf("window.__INITIAL_STATE__=") + "window.__INITIAL_STATE__=".Length;
                    var jsonContent = html.Substring(jsonStart);

                    // 查找JSON结束位置
                    var jsonEnd = FindJsonEnd(jsonContent);
                    jsonContent = jsonContent.Substring(0, jsonEnd);

                    // 清理JSON
                    jsonContent = CleanJsonData(jsonContent);

                    // 确保JSON以{开头
                    if (!jsonContent.TrimStart().StartsWith("{"))
                    {
                        jsonContent = "{" + jsonContent;
                        Console.WriteLine("添加了缺失的开头大括号");
                    }
                    
                    Console.WriteLine($"JSON数据长度: {jsonContent.Length}");
                    Console.WriteLine($"JSON开头: {jsonContent.Substring(0, Math.Min(200, jsonContent.Length))}");
                    Console.WriteLine($"JSON结尾: {jsonContent.Substring(Math.Max(0, jsonContent.Length - 200))}");
                    
                    // 保存JSON内容用于调试
                    var jsonFileName = "xiaohongshu_cleaned.json";
                    await File.WriteAllTextAsync(jsonFileName, jsonContent);
                    Console.WriteLine($"清理后的JSON数据已保存到: {jsonFileName}");
                    
                    // 尝试解析JSON
                    try
                    {
                        using var document = System.Text.Json.JsonDocument.Parse(jsonContent);
                        Console.WriteLine("✅ JSON解析成功！");

                        var root = document.RootElement;
                        if (root.TryGetProperty("note", out var noteElement))
                        {
                            Console.WriteLine("✅ 找到note属性");
                            if (noteElement.TryGetProperty("noteDetailMap", out var noteDetailMap))
                            {
                                Console.WriteLine("✅ 找到noteDetailMap属性");
                                Console.WriteLine($"noteDetailMap类型: {noteDetailMap.ValueKind}");

                                if (noteDetailMap.ValueKind == System.Text.Json.JsonValueKind.Object)
                                {
                                    var propertyCount = 0;
                                    foreach (var property in noteDetailMap.EnumerateObject())
                                    {
                                        propertyCount++;
                                        Console.WriteLine($"noteDetailMap属性 {propertyCount}: '{property.Name}', 值类型: {property.Value.ValueKind}");

                                        if (property.Value.ValueKind == System.Text.Json.JsonValueKind.Object)
                                        {
                                            // 检查这个对象是否包含note数据
                                            if (property.Value.TryGetProperty("note", out var noteData))
                                            {
                                                Console.WriteLine($"  在属性 '{property.Name}' 中找到note数据");
                                                AnalyzeNoteData(noteData);
                                            }
                                            else
                                            {
                                                // 检查这个对象本身是否就是note数据
                                                var hasTitle = property.Value.TryGetProperty("title", out _);
                                                var hasDesc = property.Value.TryGetProperty("desc", out _);
                                                var hasNoteId = property.Value.TryGetProperty("noteId", out _);
                                                var hasImageList = property.Value.TryGetProperty("imageList", out _);
                                                var hasVideo = property.Value.TryGetProperty("video", out _);
                                                var hasType = property.Value.TryGetProperty("type", out _);

                                                Console.WriteLine($"  字段检查 - title:{hasTitle}, desc:{hasDesc}, noteId:{hasNoteId}, imageList:{hasImageList}, video:{hasVideo}, type:{hasType}");

                                                if (hasTitle || hasDesc || hasNoteId || hasImageList || hasVideo || hasType)
                                                {
                                                    Console.WriteLine($"  属性 '{property.Name}' 本身就是note数据");
                                                    AnalyzeNoteData(property.Value);
                                                }
                                            }
                                        }
                                    }
                                    Console.WriteLine($"noteDetailMap总共有 {propertyCount} 个属性");
                                }
                                else if (noteDetailMap.ValueKind == System.Text.Json.JsonValueKind.Array)
                                {
                                    Console.WriteLine($"noteDetailMap是数组，包含 {noteDetailMap.GetArrayLength()} 个项目");
                                }
                            }
                        }
                    }
                    catch (System.Text.Json.JsonException ex)
                    {
                        Console.WriteLine($"❌ JSON解析失败: {ex.Message}");
                        Console.WriteLine($"错误位置: Line {ex.LineNumber}, Position {ex.BytePositionInLine}");

                        if (ex.BytePositionInLine.HasValue && ex.BytePositionInLine.Value < jsonContent.Length)
                        {
                            var errorPos = (int)ex.BytePositionInLine.Value;
                            var start = Math.Max(0, errorPos - 50);
                            var length = Math.Min(100, jsonContent.Length - start);
                            var context = jsonContent.Substring(start, length);
                            Console.WriteLine($"错误位置附近内容: ...{context}...");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("❌ 未找到 window.__INITIAL_STATE__");
                }
                
                Console.WriteLine("\n测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中出现错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        private static void AnalyzeNoteData(System.Text.Json.JsonElement noteData)
        {
            try
            {
                Console.WriteLine("    分析note数据结构:");

                // 基本信息
                if (noteData.TryGetProperty("noteId", out var noteIdElement))
                    Console.WriteLine($"      noteId: {noteIdElement.GetString()}");

                if (noteData.TryGetProperty("title", out var titleElement))
                    Console.WriteLine($"      title: {titleElement.GetString()}");

                if (noteData.TryGetProperty("desc", out var descElement))
                {
                    var desc = descElement.GetString();
                    Console.WriteLine($"      desc: {(desc?.Length > 50 ? desc.Substring(0, 50) + "..." : desc)}");
                }

                if (noteData.TryGetProperty("type", out var typeElement))
                    Console.WriteLine($"      type: {typeElement.GetString()}");

                // 检查媒体内容
                if (noteData.TryGetProperty("imageList", out var imageListElement) &&
                    imageListElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                {
                    Console.WriteLine($"      imageList: 包含 {imageListElement.GetArrayLength()} 个图片");

                    int imageCount = 0;
                    foreach (var image in imageListElement.EnumerateArray())
                    {
                        imageCount++;
                        if (imageCount <= 3) // 只显示前3个
                        {
                            if (image.TryGetProperty("urlDefault", out var urlElement))
                            {
                                Console.WriteLine($"        图片{imageCount}: {urlElement.GetString()}");
                            }
                        }
                    }
                }

                if (noteData.TryGetProperty("video", out var videoElement))
                {
                    Console.WriteLine($"      video: 找到视频数据");

                    if (videoElement.TryGetProperty("consumer", out var consumerElement))
                    {
                        Console.WriteLine($"        consumer: 找到consumer数据");

                        if (consumerElement.TryGetProperty("originVideoKey", out var videoKeyElement))
                        {
                            var videoKey = videoKeyElement.GetString();
                            Console.WriteLine($"        originVideoKey: {videoKey}");

                            if (!string.IsNullOrEmpty(videoKey))
                            {
                                var videoUrl = $"https://sns-video-bd.xhscdn.com/{videoKey}";
                                Console.WriteLine($"        构造的视频URL: {videoUrl}");
                            }
                        }
                        else
                        {
                            Console.WriteLine($"        ❌ 未找到originVideoKey");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"        ❌ 未找到consumer");
                    }
                }

                // 用户信息
                if (noteData.TryGetProperty("user", out var userElement))
                {
                    if (userElement.TryGetProperty("nickname", out var nicknameElement))
                        Console.WriteLine($"      用户: {nicknameElement.GetString()}");
                }

                // 互动信息
                if (noteData.TryGetProperty("interactInfo", out var interactElement))
                {
                    if (interactElement.TryGetProperty("likedCount", out var likedElement))
                        Console.WriteLine($"      点赞数: {likedElement.GetString()}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    分析note数据失败: {ex.Message}");
            }
        }

        private static int FindJsonEnd(string jsonData)
        {
            int braceCount = 0;
            bool inString = false;
            bool escaped = false;
            
            for (int i = 0; i < jsonData.Length; i++)
            {
                char c = jsonData[i];
                
                if (escaped)
                {
                    escaped = false;
                    continue;
                }
                
                if (c == '\\' && inString)
                {
                    escaped = true;
                    continue;
                }
                
                if (c == '"')
                {
                    inString = !inString;
                    continue;
                }
                
                if (!inString)
                {
                    if (c == '{')
                    {
                        braceCount++;
                    }
                    else if (c == '}')
                    {
                        braceCount--;
                        if (braceCount == 0)
                        {
                            return i + 1;
                        }
                    }
                }
            }
            
            // 如果没有找到完整的JSON，尝试查找分号或其他结束标记
            var endMarkers = new[] { ";</script>", ";(function", ";window.", ";document.", ";\n", ";var ", ";function", ";undefined" };
            int minEnd = jsonData.Length;
            
            foreach (var marker in endMarkers)
            {
                var index = jsonData.IndexOf(marker);
                if (index > 0 && index < minEnd)
                {
                    minEnd = index;
                }
            }
            
            return minEnd;
        }

        private static string CleanJsonData(string jsonData)
        {
            try
            {
                // 移除可能的BOM标记
                if (jsonData.StartsWith("\uFEFF"))
                {
                    jsonData = jsonData.Substring(1);
                }

                // 移除开头和结尾的空白字符
                jsonData = jsonData.Trim();

                // 替换undefined为null
                jsonData = Regex.Replace(jsonData, @"\bundefined\b", "null");

                // 查找并移除可能的JavaScript代码
                var patterns = new[]
                {
                    @";null\b",
                    @";window\.",
                    @";document\.",
                    @";\s*\(function",
                    @";\s*function\s+",
                    @";\s*var\s+",
                    @";\s*let\s+",
                    @";\s*const\s+"
                };

                foreach (var pattern in patterns)
                {
                    var match = Regex.Match(jsonData, pattern);
                    if (match.Success)
                    {
                        jsonData = jsonData.Substring(0, match.Index);
                        break;
                    }
                }

                // 确保JSON以}结尾
                if (!jsonData.EndsWith("}"))
                {
                    var lastBrace = jsonData.LastIndexOf('}');
                    if (lastBrace > 0)
                    {
                        jsonData = jsonData.Substring(0, lastBrace + 1);
                    }
                }

                return jsonData;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理JSON数据失败: {ex.Message}");
                return jsonData;
            }
        }
    }
}
